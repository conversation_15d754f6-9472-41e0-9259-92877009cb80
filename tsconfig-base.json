{
  "compilerOptions": {
    /* Projects */
    "composite": true,                                   /* Enable constraints that allow a TypeScript project to be used with project references. */

    /* Language and Environment */
    "target": "ES2023",                                  /* Specify ECMAScript target version. */

    /* Modules */
    "module": "NodeNext",                                /* Specify module code generation. */
    "moduleDetection": "force",                          /* Specify module detection strategy. */
    "paths": {                                           /* Specify a set of entries that re-map imports to additional lookup locations. */
      "cpesc-shared": ["./shared/src"]
    },

    /* Emit */
    "declaration": true,                                 /* Generates corresponding '.d.ts' file. */
    "sourceMap": true,                                   /* Generates corresponding '.map' file. */
    "removeComments": true,                              /* Disable emitting comments. */

    /* Interop Constraints */
    "esModuleInterop": true,                             /* Enables emit interoperability between CommonJS and ES Modules. */
    "forceConsistentCasingInFileNames": true,            /* Disallow inconsistently-cased references to the same file. */

    /* Type Checking */
    "strict": true,                                      /* Enable all strict type-checking options. */
    "noImplicitAny": true,                               /* Enable error reporting for expressions and declarations with an implied 'any' type. */
    "noImplicitThis": true,                              /* Enable error reporting when 'this' is given the type 'any'. */
    "useUnknownInCatchVariables": true,                  /* Default catch clause variables as 'unknown' instead of 'any'. */
    "alwaysStrict": true,                                /* Ensure 'use strict' is always emitted. */
    "noUnusedLocals": true,                              /* Enable error reporting when local variables aren't read. */
    "noImplicitReturns": true,                           /* Enable error reporting for codepaths that do not explicitly return in a function. */
    "noFallthroughCasesInSwitch": true,                  /* Enable error reporting for fallthrough cases in switch statements. */
    "noImplicitOverride": true,                          /* Ensure overriding members in derived classes are marked with an override modifier. */
    "noUncheckedSideEffectImports": true,                /* Ensure imports are checked for side effects before marking them as unused. */
    "skipLibCheck": true                                 /* Skip type checking of all declaration files (*.d.ts). */
  }
}
