{"name": "cpesc-frontend", "private": true, "version": "2.0.0", "type": "module", "scripts": {"start": "npm run start:dev", "start:dev": "npm run copy-env:des && vite", "start:hom": "npm run copy-env:hom && vite", "build": "tsc -b && vite build", "build:dev": "npm run copy-env:des && npm run build", "build:hom": "npm run copy-env:hom && npm run build", "copy-env:des": "cp src/env.dev.ts src/env.ts", "copy-env:hom": "cp src/env.hom.ts src/env.ts", "preview": "vite preview"}, "dependencies": {"@react-pdf/renderer": "^4.3.1", "@tanstack/react-query": "^5.90.2", "chart.js": "^4.4.0", "classnames": "^2.5.1", "framer-motion": "^12.23.22", "js-cookie": "^3.0.5", "primeflex": "^4.0.0", "primeicons": "^7.0.0", "primereact": "^10.9.7", "qrcode": "^1.5.4", "react": "^19.1.1", "react-dom": "^19.1.1", "react-error-boundary": "^6.0.0", "react-number-format": "^5.4.4", "react-router-dom": "^7.9.3", "zod": "^4.1.11", "zustand": "^5.0.8"}, "devDependencies": {"@types/express-serve-static-core": "^5.0.7", "@types/js-cookie": "^3.0.6", "@types/qrcode": "^1.5.5", "@types/react": "^19.1.16", "@types/react-dom": "^19.1.9", "@vitejs/plugin-react-swc": "^4.1.0", "sass": "^1.93.2", "typescript": "^5.9.3", "vite": "^7.1.7", "vite-plugin-checker": "^0.11.0"}}