@use "@/variables";

.label {
  display: grid;
  gap: 1em;
  grid-template-columns: 16% 1fr;
  align-items: center;
  margin-bottom: 1em;
  & > span {
    font-weight: bold;
    justify-self: flex-end;
    text-align: end;
  }

  @media (max-width: variables.$limite-largura-tela-mobile-up) {
    grid-template-columns: 1fr;
    gap: 0.25em;
    & > span {
      justify-self: flex-start;
    }
  }
}

.tituloTabela {
  margin: 0;
}

.tabela {
  margin: 1rem 0;
}

.tabela :global(.p-datatable-tbody > tr > td:last-child),
.tabela :global(.p-datatable-thead > tr > th:last-child) {
  display: none;
}

.combo {
  & li {
    & span {
      text-wrap: auto;
    }
  }
}

.botao {
  padding: 0.5rem 1rem;
  width: fit-content;
}
