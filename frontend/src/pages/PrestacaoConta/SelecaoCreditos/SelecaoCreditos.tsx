import Breadcrumb from "@/components/Breadcrumb/Breadcrumb";
import ContainerQuery from "@/components/ContainerQuery";
import SpinnerCpesc from "@/components/SpinnerCpesc";
import TextoErro from "@/components/TextoErro";
import { useAuth } from "@/contexts/auth/useAuth";
import { chamarApi } from "@/helpers/api";
import { DescricaoSituacaoFimLimiteCartao, SeveridadeSituacaoFimLimiteCartao } from "@/helpers/constantes";
import { formatarData, formatarMoeda } from "@/helpers/formatacao";
import { useQueryListaUgUsuario } from "@/hooks/useQueryListaUgUsuario";
import telaPadrao from "@/tela-padrao.module.scss";
import { useQuery } from "@tanstack/react-query";
import { type CreditoCartao, Perfil, SituacaoFimLimiteCartao } from "cpesc-shared";
import { endpoints } from "cpesc-shared/out/endpoints/main";
import { Button } from "primereact/button";
import { Column, type ColumnBodyOptions } from "primereact/column";
import { DataTable } from "primereact/datatable";
import { Dropdown } from "primereact/dropdown";
import { Message } from "primereact/message";
import { Tag } from "primereact/tag";
import React, { type ReactNode, useEffect, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import estilos from "./SelecaoCreditos.module.scss";

export default function SelecaoCreditos() {
  const { usuario } = useAuth();

  const perfil = usuario.perfil;
  const possuiPerfilPortador = perfil === Perfil.Portador;

  const [searchParams, setSearchParams] = useSearchParams();
  const urlUG = searchParams.get("ug");
  const [ugSelecionada, setUgSelecionada] = useState(urlUG !== null && !possuiPerfilPortador ? parseInt(urlUG) : null);
  const urlPortador = searchParams.get("portador");
  const [portadorSelecionado, setPortadorSelecionado] = useState(
    urlPortador !== null && !possuiPerfilPortador ? parseInt(urlPortador) : null,
  );
  const urlUA = searchParams.get("ua");
  const [uaSelecionada, setUaSelecionada] = useState(urlUA !== null && !possuiPerfilPortador ? parseInt(urlUA) : null);

  const { data, isPending, error } = useQuery({
    queryKey: ["SelecaoCreditos", usuario.id, portadorSelecionado, uaSelecionada],
    queryFn: () => {
      if (uaSelecionada !== null) {
        return chamarApi(endpoints.buscarSelecaoCreditoPorPortadorUA, {
          idPortador: portadorSelecionado?.toString() ?? "",
          idUa: uaSelecionada.toString(),
        });
      } else if (possuiPerfilPortador) {
        return chamarApi(endpoints.buscarSelecaoCreditoPorPortador, { id: usuario.id.toString() });
      } else {
        return [];
      }
    },
  });

  const listaUgPortador = [
    ...new Set(data?.map(d => d.cartao.portadorUnidadeAdministrativa.unidadeAdministrativa.unidadeGestora.id)),
  ];

  const exibirResultados = (possuiPerfilPortador && listaUgPortador.length == 1) || uaSelecionada !== null;

  const tratarUgSelecionada = (ug: number) => {
    setSearchParams({ ug: ug.toString() });
    setUgSelecionada(ug);
    setPortadorSelecionado(null);
    setUaSelecionada(null);
  };

  const tratarPortadorSelecionado = (portador: number) => {
    setSearchParams({ ug: ugSelecionada?.toString() ?? "", portador: portador.toString() });
    setPortadorSelecionado(
      possuiPerfilPortador && data ? data[0].cartao.portadorUnidadeAdministrativa.portador.id : portador,
    );
    setUaSelecionada(null);
  };

  const tratarUaSelecionada = (ua: number) => {
    setSearchParams({
      ug: ugSelecionada?.toString() ?? "",
      portador: portadorSelecionado?.toString() ?? "",
      ua: ua.toString(),
    });
    setUaSelecionada(ua);
  };

  return (
    <main className={telaPadrao.container}>
      <section className={telaPadrao.tituloTela}>
        <h1>Demonstrativo para Prestação de Contas</h1>
        <Breadcrumb />
      </section>
      <section>
        <div className={telaPadrao.espaco} />
        <p>
          Esta consulta apresenta os gastos efetuados pelo <b>portador do cartão</b> para o <b>crédito selecionado</b>,
          para fins de prestação de contas.
        </p>
        <div className={telaPadrao.espaco} />
        <div>
          <div className={telaPadrao.espaco} />
          <Campo titulo="Unidade Gestora:">
            <ComboUG ug={ugSelecionada} onSelecaoUG={tratarUgSelecionada} />
          </Campo>
        </div>

        {ugSelecionada && (
          <div>
            <Campo titulo="Portador:">
              <ComboPortador
                portador={portadorSelecionado}
                ug={ugSelecionada}
                onSelecaoPortador={tratarPortadorSelecionado}
              />
            </Campo>

            {portadorSelecionado && (
              <Campo titulo="Unidade Administrativa:">
                <ComboUA
                  ua={uaSelecionada}
                  portador={portadorSelecionado}
                  ug={ugSelecionada}
                  onSelecaoUa={tratarUaSelecionada}
                />
              </Campo>
            )}
          </div>
        )}

        {exibirResultados && (
          <div>
            <div className={telaPadrao.espaco} />
            <ContainerQuery
              carregando={isPending}
              fallbackCarregamento={<SpinnerCpesc />}
              erro={error}
              fallbackErro={<TextoErro mensagem="Falha ao obter dados" />}
            >
              <h2 className={estilos.tituloTabela}>Seleção de Créditos</h2>
              <hr />
              <TabelaCreditos dados={data ?? []} />
            </ContainerQuery>
          </div>
        )}
        <div className={telaPadrao.espaco} />
      </section>
    </main>
  );
}

function Campo(props: { titulo: string; children: React.ReactNode }) {
  return (
    <label className={estilos.label}>
      <span>{props.titulo}</span>
      {props.children}
    </label>
  );
}

interface Opcao {
  descricao: string;
  codigo: number;
}

const ptCombo = {
  panel: {
    className: estilos.combo,
  },
};

function ComboUG(props: { ug: number | null; onSelecaoUG: (ug: number) => void }) {
  const query = useQueryListaUgUsuario();
  const { onSelecaoUG } = props;

  const opcoes =
    query.data?.map((ug): Opcao => {
      return { descricao: ug.nome + " (" + ug.descricao + ")" + " - " + ug.codigo, codigo: ug.id };
    }) ?? [];

  const opcaoSelecionada = opcoes.find(opcao => opcao.codigo === props.ug);

  useEffect(() => {
    if (query.data && query.data.length === 1) {
      onSelecaoUG(query.data[0].id);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [query.data]);

  return (
    <ContainerQuery
      carregando={query.isPending}
      fallbackCarregamento={<SpinnerCpesc />}
      erro={query.error}
      fallbackErro={<TextoErro mensagem="Falha ao obter dados" />}
    >
      <Dropdown
        value={opcaoSelecionada}
        onChange={e => props.onSelecaoUG((e.value as Opcao).codigo)}
        options={opcoes}
        optionLabel="descricao"
        placeholder="Selecione uma unidade gestora"
        filter
        className="p-inputtext-sm"
        id="ddug"
        pt={ptCombo}
      />
    </ContainerQuery>
  );
}

function ComboPortador(props: { portador: number | null; ug: number; onSelecaoPortador: (portador: number) => void }) {
  const { data, isPending, error } = useQuery({
    queryKey: [endpoints.buscarPortadoresPorUG.uri, props.ug],
    queryFn: () => chamarApi(endpoints.buscarPortadoresPorUG, { id: props.ug.toString() }),
  });

  const { usuario } = useAuth();
  const { onSelecaoPortador } = props;

  const opcoes =
    data?.map((portador): Opcao => {
      return { descricao: portador.nome + " - " + portador.cpfOfuscado, codigo: portador.id };
    }) ?? [];

  const opcaoSelecionada = opcoes.find(opcao => opcao.codigo === props.portador);

  useEffect(() => {
    if (data && data.length === 1) {
      onSelecaoPortador(data[0].id);
    } else if (usuario.perfil === Perfil.Portador && data?.find(d => d.id === usuario.id)) {
      onSelecaoPortador(usuario.id);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  return (
    <ContainerQuery
      carregando={isPending}
      fallbackCarregamento={<SpinnerCpesc />}
      erro={error}
      fallbackErro={<TextoErro mensagem="Falha ao obter dados" />}
    >
      <Dropdown
        options={opcoes}
        optionLabel="descricao"
        value={opcaoSelecionada}
        onChange={e => props.onSelecaoPortador((e.value as Opcao).codigo)}
        placeholder="Selecione um portador de cartão"
        filter
        className="p-inputtext-sm"
        id="ddportador"
        emptyMessage="Nenhum portador encontrado"
        pt={ptCombo}
      />
    </ContainerQuery>
  );
}

function ComboUA(props: { ua: number | null; portador: number; ug: number; onSelecaoUa: (ua: number) => void }) {
  const { data, isPending, error } = useQuery({
    queryKey: [endpoints.buscarUAsPorPortadorUG.uri, props.portador, props.ug],
    queryFn: () =>
      chamarApi(endpoints.buscarUAsPorPortadorUG, { idPortador: props.portador.toString(), idUg: props.ug.toString() }),
  });

  const { onSelecaoUa } = props;

  const opcoes =
    data?.map((ua): Opcao => {
      return { descricao: ua.municipio.nomeMunicipio + " - " + ua.nome, codigo: ua.id };
    }) ?? [];

  const opcaoSelecionada = opcoes.find(opcao => opcao.codigo === props.ua);

  useEffect(() => {
    if (data && data.length === 1) {
      onSelecaoUa(data[0].id);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  return (
    <ContainerQuery
      carregando={isPending}
      fallbackCarregamento={<SpinnerCpesc />}
      erro={error}
      fallbackErro={<TextoErro mensagem="Falha ao obter dados" />}
    >
      <Dropdown
        options={opcoes}
        optionLabel="descricao"
        value={opcaoSelecionada}
        onChange={e => onSelecaoUa((e.value as Opcao).codigo)}
        placeholder="Selecione um portador de cartão"
        filter
        className="p-inputtext-sm"
        id="ddua"
        autoOptionFocus
        pt={ptCombo}
      />
    </ContainerQuery>
  );
}

function TabelaCreditos(props: { dados: CreditoCartao[] }) {
  interface ColunasMeta {
    chave: string;
    campo: string;
    cabecalho: string;
    corpo: ReactNode | ((data: CreditoCartao, options: ColumnBodyOptions) => React.ReactNode);
  }
  const creditos = props.dados;
  const [creditoSelecionado, setCreditoSelecionado] = useState<CreditoCartao | null>(null);

  const navigate = useNavigate();

  const navegarExtrato = (creditoId: number) => {
    void navigate(`/prestacao-contas/extrato/${creditoId}`);
  };

  const tratarSelecaoLinha = (e: CreditoCartao) => {
    setCreditoSelecionado(e);
    navegarExtrato(e.id);
  };

  const colunas: ColunasMeta[] = [
    {
      chave: "dataCredito",
      campo: "dataCredito",
      cabecalho: "Data de Crédito",
      corpo: (registro: CreditoCartao) => formatarData(registro.dataCredito),
    },
    {
      chave: "dataLimite",
      campo: "dataLimite",
      cabecalho: "Data Limite",
      corpo: (registro: CreditoCartao) => formatarData(registro.dataVencimento),
    },
    {
      chave: "subelemento",
      campo: "subelemento.nome",
      cabecalho: "Tipo de Gasto",
      corpo: (registro: CreditoCartao) => registro.subelemento.nome,
    },
    {
      chave: "valorCredito",
      campo: "valorCredito",
      cabecalho: "Valor",
      corpo: (registro: CreditoCartao) => formatarMoeda(registro.valorCredito),
    },
    {
      chave: "notalancamento",
      campo: "notalancamento",
      cabecalho: "Nota Lançamento",
      corpo: (registro: CreditoCartao) => registro.notaLancamento,
    },
    {
      chave: "situacao",
      campo: "situacao",
      cabecalho: "Situação",
      corpo: (registro: CreditoCartao) => {
        const situacao = registro.fimLimiteCartao
          ? registro.fimLimiteCartao.situacao
          : SituacaoFimLimiteCartao.Pendente;

        return (
          <Tag
            value={DescricaoSituacaoFimLimiteCartao[situacao]}
            severity={SeveridadeSituacaoFimLimiteCartao[situacao]}
          />
        );
      },
    },
    {
      chave: "acao",
      campo: "acao",
      cabecalho: "",
      corpo: (registro: CreditoCartao) => {
        return (
          <Button
            label="Consultar"
            icon="pi pi-search"
            severity="success"
            className={`${estilos.botao} ${telaPadrao.botaoTabela}`}
            size="small"
            onClick={() => navegarExtrato(registro.id)}
          />
        );
      },
    },
  ];

  return creditos.length > 0 ? (
    <>
      <div className={telaPadrao.espaco} />
      <p>
        Clique em uma linha abaixo para selecionar um crédito, para exibir o demonstrativo para prestação de contas.
      </p>
      <div className={telaPadrao.espaco} />
      <div className={estilos.tabela}>
        <DataTable
          value={creditos}
          selection={creditoSelecionado}
          selectionMode="single"
          onSelectionChange={e => tratarSelecaoLinha(e.value as CreditoCartao)}
          dataKey="id"
          className={estilos.tabela}
          responsiveLayout="stack"
          breakpoint="640px"
        >
          {colunas.map(col => (
            <Column key={col.chave} field={col.campo} header={col.cabecalho} body={col.corpo} />
          ))}
        </DataTable>
      </div>
    </>
  ) : (
    <Message
      severity="warn"
      text="Atenção: Nenhum crédito encontrado para portador selecionado. Verifique a busca realizada e tente novamente."
      className="w-full justify-content-start"
    />
  );
}
