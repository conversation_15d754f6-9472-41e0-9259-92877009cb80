import { formatarData } from "@/helpers/formatacao";
import type { Credito, CreditoCartao } from "cpesc-shared";
import { Column, type ColumnProps } from "primereact/column";
import { DataTable } from "primereact/datatable";
import estilo from "../ExtratoPrestacaoConta.module.scss";

interface ColunasMeta extends ColumnProps {
  key: string;
}

export function TabelaCredito({ credito }: { credito: CreditoCartao | undefined }) {
  if (!credito) {
    return null;
  }

  const colunas: ColunasMeta[] = [
    { key: "dataCredito", header: "Data Crédito", body: (rowData: Credito) => formatarData(rowData.dataCredito) },
    {
      key: "dataLimite",
      header: "Data Limite",
      body: (rowData: Credito) => formatarData(rowData.dataLimiteMovimentacao),
    },
    { key: "notaEmpenho", header: "Nota Empenho", body: (rowData: Credito) => rowData.notaEmpenho },
    { key: "notaLancamento", header: "Nota Lançamento", body: (rowData: Credito) => rowData.notaLancamento },
    { key: "ordemBancaria", header: "Ordem Bancária", body: (rowData: Credito) => rowData.ordemBancaria },
    { key: "fonteRecurso", header: "Fonte Recurso", body: (rowData: Credito) => rowData.fonteRecurso },
    { key: "subElemento", header: "Tipo de Gasto", body: (rowData: Credito) => rowData.subelemento.nome },
  ];

  const creditos = [credito];

  return (
    <>
      <div className={estilo.linha}>
        <h2>Crédito</h2>
      </div>
      <hr />
      <DataTable value={creditos} responsiveLayout="stack" breakpoint="768px" className={estilo.tabela}>
        {colunas.map(col => (
          <Column key={col.key} body={col.body} header={col.header} />
        ))}
      </DataTable>
    </>
  );
}

export default TabelaCredito;
