import SpinnerCpesc from "@/components/SpinnerCpesc";
import Tabel<PERSON> from "@/components/Tabela/Tabela";
import { useAuth } from "@/contexts/auth/useAuth";
import { chamarApi } from "@/helpers/api";
import { opcoesDocumentoFiscal } from "@/helpers/constantes";
import { aplicarMascaraCNPJ, formatarData, formatarDataHora, formatarMoeda } from "@/helpers/formatacao";
import { useExtratoPrestacaoContaStore } from "@/stores/ExtratoPrestacaoContaStore";
import telaPadrao from "@/tela-padrao.module.scss";
import { useMutation } from "@tanstack/react-query";
import classNames from "classnames";
import {
  type DocumentoFiscal,
  type DocumentoFiscalItem,
  type Movimentacao,
  SituacaoFimLimiteCartao,
  TipoCompra,
  TipoDocumentoFiscal,
  tiposCompraPermitida,
} from "cpesc-shared";
import { endpoints } from "cpesc-shared/out/endpoints/main";
import type { PayloadCriarDocFiscal, PayloadCriarReversao } from "cpesc-shared/src/endpoints/documento-fiscal";
import { Button } from "primereact/button";
import { Checkbox } from "primereact/checkbox";
import { Column, type ColumnProps } from "primereact/column";
import { confirmDialog } from "primereact/confirmdialog";
import { DataTable } from "primereact/datatable";
import { Dropdown, type DropdownChangeEvent } from "primereact/dropdown";
import { Tag } from "primereact/tag";
import { useState } from "react";
import AdicaoDocumentoFiscalImportacao from "../AdicaoDocumentoFiscalImportacao/AdicaoDocumentoFiscalImportacao";
import AdicaoDocumentoFiscalManual from "../AdicaoDocumentoFiscalManual/AdicaoDocumentoFiscalManual";
import estilo from "../ExtratoPrestacaoConta.module.scss";
import VincularReversao from "../Vincular-Reversao/Vincular-Reversao";

export interface NotaByData {
  data: string;
  valor: number;
  cnpj: string;
}

interface ColunasMeta extends ColumnProps {
  key: string;
}

interface TabelaGastosProps {
  gastos: Movimentacao[] | undefined;
  situacaoFimLimite: SituacaoFimLimiteCartao;
  onVincularNotaImportacao: (nota: PayloadCriarDocFiscal) => Promise<Movimentacao | null>;
  onCancelarEdicaoNotaImportacao: () => void;
  onCriarNotaManual: (nota: PayloadCriarDocFiscal) => void;
  onAlterarNota: (nota: DocumentoFiscal) => void;
  onExcluirNota: () => void;
  onVincularReversao: (reversao: PayloadCriarReversao) => void;
  onExcluirReversao: () => void;
}

interface DadosLinhaTabela extends Movimentacao {
  selecionado: boolean;
  atualizando: boolean;
}

export interface OperacaoTabelaGastos {
  tipoOperacao: TipoOperacaoTabelaGastos;
  tipoNota: TipoDocumentoFiscal;
}

export enum TipoOperacaoTabelaGastos {
  AdicionarManual,
  EditarManual,
  AdicionarImportacao,
  EditarImportacao,
  VincularReversao,
}

export function TabelaGastos(props: TabelaGastosProps) {
  const { usuario } = useAuth();
  const [movimentacaoSelecionada, setMovimentacaoSelecionada] = useState<Movimentacao | null>(null);
  const [movimentacoesReversao, setMovimentacoesReversao] = useState<Movimentacao[] | null>(null);
  const [operacao, setOperacao] = useState<OperacaoTabelaGastos | null>(null);
  const gastosSelecionados = useExtratoPrestacaoContaStore(state => state.gastosSelecionados);
  const setSelecaoGasto = useExtratoPrestacaoContaStore(state => state.setSelecaoGasto);
  const [gastoExpandidoId, setGastoExpandidoId] = useState<number | null>(null);
  const reversoesVinculadas = new Set<number>();

  const queryExclusaoNota = useMutation({
    mutationFn: async (idMovimentacao: number) => {
      const response = await chamarApi(endpoints.excluirVinculo, { id: idMovimentacao.toString() }, undefined);

      if (!response.sucesso) {
        throw new Error(`Erro ao remover o Documento Fiscal: ${response.mensagem}`);
      }

      setSelecaoGasto(idMovimentacao, false);
      props.onExcluirNota();
    },
    onError: error => {
      console.error(error);
    },
  });

  const queryExclusaoReversao = useMutation({
    mutationFn: async (idReversao: number) => {
      const response = await chamarApi(endpoints.excluirReversao, { id: idReversao.toString() }, undefined);

      if (!response.sucesso) {
        throw new Error(`Erro ao remover a vínculo da reversao do Documento Fiscal: ${response.mensagem}`);
      }

      setMovimentacoesReversao(null);
      props.onExcluirReversao();
    },
    onError: error => {
      console.error(error);
    },
  });

  // Função para selecionar todos
  const handleSelectAll = () => {
    if (props.situacaoFimLimite === SituacaoFimLimiteCartao.Finalizado) {
      return;
    }

    const movimentacoesComNota = props.gastos?.filter(gasto => gasto.documentoFiscal) ?? [];
    const movimentacoesComNotaSelecionados = movimentacoesComNota.filter(gasto => gastosSelecionados.has(gasto.id));
    const algunsEstaoSelecionados = movimentacoesComNotaSelecionados.length > 0;

    for (const gasto of movimentacoesComNota) {
      if (gasto.id && gasto.valorTransacaoReal === gasto.documentoFiscal?.valor) {
        setSelecaoGasto(gasto.id, !algunsEstaoSelecionados);
      }
    }
  };

  if (!props.gastos) {
    return null;
  }

  const tratarSelecaoNotaImportacao = async (documentoFiscal: PayloadCriarDocFiscal) => {
    if (movimentacaoSelecionada !== null) {
      const movimentacaoImportada: Movimentacao | null = await props.onVincularNotaImportacao(documentoFiscal);
      if (movimentacaoImportada !== null) {
        editarNota(movimentacaoImportada);
      }
    }
  };

  const tratarVincularReversao = (movimentacaoId: number, movimentacaoReversao: Movimentacao) => {
    setMovimentacoesReversao(null);
    const reversao: PayloadCriarReversao = {
      movimentacaoId,
      numero: movimentacaoReversao.id,
      dataEmissao: movimentacaoReversao.dataTransacao,
    };
    props.onVincularReversao(reversao);
  };

  const tratarCriacaoNotaManual = (nota: PayloadCriarDocFiscal) => {
    if (movimentacaoSelecionada !== null) {
      setOperacao(null);
      props.onCriarNotaManual(nota);
    }
  };

  const tratarAlteracaoNota = (nota: DocumentoFiscal) => {
    if (movimentacaoSelecionada !== null) {
      setOperacao(null);
      props.onAlterarNota(nota);
    }
  };

  const tratarFecharEdicao = (operacao: OperacaoTabelaGastos) => {
    if (operacao.tipoOperacao === TipoOperacaoTabelaGastos.EditarImportacao) {
      props.onCancelarEdicaoNotaImportacao();
    }
    setOperacao(null);
    setMovimentacaoSelecionada(null);
  };

  const handleVincularNota = (tipoNota: TipoDocumentoFiscal, movimentacao: Movimentacao) => {
    setMovimentacaoSelecionada(movimentacao);

    if (tipoNota === TipoDocumentoFiscal.reversao) {
      const valorParaReversao = movimentacao.valorTransacaoReal;
      const dataParaReversao = movimentacao.dataTransacao;
      const movRev = props.gastos?.filter(
        d =>
          d.codigoTransacaoBB === 253600 &&
          d.valorTransacaoReal * -1 === valorParaReversao &&
          d.dataTransacao >= dataParaReversao &&
          !reversoesVinculadas.has(d.id),
      );
      setMovimentacoesReversao(movRev ?? null);
      setOperacao({ tipoOperacao: TipoOperacaoTabelaGastos.VincularReversao, tipoNota });
      return;
    }

    if (tipoNota === TipoDocumentoFiscal.notaFiscalEletronicaImportacao) {
      setOperacao({ tipoOperacao: TipoOperacaoTabelaGastos.AdicionarImportacao, tipoNota });
    } else {
      setOperacao({ tipoOperacao: TipoOperacaoTabelaGastos.AdicionarManual, tipoNota });
    }
  };

  const excluirNota = (movimentacao: Movimentacao) => {
    queryExclusaoNota.mutate(movimentacao.id);
  };

  const excluirReversao = (idMovimentacao: number) => {
    queryExclusaoReversao.mutate(idMovimentacao);
  };

  const editarNota = (movimentacao: Movimentacao | null) => {
    if (movimentacao === null) {
      return;
    }

    if (movimentacao.documentoFiscal === null) {
      return;
    }

    setMovimentacaoSelecionada(movimentacao);
    const tipoNota = movimentacao.documentoFiscal.tipodocumentofiscalId;

    if (tipoNota === TipoDocumentoFiscal.notaFiscalEletronicaImportacao) {
      setOperacao({ tipoOperacao: TipoOperacaoTabelaGastos.EditarImportacao, tipoNota });
    } else {
      setOperacao({ tipoOperacao: TipoOperacaoTabelaGastos.EditarManual, tipoNota });
    }
  };

  const tipoNotaTemplate = (rowData: Movimentacao) => {
    if (rowData.documentoFiscal === null) {
      return "-";
    }

    const tipoNota = rowData.documentoFiscal.tipodocumentofiscalId;

    const getTipoNota = () => {
      if (tipoNota == TipoDocumentoFiscal.notaFiscalEletronicaImportacao) {
        return { tipoTitle: "Nota Fiscal Eletrônica Importada Automática", tipoString: "NF-e", tipoLabel: "A" };
      } else if (tipoNota == TipoDocumentoFiscal.notaFiscalEletronicaManual) {
        return { tipoTitle: "Nota Fiscal Eletrônica Digitada Manual", tipoString: "NF-e", tipoLabel: "M" };
      } else if (tipoNota == TipoDocumentoFiscal.notaServicoManual) {
        return { tipoTitle: "Nota Fiscal de Serviço Digitada Manual", tipoString: "NF-s", tipoLabel: "M" };
      } else if (tipoNota == TipoDocumentoFiscal.cupomFiscalManual) {
        return { tipoTitle: "Cupom Fiscal Digitado Manual", tipoString: "CF", tipoLabel: "M" };
      } else if (tipoNota == TipoDocumentoFiscal.reversao) {
        return { tipoTitle: "Documento Fiscal Revertido", tipoString: "Revertida", tipoLabel: "R" };
      } else {
        return { tipoTitle: "Erro na identificação do Documento", tipoString: "Erro", tipoLabel: "E" };
      }
    };

    const { tipoTitle, tipoString, tipoLabel } = getTipoNota();

    return (
      <>
        <span title={tipoTitle}>{tipoString}</span>
        <Tag
          className="ml-2"
          severity={tipoLabel == "A" ? "success" : tipoLabel == "R" ? "info" : "warning"}
          value={tipoLabel}
        />
      </>
    );
  };

  const documentoFiscalTemplate = (rowData: DadosLinhaTabela) => {
    const templateSelecaoNota = () => {
      const ptDropDown = {
        panel: {
          className: estilo.dropDown,
        },
        input: {
          className: estilo.dropDownInput,
        },
        list: {
          className: estilo.dropDown,
        },
      };
      return (
        <Dropdown
          value={null}
          onChange={(e: DropdownChangeEvent) => handleVincularNota(e.value as TipoDocumentoFiscal, rowData)}
          options={opcoesDocumentoFiscal}
          optionLabel="descricao"
          optionValue="codigo"
          placeholder="Vincular Documento Fiscal"
          title="Vincular Documento Fiscal ou Reversão"
          pt={ptDropDown}
        />
      );
    };

    const reversaoTemplate = () => {
      return (
        rowData.documentoFiscal?.tipodocumentofiscalId === TipoDocumentoFiscal.reversao && (
          <div className={`card  ${estilo.containerChevron}`}>
            <span>Data da Reversão : {formatarDataHora(rowData.documentoFiscal.dataEmissao ?? "")}</span>
            {props.situacaoFimLimite !== SituacaoFimLimiteCartao.Finalizado && (
              <Button
                onClick={() => {
                  confirmDialog({
                    message: "Você tem certeza que deseja remover o vínculo com a Reversão deste Documento Fiscal?",
                    header: "Confirmação",
                    icon: "pi pi-exclamation-triangle",
                    acceptLabel: "Excluir",
                    rejectLabel: "Cancelar",
                    acceptClassName: telaPadrao.botaoPrincipal,
                    rejectClassName: estilo.botaoCancelar,
                    accept: () => {
                      if (rowData.documentoFiscal?.tipodocumentofiscalId === TipoDocumentoFiscal.reversao) {
                        excluirReversao(rowData.documentoFiscal.id);
                      }
                    },
                  });
                }}
                title="Remover vínculo com a Reversão deste Documento Fiscal"
              >
                <i className="pi pi-trash" />
              </Button>
            )}
          </div>
        )
      );
    };

    if (rowData.atualizando) {
      return <SpinnerCpesc style={{ width: "24px", height: "24px" }} />;
    }

    if (rowData.documentoFiscal?.tipodocumentofiscalId === TipoDocumentoFiscal.reversao) {
      return reversaoTemplate();
    }

    if (rowData.codigoTransacaoBB === 253600) {
      if (reversoesVinculadas.has(rowData.id)) {
        return <b>REVERSÃO DE PAGAMENTO (vinculada)</b>;
      }
      return <>REVERSÃO DE PAGAMENTO</>;
    }

    if (!rowData.documentoFiscal && props.situacaoFimLimite === SituacaoFimLimiteCartao.Finalizado) {
      return <i className="pi pi-ban" title="Documento Fiscal não encontrada." />;
    }

    if (!rowData.documentoFiscal) {
      return templateSelecaoNota();
    }

    const expandido = gastoExpandidoId === rowData.id;

    return (
      <div className={estilo.containerChevron}>
        <Button
          link
          onClick={() => {
            setGastoExpandidoId(expandido ? null : rowData.id);
          }}
        >
          <i className={`pi ${expandido ? "pi-chevron-up" : "pi-chevron-down"}`} />
          {rowData.documentoFiscal.numero}-{rowData.documentoFiscal.serie}
        </Button>
        {props.situacaoFimLimite !== SituacaoFimLimiteCartao.Finalizado && (
          <>
            <Button
              onClick={() => {
                confirmDialog({
                  message: "Você tem certeza que deseja remover o vínculo com o Documento Fiscal?",
                  header: "Confirmação",
                  icon: "pi pi-exclamation-triangle",
                  acceptLabel: "Excluir",
                  rejectLabel: "Cancelar",
                  acceptClassName: telaPadrao.botaoPrincipal,
                  rejectClassName: estilo.botaoCancelar,
                  accept: () => excluirNota(rowData),
                });
              }}
              title="Remover vínculo com o Documento Fiscal"
            >
              <i className="pi pi-trash" />
            </Button>
            <Button onClick={() => editarNota(rowData)} title={"Editar o vínculo com o Documento Fiscal"}>
              <i className="pi pi-pencil" />
            </Button>
            {rowData.documentoFiscal.valor !== rowData.valorTransacaoReal && (
              <Button
                title="Este Documento Fiscal apresenta valor diferente do Gasto"
                className={estilo.botaoNaTabelaComErro}
              >
                <i className="pi pi-exclamation-triangle" />
              </Button>
            )}
          </>
        )}
      </div>
    );
  };

  const tipoCompraTemplate = (rowData: DocumentoFiscalItem) => {
    const tipoCompraAtual = tiposCompraPermitida.find(tipo => tipo.key === rowData.tipocompraId);
    return (
      <div className="flex align-items-center gap-1" style={{ width: "10rem" }}>
        <Tag
          className={estilo.margemDireita}
          severity={
            tipoCompraAtual?.key == TipoCompra.padrao
              ? "success"
              : tipoCompraAtual?.key == TipoCompra.emergencial
                ? "info"
                : "warning"
          }
          value={tipoCompraAtual?.label ?? "P"}
        />
        <div>{tipoCompraAtual?.name ?? "Padrão"}</div>
        {tipoCompraAtual?.key != TipoCompra.padrao && (
          <div className={estilo.semQuebra}>{rowData.processotipocompra}</div>
        )}
      </div>
    );
  };

  const rowExpansionTemplate = (data: Movimentacao) => {
    if (!data.documentoFiscal?.dataEmissao) return null;

    const rodapeExpandido = () => {
      return (
        <div className={estilo.impostos}>
          {(data.documentoFiscal?.acrescimo ?? 0) > 0 && (
            <>Acréscimo: {formatarMoeda(data.documentoFiscal?.acrescimo ?? 0)} </>
          )}
          {(data.documentoFiscal?.acrescimo ?? 0) > 0 && (data.documentoFiscal?.desconto ?? 0) > 0 && "  |  "}
          {(data.documentoFiscal?.desconto ?? 0) > 0 && (
            <>Desconto: {formatarMoeda(data.documentoFiscal?.desconto ?? 0)} </>
          )}
        </div>
      );
    };
    const colunasNota: ColunasMeta[] = [
      {
        key: "ncm",
        header: "NCM - Descrição",
        body: (item: DocumentoFiscalItem) =>
          item.descricao ? `${item.ncm} - ${item.descricao}` : "NCM não encontrado",
      },
      { key: "quantidade", header: "Quant.", field: "quantidade" },
      { key: "unidade", header: "Uni.", field: "unidade" },
      {
        key: "valorUnitario",
        header: "V. Unitário",
        body: (rowData: DocumentoFiscalItem) => formatarMoeda(rowData.valorunitario),
      },
      {
        key: "valor",
        header: "Valor",
        body: (rowData: DocumentoFiscalItem) => formatarMoeda(rowData.valor),
      },
      {
        key: "tipocompra",
        header: "Tipo de Compra",
        body: (rowData: DocumentoFiscalItem) => tipoCompraTemplate(rowData),
      },
    ];

    return (
      <div className={estilo.documentoFiscalExpandido}>
        <div className={estilo.documentoFiscalHeader}>
          <h3>Documento Fiscal - {data.documentoFiscal.numeroSerie}</h3>
          <Button icon="pi pi-times" rounded text severity="secondary" onClick={() => setGastoExpandidoId(null)} />
        </div>
        <div className={estilo.documentoFiscalInfo}>
          <span>Valor: {formatarMoeda(data.documentoFiscal.valor)}</span>
          <span>Data de emissão: {formatarData(data.documentoFiscal.dataEmissao)}</span>
        </div>
        <DataTable
          value={data.documentoFiscal.documentoFiscalItens ?? []}
          responsiveLayout="stack"
          breakpoint="640px"
          emptyMessage="Nenhum item encontrado neste Documento Fiscal."
          footer={rodapeExpandido}
        >
          {colunasNota.map(col => (
            <Column key={col.key} field={col.field} header={col.header} body={col.body} />
          ))}
        </DataTable>
      </div>
    );
  };

  const checkboxTemplate = (rowData: DadosLinhaTabela) => {
    return (
      <div className="flex lg:justify-content-center justify-content-end">
        <Checkbox
          checked={rowData.selecionado}
          disabled={
            rowData.documentoFiscal === null ||
            props.situacaoFimLimite === SituacaoFimLimiteCartao.Finalizado ||
            rowData.documentoFiscal.valor !== rowData.valorTransacaoReal
          }
          onChange={e => {
            if (rowData.documentoFiscal) {
              setSelecaoGasto(rowData.id, e.checked ?? false);
            }
          }}
        />
      </div>
    );
  };

  const headerCheckboxTemplate = (transpor: boolean) => {
    const movimentacoesComNota = props.gastos?.filter(gasto => gasto.documentoFiscal) ?? [];
    const movimentacoesComNotaSelecionados = movimentacoesComNota.filter(gasto => gastosSelecionados.has(gasto.id));
    const todosEstaoSelecionados = movimentacoesComNota.length === movimentacoesComNotaSelecionados.length;
    const algunsEstaoSelecionados = movimentacoesComNotaSelecionados.length > 0;

    let classButton = "pi pi-circle-off";
    let titleButton = "Todos Desmarcados - clique para selecionar/desmarcar todos";

    if (todosEstaoSelecionados) {
      classButton = "pi pi-check-circle";
      titleButton = "Todos Selecionados - clique para desmarcar/selecionar todos";
    } else if (algunsEstaoSelecionados) {
      classButton = "pi pi-minus-circle";
      titleButton = "Alguns Selecionados - clique para desmarcar/selecionar todos";
    }

    return (
      <div
        className={classNames("flex align-items-center", !transpor && "justify-content-center")}
        style={{ color: "var(--primary-color)" }}
      >
        <i className={classButton} title={titleButton} onClick={handleSelectAll} />
      </div>
    );
  };

  const dadosTabela = props.gastos.map((gasto): DadosLinhaTabela => {
    if (gasto.id === movimentacaoSelecionada?.id) {
      gasto = movimentacaoSelecionada;
    }
    const gastoSelecionado = gastosSelecionados.has(gasto.id);
    if (gasto.documentoFiscal?.tipodocumentofiscalId === TipoDocumentoFiscal.reversao && gasto.documentoFiscal.numero) {
      reversoesVinculadas.add(gasto.documentoFiscal.numero);
    }
    return {
      ...gasto,
      valorTransacaoReal: gasto.valorTransacaoReal,
      selecionado: gastoSelecionado,
      atualizando: queryExclusaoNota.isPending && queryExclusaoNota.variables === gasto.id,
    };
  });

  return (
    <>
      <div className={estilo.linha}>
        <h2>Gastos</h2>
      </div>
      <hr />
      <Tabela
        dados={dadosTabela}
        breakpoint={960}
        linhaExpandida={rowData => gastoExpandidoId === rowData.id && rowExpansionTemplate(rowData)}
        colunas={[
          {
            cabecalho: headerCheckboxTemplate,
            conteudo: checkboxTemplate,
          },
          { cabecalho: "Data Gasto", conteudo: (rowData: DadosLinhaTabela) => formatarData(rowData.dataTransacao) },
          { cabecalho: "Estabelecimento", conteudo: (rowData: DadosLinhaTabela) => rowData.nomeEstabelecimento },
          {
            cabecalho: "CNPJ",
            conteudo: (rowData: DadosLinhaTabela) => aplicarMascaraCNPJ(rowData.CNPJEstabelecimento),
          },
          { cabecalho: "Cidade", conteudo: (rowData: DadosLinhaTabela) => rowData.cidadeEstabelecimento },
          { cabecalho: "UF", conteudo: (rowData: DadosLinhaTabela) => rowData.ufEstabelecimento },
          { cabecalho: "Tipo", conteudo: tipoNotaTemplate },
          { cabecalho: "Documento Fiscal", conteudo: documentoFiscalTemplate },
          { cabecalho: "Valor", conteudo: (rowData: DadosLinhaTabela) => formatarMoeda(rowData.valorTransacaoReal) },
        ]}
        mensagemSemDados="Nenhum gasto encontrado"
      />
      {movimentacaoSelecionada && operacao && (
        <>
          <AdicaoDocumentoFiscalImportacao
            movimentacaoId={movimentacaoSelecionada.id}
            email={usuario.email}
            chave={movimentacaoSelecionada.documentoFiscal?.chave ?? ""}
            operacao={operacao}
            onSelecionar={nota => {
              void tratarSelecaoNotaImportacao(nota);
            }}
            onFechar={() => {
              setMovimentacaoSelecionada(null);
            }}
          />
          <AdicaoDocumentoFiscalManual
            movimentacaoId={movimentacaoSelecionada.id}
            totalMovimentacao={movimentacaoSelecionada.valorTransacaoReal}
            operacao={operacao}
            onCriar={tratarCriacaoNotaManual}
            onAlterar={tratarAlteracaoNota}
            onFechar={tratarFecharEdicao}
          />
          {movimentacoesReversao !== null && (
            <VincularReversao
              movimentacaoId={movimentacaoSelecionada.id}
              movimentacoes={movimentacoesReversao}
              onSelecionar={tratarVincularReversao}
              onFechar={() => setMovimentacoesReversao(null)}
            />
          )}
        </>
      )}
    </>
  );
}
export default TabelaGastos;
