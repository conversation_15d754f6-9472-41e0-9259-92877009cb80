@use "@/variables.scss";

.formContainer {
  display: flex;
  gap: 2rem;
  padding: 1.5rem 0;

  @media only screen and (max-width: variables.$limite-largura-tela-tablet) {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }
}

.formSection {
  display: flex;
  flex-direction: column;
  gap: 1rem;

  &:first-child {
    flex: 0 0 38.2%; // Primeira seção ocupará menor espaço
  }

  &:last-child {
    flex: 1; // Última seção ocupará o espaço restante
  }
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 0.3rem;

  label {
    font-weight: 600;
    font-size: 0.9rem;
    color: var(--cor-texto-destacado);
  }
}

.input {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
  width: 100%;
}

.painelBotao {
  display: flex;
  gap: 10px;
  justify-content: right;
  align-items: center;
  padding-bottom: 1rem;

  [class*="botaoPrincipal"] {
    margin: 0;
  }
}

.mensagemInferior {
  width: 100%;

  :global(.p-message) {
    margin: 1rem;
  }
}

.painel {
  flex-direction: column-reverse;
}

.tabela {
  td {
    &:last-child {
      text-align: right;
      justify-content: flex-end !important;
      flex-wrap: wrap;
      & button {
        @media screen and (max-width: variables.$limite-largura-tela-media-up) {
          margin-right: 0.5rem;
        }
        &:last-child {
          margin-right: 0;
        }
      }
    }
    & button {
      @media screen and (max-width: variables.$limite-largura-tela-tablet) {
        margin: 0 0.5rem 0.5rem 0;
      }
    }
  }
}
