import ContainerQuery from "@/components/ContainerQuery";
import SpinnerCpesc from "@/components/SpinnerCpesc";
import TextoErro from "@/components/TextoErro";
import { chamarApi } from "@/helpers/api";
import { opcoesDocumentoFiscal } from "@/helpers/constantes";
import { aplicarMascaraCNPJ, formatarData, formatarMoeda } from "@/helpers/formatacao";
import telaPadrao from "@/tela-padrao.module.scss";
import { useQuery } from "@tanstack/react-query";
import { type DocumentoFiscal, type DocumentoFiscalSigef, TipoDocumentoFiscal } from "cpesc-shared";
import type { PayloadCriarDocFiscalImportacao } from "cpesc-shared/out/endpoints/documento-fiscal";
import { endpoints } from "cpesc-shared/out/endpoints/main";
import { Button } from "primereact/button";
import { Column } from "primereact/column";
import { DataTable } from "primereact/datatable";
import { Dialog } from "primereact/dialog";
import { Divider } from "primereact/divider";
import { InputMask } from "primereact/inputmask";
import { InputText } from "primereact/inputtext";
import { Message } from "primereact/message";
import { useState } from "react";
import { type OperacaoTabelaGastos, TipoOperacaoTabelaGastos } from "../Tabelas/TabelaGastos";
import estilo from "./AdicaoDocumentoFiscalImportacao.module.scss";

interface AdicaoDocumentoFiscalImportacaoProps {
  movimentacaoId: number;
  email: string;
  chave?: string;
  operacao: OperacaoTabelaGastos;
  onSelecionar: (documentoFiscal: PayloadCriarDocFiscalImportacao) => void;
  onFechar: () => void;
}

export default function AdicaoDocumentoFiscalImportacao(props: AdicaoDocumentoFiscalImportacaoProps) {
  const [buscaPermitida, setBuscaPermitida] = useState<boolean>(false);
  const [parametro, setParametro] = useState<string | undefined>(undefined);
  const [cnpj, setCnpj] = useState<string | undefined>(undefined);
  const [numero, setNumero] = useState<string | undefined>(undefined);
  const [serie, setSerie] = useState<string | undefined>(undefined);
  const [chave, setChave] = useState<string | undefined>(props.chave);
  const [selecionandoNota, setSelecionandoNota] = useState<boolean>(false);

  const { data, isPending, error } = useQuery({
    queryKey: ["/documento-fiscal/buscar-externo/", parametro],
    queryFn: () => chamarApi(endpoints.buscarDocumentosFiscaisExternos, { parametro: parametro ?? "" }),
    enabled: buscaPermitida,
  });

  const colunaGrupoBotoes = (rowData: DocumentoFiscal) => {
    return (
      <>
        {detalharNotaTemplate(rowData)}
        {selecionarNotaTemplate(rowData)}
      </>
    );
  };

  // Botão para selecionar o documento
  const selecionarNotaTemplate = (rowData: DocumentoFiscal) => {
    return <Button label="Selecionar" onClick={() => selecionarNota(rowData)} className={telaPadrao.botaoPrincipal} />;
  };

  // Salva o documento fiscal no banco de dados e passa resultado para o componente pai
  const selecionarNota = (nota: DocumentoFiscal) => {
    setSelecionandoNota(true);
    if (
      nota.chave == null ||
      nota.cnpj === null ||
      nota.codigoSigef == null ||
      nota.dataEmissao === null ||
      nota.numero === null ||
      nota.serie === null ||
      nota.valor === null
    ) {
      throw Error("Dados do documento fiscal incompletos.");
    }

    const payloadCriacao: PayloadCriarDocFiscalImportacao = {
      chave: nota.chave,
      cnpj: nota.cnpj,
      codigoSigef: nota.codigoSigef,
      dataEmissao: nota.dataEmissao,
      movimentacaoId: props.movimentacaoId,
      numero: nota.numero,
      serie: nota.serie,
      tipodocumentofiscalId: TipoDocumentoFiscal.notaFiscalEletronicaImportacao,
      valor: nota.valor,
    };

    props.onSelecionar(payloadCriacao);
  };

  // Botão para detalhar a nota
  const detalharNotaTemplate = (rowData: DocumentoFiscal) => {
    if (rowData.chave) {
      return (
        <Button
          label="Mais detalhes"
          onClick={() => detalharNota(rowData.chave ?? "")}
          className={telaPadrao.botaoSecundario}
        />
      );
    }

    return null;
  };

  //Formatação do CNPJ
  const cnpjTemplate = (rowData: DocumentoFiscal) => {
    return aplicarMascaraCNPJ(rowData.cnpj ?? "");
  };

  //Formatação da data
  const dataTemplate = (rowData: DocumentoFiscal) => {
    return formatarData(rowData.dataEmissao ?? "");
  };

  //Formatação do valor
  const valorTemplate = (rowData: DocumentoFiscal) => {
    return formatarMoeda(rowData.valor);
  };

  //Abrir link para detalhar nota no SAT
  const detalharNota = (chave: string) => {
    if (chave.length == 44) {
      return window.open(
        "https://sat.sef.sc.gov.br/tax.NET/Sat.NFe.Web/Consultas/ConsultaPublicaNFe.aspx?chaveacesso=" + chave,
        "_blank",
      );
    }
    return null;
  };

  //Validação dos campos e chamada da API
  function botaoBuscarNota(): void {
    if ((numero && serie && cnpj) || chave) {
      setBuscaPermitida(true);
      if (chave) {
        setParametro(chave);
      } else if (cnpj && numero && serie) {
        setParametro(cnpj.replace(/[^A-Z0-9]/g, "") + "-" + numero + "-" + serie);
      }
    } else {
      setBuscaPermitida(false);
    }
  }

  //Tabela com os dados do documento fiscal
  function TabelaBuscarNota(props: { dados: DocumentoFiscalSigef[] | undefined }) {
    if (props.dados) {
      const listanotas = props.dados;
      return (
        <>
          {listanotas.length > 0 && (
            <div className={`${telaPadrao.painel} ${estilo.painel}`}>
              <Message
                severity="success"
                text="Nota fiscal encontrada: confira e clique em selecionar para adicionar ao demonstrativo de Prestação de Contas."
                className={estilo.mensagemInferior}
              />
              <DataTable
                value={listanotas}
                resizableColumns
                tableStyle={{ fontSize: "13px" }}
                className={estilo.tabela}
                responsiveLayout="stack"
                breakpoint="896px"
              >
                <Column body={cnpjTemplate} field="cnpj" header="CNPJ" />
                <Column field="numeroSerie" header="Número-série" />
                <Column body={dataTemplate} field="dataEmissao" header="Data" />
                <Column body={valorTemplate} field="valor" header="Valor" />
                <Column body={colunaGrupoBotoes} field="chave" />
              </DataTable>
            </div>
          )}
          {listanotas.length == 0 && buscaPermitida && (
            <Message
              severity="warn"
              text="Atenção: Nota fiscal não encontrada. Verifique a busca realizada e tente novamente."
              className={estilo.mensagemInferior}
            />
          )}
        </>
      );
    }

    return null;
  }

  function onHideDialog() {
    // Reseta todos os estados para seus valores iniciais
    props.onFechar();
    setBuscaPermitida(false);
    setParametro(undefined);
    setCnpj(undefined);
    setNumero(undefined);
    setSerie(undefined);
    setChave(undefined);
    setSelecionandoNota(false);
  }

  return (
    <>
      <div className={telaPadrao.container}>
        <Dialog
          visible={props.operacao.tipoOperacao === TipoOperacaoTabelaGastos.AdicionarImportacao}
          modal
          style={{ width: "60vw" }}
          breakpoints={{ "1536px": "70vw", "1279px": "85vw", "1024px": "95vw", "896px": "100vw" }}
          contentStyle={{ background: "hsl(45, 80%, 98%)" }}
          onHide={onHideDialog}
          closeOnEscape={true}
          header={opcoesDocumentoFiscal
            .filter(opcao => opcao.codigo === TipoDocumentoFiscal.notaFiscalEletronicaImportacao)
            .map(opcao => opcao.descricao)}
          headerStyle={{ background: "hsl(45, 80%, 98%)" }}
        >
          {!selecionandoNota && (
            <>
              <div className={estilo.formContainer}>
                <div className={estilo.formSection}>
                  <div className={estilo.formGroup}>
                    <label htmlFor="cnpj">CNPJ do Estabelecimento</label>
                    <InputMask
                      id="cnpj"
                      mask="**.***.***/****-99"
                      keyfilter={/[A-Z0-9]/}
                      title="Apenas números e letras maiúsculas são permitidos."
                      className={estilo.input}
                      value={cnpj}
                      onChange={e => setCnpj(e.target.value ?? "")}
                    />
                  </div>
                  <div className={estilo.formGroup}>
                    <label htmlFor="numero">Número da Nota</label>
                    <InputText
                      id="numero"
                      maxLength={10}
                      keyfilter="int"
                      className={estilo.input}
                      value={numero}
                      onChange={e => setNumero(e.target.value)}
                    />
                  </div>
                  <div className={estilo.formGroup}>
                    <label htmlFor="serie">Série da Nota</label>
                    <InputText
                      id="serie"
                      maxLength={10}
                      keyfilter="int"
                      className={estilo.input}
                      value={serie}
                      onChange={e => setSerie(e.target.value)}
                    />
                  </div>
                </div>
                <div className={estilo.colunaCampos}>
                  <Divider layout="vertical" className="hidden md:flex">
                    <span>ou</span>
                  </Divider>
                  <Divider layout="horizontal" className="flex md:hidden" align="center">
                    <span>ou</span>
                  </Divider>
                </div>

                <div className={estilo.formSection}>
                  <div className={estilo.formGroup}>
                    <label htmlFor="chave">Chave de acesso</label>
                    <InputText
                      id="chave"
                      maxLength={44}
                      keyfilter="int"
                      className={estilo.input}
                      value={chave ?? ""}
                      onChange={e => setChave(e.target.value)}
                    />
                  </div>
                </div>
              </div>

              <div className={estilo.painelBotao}>
                <Button className={telaPadrao.botaoPrincipal} label="Buscar Nota" onClick={botaoBuscarNota} />
              </div>
            </>
          )}

          {buscaPermitida && (
            <ContainerQuery
              carregando={isPending || selecionandoNota}
              fallbackCarregamento={
                <div className="text-center">
                  <SpinnerCpesc />
                </div>
              }
              erro={error}
              fallbackErro={<TextoErro mensagem="Falha ao obter dados" />}
            >
              <TabelaBuscarNota dados={data} />
            </ContainerQuery>
          )}
          {!buscaPermitida && (
            <Message
              severity="info"
              text="Preencha CNPJ, Número e Série ou somente a Chave para buscar uma nota fiscal eletrônica. "
              className={estilo.mensagemInferior}
            />
          )}
        </Dialog>
      </div>
    </>
  );
}
