import { chamarApi } from "@/helpers/api";
import { useQuery } from "@tanstack/react-query";
import { MIN_BUSCA, SEPARADOR_NCM, type ItemFiscal } from "cpesc-shared";
import { endpoints } from "cpesc-shared/src/endpoints/main";
import { Dropdown } from "primereact/dropdown";
import { useEffect, useRef, useState } from "react";
import estilo from "./AdicaoDocumentoFiscalManual.module.scss";

export interface BuscarNcmProps {
  busca: string;
  tipoGasto: string;
  onCancelarNcm: () => void;
  onSelecaoNcm: (ncm: string) => void;
}

export default function DropdownBuscarNcm(props: BuscarNcmProps) {
  const [itemDigitado, setItemDigitado] = useState<string>(props.busca);
  const [items, setItems] = useState<{ label: string; ncm: string }[]>([]);
  const [blocoAtual, setblocoAtual] = useState<number>(0);
  const [totalItens, setTotalItens] = useState<number>(0);
  const loadLazyTimeout = useRef<NodeJS.Timeout | null>(null);

  const { data, isLoading, refetch } = useQuery({
    enabled: false,
    queryKey: [itemDigitado],
    queryFn: () =>
      chamarApi(
        endpoints.buscarItensFiscaisPorNcmETipoGasto,
        {
          ncm: itemDigitado,
          tipoGasto: props.tipoGasto,
        },
        `?skip=${blocoAtual}&take=10`,
      ),
  });

  useEffect(() => {
    if (data) {
      const newItems = data.items.map((item: ItemFiscal) => ({
        label: `${item.ncm}${SEPARADOR_NCM}${item.descricaoLonga}`,
        ncm: `${item.ncm ?? "0"}${SEPARADOR_NCM}`,
      }));

      if (blocoAtual <= 0) {
        //para blocoAtual 0 ou -1, substitui os itens, senao adiciona
        setItems(newItems);
      } else {
        setItems(prevItems => [...prevItems, ...newItems]);
      }
      setTotalItens(data.total);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps -- nao quero que o efeito dependa de blocoAtual
  }, [data]);

  useEffect(() => {
    if (itemDigitado && itemDigitado.length >= MIN_BUSCA) {
      if (blocoAtual === 0) {
        //se ja esta no bloco 0, nao precisa setar novamente
        void refetch();
      } else {
        //força alteracao do blocoAtual para disparar o useEffect de refetch
        setblocoAtual(0);
      }
    } else {
      setItems([]); //limpa itens se apagar o que foi digitado
      setTotalItens(0);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps -- nao quero que o efeito dependa de blocoAtual
  }, [itemDigitado, refetch]);

  useEffect(() => {
    if (itemDigitado && itemDigitado.length >= MIN_BUSCA) {
      //garante refetch apenas se tiver algo digitado no comprimento minimo
      void refetch();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps -- nao quero que o efeito dependa de itemDigitado
  }, [blocoAtual, refetch]);

  const obterNcm = (valorDigitado: string) => {
    if (valorDigitado && valorDigitado.length >= MIN_BUSCA) {
      const ncmExtraido = valorDigitado.split(`${SEPARADOR_NCM}`)[0];
      if (isNaN(Number(ncmExtraido))) {
        return "";
      }
      return ncmExtraido;
    }
    return valorDigitado;
  };

  //funcao chamada quando o usuario rola a lista do dropdown
  const onLazyLoad = (event: { first: number; last: number }) => {
    const { last } = event;
    const nextSkip = items.length;
    const temMais = nextSkip < totalItens;

    if (last === totalItens || !temMais || (itemDigitado && itemDigitado.length < MIN_BUSCA)) {
      return;
    }

    const threshold = items.length * 0.75; //caso a lista exiba 75% dos itens, carrega mais

    if (last > threshold) {
      if (loadLazyTimeout.current) {
        clearTimeout(loadLazyTimeout.current);
      }
      loadLazyTimeout.current = setTimeout(() => {
        // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition -- como acima tem um return para !temMais, esse if seria desnecessario, mas temos um Timeout aqui
        if (itemDigitado && itemDigitado.length >= MIN_BUSCA && temMais) {
          setblocoAtual(nextSkip);
        }
      }, 250);
    }
  };

  //atribuicao dos css para itens internos do dropDown
  const ptDropDown = {
    panel: {
      className: estilo.ptDropdownPanel,
    },
    input: {
      className: estilo.ptDropdownInput,
    },
  };

  return (
    <Dropdown
      className={estilo.dropDown}
      loading={isLoading}
      value={itemDigitado}
      onChange={e => {
        setItemDigitado(e.value as string);
      }}
      onBlur={e => {
        const ncm = e.target.value;
        setItemDigitado(ncm);
        if (ncm) {
          const ncmExtraido = obterNcm(ncm);
          props.onSelecaoNcm(ncmExtraido);
        }
      }}
      options={items}
      optionValue="ncm"
      placeholder="Pesquisar por NCM ou descrição"
      title="Pesquisar NCM ou descrição"
      emptyMessage={items.length === 0 ? "Nenhum item encontrado" : "Carregando..."}
      pt={ptDropDown}
      virtualScrollerOptions={{
        lazy: true,
        onLazyLoad,
        itemSize: 50,
        showLoader: true,
        numToleratedItems: 10,
        step: 10,
      }}
      dropdownIcon="pi pi-search"
      required
      editable
      showClear
      checkmark
      appendTo="self"
    />
  );
}
