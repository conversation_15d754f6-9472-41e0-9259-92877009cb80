import { chamar<PERSON>pi } from "@/helpers/api";
import { opcoesDocumentoFiscal } from "@/helpers/constantes";
import type { DeepPartial } from "@/helpers/DeepPartial";
import { formatarMoeda } from "@/helpers/formatacao";
import { useToast } from "@/hooks/useToast";
import telaPadrao from "@/tela-padrao.module.scss";
import { useQuery } from "@tanstack/react-query";
import classNames from "classnames";
import {
  type DocumentoFiscal,
  type DocumentoFiscalItem,
  MIN_BUSCA,
  TipoCompra,
  TipoDocumentoFiscal,
  validarCnpj,
} from "cpesc-shared";
import { endpoints } from "cpesc-shared/out/endpoints/main";
import type { PayloadCriarDocFiscalManual } from "cpesc-shared/src/endpoints/documento-fiscal";
import { Button } from "primereact/button";
import { Calendar } from "primereact/calendar";
import { Dialog } from "primereact/dialog";
import { InputMask } from "primereact/inputmask";
import { InputNumber } from "primereact/inputnumber";
import { Message } from "primereact/message";
import { type ReactNode, useEffect, useState } from "react";
import { type OperacaoTabelaGastos, TipoOperacaoTabelaGastos } from "../Tabelas/TabelaGastos";
import estilo from "./AdicaoDocumentoFiscalManual.module.scss";
import TabelaAdicaoNFManual from "./TabelaAdicaoNFManual";

interface AdicaoDocumentoFiscalManualProps {
  movimentacaoId: number;
  totalMovimentacao: number;
  operacao: OperacaoTabelaGastos;
  onCriar: (nota: PayloadCriarDocFiscalManual) => void;
  onAlterar: (nota: DocumentoFiscal) => void;
  onFechar: (operacao: OperacaoTabelaGastos) => void;
}

export default function AdicaoDocumentoFiscalManual(props: AdicaoDocumentoFiscalManualProps) {
  const [mensagem, setMensagem] = useState<ReactNode>(
    <>
      Clique no ícone <i className="pi pi-check"></i> quando terminar de preencher os campos. Clicando no botão{" "}
      <i className="pi pi-plus"></i> para adicionar mais um item. Você também pode clicar no ícone{" "}
      <i className="pi pi-trash"></i> para excluir um item. Clicando no ícone <i className="pi pi-pencil"></i> você pode
      editar os dados.
    </>,
  );

  const documentoFiscalItensVazio: DocumentoFiscalItem[] = [
    {
      id: -1,
      ncm: "",
      descricao: "",
      documentoFiscalId: 0,
      quantidade: 0,
      unidade: "",
      valorunitario: null,
      valor: 0,
      tipocompraId: TipoCompra.padrao,
      processotipocompra: "",
    },
  ];

  const documentoVazio: DocumentoFiscal = {
    id: 0,
    movimentacaoId: 0,
    tipodocumentofiscalId: props.operacao.tipoNota,
    numeroSerie: "",
    codigoSigef: null,
    chave: "",
    serie: null,
    numero: null,
    cnpj: "",
    dataEmissao: null,
    valor: null,
    desconto: null,
    acrescimo: null,
    documentoFiscalItens: documentoFiscalItensVazio,
  };

  //TODO ao iniciar garantir que os dados estejam vazios
  const [documentoFiscal, setDocumentoFiscal] = useState<DocumentoFiscal>(documentoVazio);
  const [itensEmEdicao, setItensEmEdicao] = useState(
    new Map<number, Partial<DocumentoFiscalItem>>([[-1, { ...documentoFiscalItensVazio[0] }]]),
  );
  const toast = useToast();

  const habilitarBusca: boolean =
    (props.operacao.tipoOperacao === TipoOperacaoTabelaGastos.EditarManual ||
      props.operacao.tipoOperacao === TipoOperacaoTabelaGastos.EditarImportacao) &&
    props.movimentacaoId > 0;

  // Busca o documento fiscal no banco de dados
  const { data, isLoading } = useQuery<DocumentoFiscal | undefined>({
    queryKey: ["/documento-fiscal", props.movimentacaoId, props.operacao.tipoNota],
    queryFn: async () => {
      const resultados = await chamarApi(
        endpoints.buscarDocumentosFiscais,
        {},
        `?movimentacaoId=${props.movimentacaoId.toString()}`,
      );

      return resultados[0];
    },
    enabled: habilitarBusca,
  });

  useEffect(() => {
    if (data) {
      setDocumentoFiscal({
        ...data,
      });
      setItensEmEdicao(new Map());
    }
  }, [data]);

  const somaSubtotal = documentoFiscal.documentoFiscalItens
    ? Math.round(
        documentoFiscal.documentoFiscalItens.reduce((sum, item) => {
          return ((sum + item.valor) * 100) / 100;
        }, 0) * 100,
      ) / 100
    : 0;

  const liberarBotaoSalvar =
    (props.operacao.tipoNota === TipoDocumentoFiscal.cupomFiscalManual || documentoFiscal.serie !== null) &&
    documentoFiscal.numero !== null &&
    documentoFiscal.dataEmissao !== null &&
    itensEmEdicao.size === 0 &&
    validarCnpj(documentoFiscal.cnpj ?? "") &&
    (documentoFiscal.acrescimo ?? 0) < somaSubtotal &&
    (documentoFiscal.desconto ?? 0) < somaSubtotal;

  // Salva documento fiscal no banco de dados e passa resultado para o componente pai
  function botaoSalvarNota() {
    // Verifica se todos os campos obrigatórios dos itens estão preenchidos
    const itens = documentoFiscal.documentoFiscalItens ?? [];
    if (
      !validarCnpj(documentoFiscal.cnpj ?? "") ||
      (itens.every(item => item.ncm != "0") && itens.every(item => item.ncm.length < MIN_BUSCA)) ||
      !itens.every(item => item.valorunitario != null) ||
      !itens.every(item => (item.valorunitario ?? 0) > 0) ||
      !itens.every(item => {
        return (
          (item.tipocompraId !== TipoCompra.padrao &&
            (item.processotipocompra ? item.processotipocompra.trim() !== "" : false)) ||
          (item.tipocompraId === TipoCompra.padrao &&
            (item.processotipocompra ? item.processotipocompra.trim() === "" : true))
        );
      })
    ) {
      toast.show({
        severity: "error",
        summary: "Erro",
        detail: "Todos os itens devem ser preenchidos com valores válidos!",
        life: 3000, // Tempo de exibição em milissegundos
      });
      return;
    }

    // Verifica se todos os campos obrigatórios estão preenchidos
    if (
      documentoFiscal.numero == null ||
      (props.operacao.tipoNota !== TipoDocumentoFiscal.cupomFiscalManual && documentoFiscal.serie === null) ||
      documentoFiscal.cnpj == null ||
      documentoFiscal.dataEmissao == null ||
      documentoFiscal.documentoFiscalItens == null
    ) {
      toast.show({
        severity: "error",
        summary: "Erro",
        detail: "Campos obrigatórios não preenchidos, insira valores válidos!",
      });
      return;
    }
    // Documento fiscal completo
    else {
      // Verifica se a soma dos itens confere com o valor da movimentação
      if (
        somaSubtotal + (documentoFiscal.acrescimo ?? 0) - (documentoFiscal.desconto ?? 0) !==
        props.totalMovimentacao
      ) {
        toast.show({
          severity: "warn",
          summary: "ATENÇÃO",
          detail:
            "A soma dos itens lançados não confere com o valor do gasto. Lembre-se de corrigir antes de Finalizar o Crédito.",
          life: 5000,
        });
      }
      let nota: DeepPartial<DocumentoFiscal>;

      if (props.operacao.tipoOperacao === TipoOperacaoTabelaGastos.EditarImportacao) {
        nota = {
          id: documentoFiscal.id,
          documentoFiscalItens: documentoFiscal.documentoFiscalItens.map(item => ({
            id: item.id,
            ncm: item.ncm,
            tipocompraId: item.tipocompraId,
            processotipocompra: item.processotipocompra,
          })),
        };
      } else {
        nota = {
          tipodocumentofiscalId: props.operacao.tipoNota,
          movimentacaoId: props.movimentacaoId,
          documentoFiscalItens: documentoFiscal.documentoFiscalItens,
          codigoSigef: null,
          serie: documentoFiscal.serie,
          numero: documentoFiscal.numero,
          cnpj: documentoFiscal.cnpj,
          dataEmissao: documentoFiscal.dataEmissao,
          valor: somaSubtotal + (documentoFiscal.acrescimo ?? 0) - (documentoFiscal.desconto ?? 0),
          desconto: documentoFiscal.desconto ?? null,
          acrescimo: documentoFiscal.acrescimo ?? null,
        };

        for (const item of nota.documentoFiscalItens ?? []) {
          if (item.id !== undefined && item.id <= 0) {
            delete item.id;
          }
          item.documentoFiscalId = documentoFiscal.id;
        }
      }

      if (documentoFiscal.id > 0) {
        nota.id = documentoFiscal.id;
        props.onAlterar(nota as DocumentoFiscal);
      } else {
        props.onCriar(nota as PayloadCriarDocFiscalManual);
      }
    }
  }

  // Adiciona nova linha na tabela de itens do documento fiscal
  const adicionarLinha = () => {
    const id = -((documentoFiscal.documentoFiscalItens?.length ?? 0) + 1);

    const novaLinha: DocumentoFiscalItem = { ...documentoFiscalItensVazio[0], id };

    setDocumentoFiscal({
      ...documentoFiscal,
      documentoFiscalItens: [...(documentoFiscal.documentoFiscalItens ?? []), novaLinha],
    });

    setItensEmEdicao(itensEmEdicao.set(id, { ...novaLinha }));

    setMensagem(
      <>
        Clique no ícone <i className="pi pi-pencil"></i> para editar os dados. Após a Edição clique em{" "}
        <i className="pi pi-check"></i> para confirmar ou no ícone <i className="pi pi-times"></i> para cancelar.
      </>,
    );
  };

  const ptdialog = {
    root: {
      className: estilo.modal,
    },
    headerIcons: {
      className: estilo.iconesCabecalho,
    },
  };

  return (
    <div className={telaPadrao.container}>
      <Dialog
        pt={ptdialog}
        visible={
          props.operacao.tipoOperacao === TipoOperacaoTabelaGastos.EditarManual ||
          props.operacao.tipoOperacao === TipoOperacaoTabelaGastos.EditarImportacao ||
          props.operacao.tipoOperacao === TipoOperacaoTabelaGastos.AdicionarManual
        }
        onHide={() => {
          setDocumentoFiscal(documentoVazio);
          setItensEmEdicao(new Map());
          props.onFechar(props.operacao);
        }}
        closeOnEscape={true}
        modal
        maximizable
        style={{ width: "70vw" }}
        breakpoints={{ "1536px": "80vw", "1408px": "85vw", "1280px": "90vw", "1152px": "100vw" }}
        contentStyle={{ background: "hsl(45, 80%, 98%)" }}
        header={opcoesDocumentoFiscal
          .filter(opcao => opcao.codigo === props.operacao.tipoNota)
          .map(opcao => {
            const isEditing =
              props.operacao.tipoOperacao === TipoOperacaoTabelaGastos.EditarManual ||
              props.operacao.tipoOperacao === TipoOperacaoTabelaGastos.EditarImportacao;
            return `${opcao.descricao}${isEditing ? " (Editar)" : ""}`;
          })}
        headerStyle={{ background: "hsl(45, 80%, 98%)" }}
      >
        <div className={estilo.conteudoDialog}>
          <div className={classNames(estilo.conteudoCarregadoDialog, isLoading && estilo.carregando)}>
            <div className={estilo.painelNota}>
              <label>
                <span>CNPJ do Estabelecimento:</span>
                <InputMask
                  id="cnpj"
                  mask="**.***.***/****-99"
                  keyfilter={/[A-Z0-9]/}
                  title="Apenas números e letras maiúsculas são permitidos."
                  className={estilo.caixaInputMask}
                  autoFocus
                  placeholder="00.ABY.Z00/0AZ0-00"
                  value={documentoFiscal.cnpj?.toLocaleUpperCase() ?? ""}
                  onChange={e => {
                    const cnpj = e.target.value?.replace(/[^A-Za-z0-9]/g, "") ?? "";
                    if (cnpj.length < 14) {
                      setDocumentoFiscal({ ...documentoFiscal, cnpj: e.target.value?.toLocaleUpperCase() ?? "" });
                    } else {
                      if (validarCnpj(cnpj)) {
                        setDocumentoFiscal({ ...documentoFiscal, cnpj });
                      } else {
                        toast.show({
                          severity: "error",
                          summary: "Erro",
                          detail: "CNPJ inválido!",
                          life: 3000,
                        });
                      }
                    }
                  }}
                  disabled={props.operacao.tipoNota === TipoDocumentoFiscal.notaFiscalEletronicaImportacao}
                  required
                />
              </label>
              <label>
                <span>
                  {props.operacao.tipoNota === TipoDocumentoFiscal.cupomFiscalManual
                    ? "Cupom Fiscal: "
                    : "Número da Nota Fiscal: "}
                </span>
                <InputNumber
                  id="number"
                  maxLength={10}
                  useGrouping={false}
                  className={estilo.caixaInput}
                  value={documentoFiscal.numero ?? null}
                  onChange={e => {
                    setDocumentoFiscal({ ...documentoFiscal, numero: e.value });
                  }}
                  disabled={props.operacao.tipoNota === TipoDocumentoFiscal.notaFiscalEletronicaImportacao}
                  required
                />
              </label>
              {props.operacao.tipoNota !== TipoDocumentoFiscal.cupomFiscalManual && (
                <label>
                  <span>Série da Nota: </span>
                  <InputNumber
                    maxLength={10}
                    useGrouping={false}
                    //className={estilo.caixaInputMenor}
                    className={estilo.caixaInput}
                    value={documentoFiscal.serie ?? null}
                    onChange={e => {
                      setDocumentoFiscal({ ...documentoFiscal, serie: e.value });
                    }}
                    disabled={props.operacao.tipoNota === TipoDocumentoFiscal.notaFiscalEletronicaImportacao}
                    required
                  />
                </label>
              )}
              <label>
                <span>Data emissão: </span>
                <Calendar
                  id="dataEmissao"
                  className={estilo.caixaInput}
                  value={documentoFiscal.dataEmissao ? new Date(documentoFiscal.dataEmissao) : null}
                  dateFormat="dd/mm/yy"
                  onChange={e => {
                    setDocumentoFiscal({ ...documentoFiscal, dataEmissao: e.value ?? null });
                  }}
                  disabled={props.operacao.tipoNota === TipoDocumentoFiscal.notaFiscalEletronicaImportacao}
                  locale="pt_BR"
                  placeholder="__/__/____"
                  showButtonBar
                  showIcon
                  required
                />
              </label>
            </div>
            <div className={estilo.painelBotao}>
              <label htmlFor="valor">Valor do Gasto: </label>
              <span id="valor" className={estilo.caixaInputNegrito}>
                {formatarMoeda(props.totalMovimentacao)}
              </span>
            </div>

            <div className={estilo.painelNotaItem}>
              <TabelaAdicaoNFManual
                documentoFiscal={documentoFiscal}
                setDocumentoFiscal={setDocumentoFiscal}
                itensEmEdicao={itensEmEdicao}
                setItensEmEdicao={setItensEmEdicao}
              />

              {props.operacao.tipoNota !== TipoDocumentoFiscal.notaFiscalEletronicaImportacao && (
                <div className={estilo.painelBotaoAdicionar}>
                  <Button
                    icon="pi pi-plus"
                    className={classNames(telaPadrao.botaoPrincipal, "mb-1")}
                    onClick={adicionarLinha}
                    tooltip={`Adicionar nova linha para edição de item de ${props.operacao.tipoNota === TipoDocumentoFiscal.cupomFiscalManual ? "cupom fiscal" : "nota fiscal"}`}
                    tooltipOptions={{ position: "bottom" }}
                  />
                </div>
              )}
            </div>

            <div className={estilo.painelAdicionais}>
              {props.operacao.tipoNota !== TipoDocumentoFiscal.notaFiscalEletronicaImportacao && (
                <>
                  <div className={estilo.painelBotao}>
                    <label>
                      <span>Acréscimo:</span>
                      <InputNumber
                        id="acrescimo"
                        locale="pt-BR"
                        mode="currency"
                        currency="BRL"
                        value={documentoFiscal.acrescimo ?? 0}
                        onChange={e => {
                          setDocumentoFiscal({ ...documentoFiscal, acrescimo: e.value });
                        }}
                        className={estilo.caixaInput}
                      />
                    </label>
                    <label>
                      <span>Desconto:</span>
                      <InputNumber
                        id="desconto"
                        locale="pt-BR"
                        mode="currency"
                        currency="BRL"
                        value={documentoFiscal.desconto ?? 0}
                        onChange={e => {
                          setDocumentoFiscal({ ...documentoFiscal, desconto: e.value });
                        }}
                        className={estilo.caixaInput}
                      />
                    </label>
                  </div>
                </>
              )}
              <div className={estilo.painelBotao}>
                <label className={estilo.soma}>
                  Soma da Nota:{" "}
                  {formatarMoeda(somaSubtotal + (documentoFiscal.acrescimo ?? 0) - (documentoFiscal.desconto ?? 0))}
                </label>
              </div>
            </div>

            <div className={estilo.painelBotao}>
              <Button
                className={telaPadrao.botaoPrincipal}
                label="Salvar"
                disabled={!liberarBotaoSalvar}
                onClick={botaoSalvarNota}
              />
            </div>

            {mensagem && <Message severity="info" text={mensagem} className="w-full" />}
          </div>
        </div>
      </Dialog>
    </div>
  );
}
