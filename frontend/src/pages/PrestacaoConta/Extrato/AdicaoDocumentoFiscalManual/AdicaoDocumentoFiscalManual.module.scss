@use "@/variables";

.modal,
.modal::backdrop {
  overscroll-behavior: contain;
}

.iconesCabecalho {
  gap: 1rem;
}

.conteudoDialog {
  position: relative;

  .spinner {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }

  .conteudoCarregadoDialog.carregando {
    opacity: 0.2;
  }
}

.painelNota {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  align-items: baseline;
  padding: 1.5rem;
  border: 1px solid var(--cor-verde-background);
  border-radius: var(--border-radius-padrao);

  label {
    font-weight: 600;
    font-size: 0.9rem;
    display: flex;
    gap: 0.5rem;
    align-items: baseline;

    @media only screen and (max-width: variables.$limite-largura-tela-mobile-up) {
      flex-direction: column;
    }
  }
}

.caixaInputMask {
  font-size: var(--tamanho-fonte-pequena);
  border-radius: var(--border-radius-padrao);
  margin: 0;
  width: 8.5rem;
}

.caixaInput {
  font-size: var(--tamanho-fonte-pequena);
  border-radius: var(--border-radius-padrao);
  margin: 0;
  width: 7.5rem;
  align-items: inherit;

  & input {
    font-size: inherit;
    border-radius: inherit;
    margin: inherit;
    width: inherit;
    text-wrap: wrap;
  }
}

.painelBotao {
  display: flex;
  gap: 1rem;
  justify-content: right;
  align-items: center;
  margin: 0.5rem 0;

  [class*="botaoPrincipal"] {
    margin: 1rem 0;
  }
}

.painelNotaItem {
  table {
    margin: 1rem 0 0 0;
  }
  [class*="tabelaTransposta"] {
    background-color: white;
  }
}

.caixaInputGrande {
  font-size: var(--tamanho-fonte-pequena);
  border-radius: var(--border-radius-padrao);
  margin: 0;
  width: 100%;

  & input {
    width: inherit;
  }
}

.caixaInputMenor {
  font-size: var(--tamanho-fonte-pequena);
  border-radius: var(--border-radius-padrao);
  margin: 0;
  width: 5rem;

  & input {
    font-size: inherit;
    border-radius: inherit;
    margin: inherit;
    width: inherit;
  }
}

.caixaInputNegrito {
  margin: 10px 0;
  text-align: right;
  font-weight: bold;
}

.dropDown {
  font-size: var(--tamanho-fonte-pequena);
  border-radius: var(--border-radius-padrao);
  margin: 0;
  width: 100%;
  min-width: 145px;
}

.ptDropdownPanel {
  width: 90vh;
  font-size: var(--tamanho-fonte-pequena);

  & ul {
    & li {
      height: auto !important;
      & span {
        text-wrap: wrap;
      }
    }
  }

  @media only screen and (max-width: variables.$limite-largura-tela-media-down) {
    width: 80%;
  }
}

// .ptDropdownInput {
//   @extend .caixaInput;
// }

.painelBotaoAdicionar {
  display: flex;
  justify-content: right;
  padding-bottom: 0.5rem;

  & button {
    margin: 0;
  }
}

.painelAdicionais {
  font-size: 0.9rem;
  font-weight: 600;
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  padding: 1rem;
  width: 100%;
  border: 1px solid var(--cor-verde-background);
  border-radius: var(--border-radius-padrao);
  justify-content: end;

  :first-child {
    flex-wrap: wrap;
    justify-content: start;
    @media only screen and (max-width: variables.$limite-largura-tela-mobile-down) {
      flex-direction: column;
    }
  }

  label {
    display: flex;
    gap: 0.5rem;
    align-items: baseline;

    @media only screen and (max-width: variables.$limite-largura-tela-mobile-up) {
      flex-direction: column;
    }
  }

  .soma {
    font-size: 1rem;
  }
}

.mensagemInferior {
  width: 100%;

  :global(.p-message) {
    margin: 1rem;
  }
}

.margemDireita {
  margin-right: 10px;
}

.semQuebra {
  white-space: nowrap;
  text-wrap: nowrap;
}

.dropDownTipoCompra {
  @extend .dropDown;
  display: inline-flex;
  :global(.p-dropdown-label) {
    white-space: wrap;
  }
}

.ptDropdownPanelTipoCompra {
  @extend .ptDropdownPanel;
  width: 15rem;
}

.inputProcessoTipoCompra {
  display: inline-flex;
  flex-wrap: nowrap;

  @media (max-width: variables.$limite-largura-tela-mobile) {
    flex-wrap: wrap;
  }
  @media (max-width: variables.$limite-largura-tela-padrao) and (min-width: variables.$limite-largura-tela-media-down) {
    flex-wrap: wrap;
  }

  [class*="caixaInput"] {
    width: inherit;
    height: inherit;
  }
}
