import Tabela from "@/components/Tabela/Tabela";
import { formatarMoeda } from "@/helpers/formatacao";
import { useToast } from "@/hooks/useToast";
import {
  MIN_BUSCA,
  TipoCompra,
  TipoDocumentoFiscal,
  tiposCompraPermitida,
  type DocumentoFiscal,
  type DocumentoFiscalItem,
  type TiposCompra,
} from "cpesc-shared";
import { Button } from "primereact/button";
import { Dropdown } from "primereact/dropdown";
import { InputNumber } from "primereact/inputnumber";
import { InputText } from "primereact/inputtext";
import { Tag } from "primereact/tag";
import estilo from "./AdicaoDocumentoFiscalManual.module.scss";
import DropdownBuscarNcm from "./DropDownBuscarNcm";

export interface TabelaAdicaoDocFiscalManualProps {
  documentoFiscal: DocumentoFiscal;
  setDocumentoFiscal: (documentoFiscal: DocumentoFiscal) => void;
  itensEmEdicao: Map<number, Partial<DocumentoFiscalItem>>;
  setItensEmEdicao: (itensEmEdicao: Map<number, Partial<DocumentoFiscalItem>>) => void;
}

export default function TabelaAdicaoNFManual(props: TabelaAdicaoDocFiscalManualProps) {
  const naoImportada =
    props.documentoFiscal.tipodocumentofiscalId !== TipoDocumentoFiscal.notaFiscalEletronicaImportacao;
  const toast = useToast();

  const editorBuscaNcm = (rowData: DocumentoFiscalItem) => {
    const editandoItem = props.itensEmEdicao.has(rowData.id);
    return editandoItem ? (
      <DropdownBuscarNcm
        busca={props.itensEmEdicao.get(rowData.id)?.ncm ?? ""}
        tipoGasto={props.documentoFiscal.tipodocumentofiscalId === TipoDocumentoFiscal.notaServicoManual ? "S" : "M"}
        onCancelarNcm={() => {
          const novosItensEmEdicao = new Map(props.itensEmEdicao);
          novosItensEmEdicao.delete(rowData.id);
          props.setItensEmEdicao(novosItensEmEdicao);
        }}
        onSelecaoNcm={item => {
          const novosItensEmEdicao = new Map(props.itensEmEdicao);
          novosItensEmEdicao.set(rowData.id, {
            ...novosItensEmEdicao.get(rowData.id),
            ncm: item,
          });
          props.setItensEmEdicao(novosItensEmEdicao);
        }}
      />
    ) : (
      rowData.ncm
    );
  };

  const editorNcmDescricao = (rowData: DocumentoFiscalItem) => {
    const editandoItem = props.itensEmEdicao.has(rowData.id) && naoImportada;
    return editandoItem ? (
      <InputText
        type="text"
        className={estilo.caixaInputGrande}
        value={props.itensEmEdicao.get(rowData.id)?.descricao ?? ""}
        onChange={e => {
          const novosItensEmEdicao = new Map(props.itensEmEdicao);
          novosItensEmEdicao.set(rowData.id, {
            ...novosItensEmEdicao.get(rowData.id),
            descricao: e.target.value,
          });
          props.setItensEmEdicao(novosItensEmEdicao);
        }}
        required
      />
    ) : (
      rowData.descricao
    );
  };

  const editorQuantidade = (rowData: DocumentoFiscalItem) => {
    const editandoItem = props.itensEmEdicao.has(rowData.id) && naoImportada;

    return editandoItem ? (
      <InputNumber
        name="quantidade"
        value={
          (props.itensEmEdicao.get(rowData.id)?.quantidade ?? 0) > 0
            ? props.itensEmEdicao.get(rowData.id)?.quantidade
            : null
        }
        onChange={e => {
          const novosItensEmEdicao = new Map(props.itensEmEdicao);
          novosItensEmEdicao.set(rowData.id, {
            ...novosItensEmEdicao.get(rowData.id),
            quantidade: e.value ?? 0,
          });
          props.setItensEmEdicao(novosItensEmEdicao);
        }}
        onBlur={e => {
          const novosItensEmEdicao = new Map(props.itensEmEdicao);
          const valorParsed = parseInt(e.target.value);
          novosItensEmEdicao.set(rowData.id, {
            ...novosItensEmEdicao.get(rowData.id),
            quantidade: isNaN(valorParsed) ? undefined : valorParsed,
          });
          props.setItensEmEdicao(novosItensEmEdicao);
        }}
        className={estilo.caixaInputMenor}
        useGrouping={false}
        locale="pt-BR"
        required
      />
    ) : (
      rowData.quantidade
    );
  };

  const editorUnidade = (rowData: DocumentoFiscalItem) => {
    const editandoItem = props.itensEmEdicao.has(rowData.id) && naoImportada;
    return editandoItem ? (
      <InputText
        type="text"
        className={estilo.caixaInput}
        value={props.itensEmEdicao.get(rowData.id)?.unidade ?? ""}
        onChange={e => {
          const novosItensEmEdicao = new Map(props.itensEmEdicao);
          novosItensEmEdicao.set(rowData.id, {
            ...novosItensEmEdicao.get(rowData.id),
            unidade: e.target.value,
          });
          props.setItensEmEdicao(novosItensEmEdicao);
        }}
        required
      />
    ) : (
      rowData.unidade
    );
  };

  const editorValorUnitario = (rowData: DocumentoFiscalItem) => {
    const editandoItem = props.itensEmEdicao.has(rowData.id) && naoImportada;
    return editandoItem ? (
      <InputNumber
        name="valorunitario"
        value={props.itensEmEdicao.get(rowData.id)?.valorunitario ?? null}
        onChange={e => {
          const novosItensEmEdicao = new Map(props.itensEmEdicao);
          novosItensEmEdicao.set(rowData.id, {
            ...novosItensEmEdicao.get(rowData.id),
            valorunitario: e.value,
          });
          props.setItensEmEdicao(novosItensEmEdicao);
        }}
        onBlur={e => {
          const novosItensEmEdicao = new Map(props.itensEmEdicao);
          // Remove tudo exceto dígitos e vírgula decimal
          const valorLimpo = e.target.value
            .replace(/[^\d,]/g, "") // mantém apenas números e vírgula
            .replace(/,+/g, ",") // evita múltiplas vírgulas
            .replace(/^,/, ""); // remove vírgula no início

          // Troca vírgula por ponto para parseFloat
          const valorNumerico = parseFloat(valorLimpo.replace(",", "."));

          novosItensEmEdicao.set(rowData.id, {
            ...novosItensEmEdicao.get(rowData.id),
            valorunitario: isNaN(valorNumerico) ? null : valorNumerico,
          });
          props.setItensEmEdicao(novosItensEmEdicao);
        }}
        mode="currency"
        currency="BRL"
        locale="pt-BR"
        className={estilo.caixaInputMenor}
        required
      />
    ) : (
      formatarMoeda(rowData.valorunitario)
    );
  };

  const editorSubtotal = (rowData: DocumentoFiscalItem) => {
    const editandoItem = props.itensEmEdicao.has(rowData.id);
    const itemPendente = props.itensEmEdicao.get(rowData.id);
    return editandoItem
      ? (itemPendente?.quantidade ?? 0) * (itemPendente?.valorunitario ?? 0)
      : rowData.quantidade * (rowData.valorunitario ?? 0);
  };

  const templateTipoCompra = (item: DocumentoFiscalItem) => {
    const editandoItem = props.itensEmEdicao.has(item.id);
    const itemEmEdicao = props.itensEmEdicao.get(item.id);
    const tipoCompraAtual = tiposCompraPermitida.find(
      tipo => tipo.key === (itemEmEdicao?.tipocompraId ?? item.tipocompraId),
    );
    //atribuicao dos css para itens internos do dropDown
    const ptDropDown = {
      panel: {
        className: estilo.ptDropdownPanelTipoCompra,
      },
      input: {
        className: estilo.ptDropdownInput,
      },
    };

    return editandoItem ? (
      <div className={estilo.inputProcessoTipoCompra}>
        <Dropdown
          value={itemEmEdicao?.tipocompraId ?? TipoCompra.padrao}
          onChange={e => {
            const novosItensEmEdicao = new Map(props.itensEmEdicao);
            novosItensEmEdicao.set(item.id, {
              ...novosItensEmEdicao.get(item.id),
              tipocompraId: e.value ? (e.value as number) : TipoCompra.padrao,
            });
            props.setItensEmEdicao(novosItensEmEdicao);
          }}
          options={tiposCompraPermitida}
          optionLabel="name"
          optionValue="key"
          placeholder="Tipo de Compra"
          className={estilo.dropDownTipoCompra}
          pt={ptDropDown}
          valueTemplate={tiposCompraTemplate}
          itemTemplate={tiposCompraTemplate}
          required
        />
        {tipoCompraAtual?.key != TipoCompra.padrao && (
          <>
            <InputText
              className={estilo.caixaInput}
              placeholder="Processo SGPE"
              title="Campo obrigatório - Processo do SGPE relativo ao Tipo de Compra selecionado."
              value={itemEmEdicao?.processotipocompra ?? item.processotipocompra}
              onChange={e => {
                const processo = e.target.value;
                const novosItensEmEdicao = new Map(props.itensEmEdicao);
                novosItensEmEdicao.set(item.id, {
                  ...novosItensEmEdicao.get(item.id),
                  processotipocompra: processo,
                });
                props.setItensEmEdicao(novosItensEmEdicao);
                item.processotipocompra = processo;
              }}
              required
            />
          </>
        )}
      </div>
    ) : (
      <div className="flex align-items-center gap-1 justify-content-end lg:justify-content-start">
        <Tag
          className={estilo.margemDireita}
          severity={
            tipoCompraAtual?.key == TipoCompra.padrao
              ? "success"
              : tipoCompraAtual?.key == TipoCompra.emergencial
                ? "info"
                : "warning"
          }
          value={tipoCompraAtual?.label ?? "P"}
        />
        <div className={estilo.margemDireita}>{tipoCompraAtual?.name ?? "Padrão"}</div>
        {tipoCompraAtual?.key != TipoCompra.padrao && <div className={estilo.semQuebra}>{item.processotipocompra}</div>}
      </div>
    );
  };

  const tiposCompraTemplate = (opcoes: TiposCompra) => {
    return (
      <div className="flex align-items-center">
        <Tag
          className={estilo.margemDireita}
          severity={
            opcoes.key == TipoCompra.padrao ? "success" : opcoes.key == TipoCompra.emergencial ? "info" : "warning"
          }
          value={opcoes.label ?? "P"}
        />
        <div>{opcoes.name}</div>
      </div>
    );
  };

  const templateEdicao = (item: DocumentoFiscalItem) => {
    const editandoItem = props.itensEmEdicao.has(item.id);
    const itemPendente = props.itensEmEdicao.get(item.id);
    return editandoItem ? (
      <div className="flex gap-1 justify-content-end">
        <Button
          tooltip="Salvar vínculo com item"
          tooltipOptions={{ position: "top" }}
          onClick={() => {
            const testaNuloOuVazio = (valor: string | number | null | undefined) =>
              valor == null || valor.toString().trim() === "" || (typeof valor === "number" && valor <= 0);
            if (
              testaNuloOuVazio(itemPendente?.ncm) ||
              (itemPendente?.ncm !== undefined && itemPendente.ncm != "0" && itemPendente.ncm.length < MIN_BUSCA) ||
              testaNuloOuVazio(itemPendente?.descricao) ||
              testaNuloOuVazio(itemPendente?.quantidade) ||
              testaNuloOuVazio(itemPendente?.unidade) ||
              testaNuloOuVazio(itemPendente?.valorunitario) ||
              (itemPendente?.tipocompraId !== TipoCompra.padrao && testaNuloOuVazio(itemPendente?.processotipocompra))
            ) {
              toast.show({
                severity: "error",
                summary: "Erro",
                detail: "Todos os itens da tabela devem ser preenchidos com valores válidos! Edite os dados digitados.",
                life: 3000, // Tempo de exibição em milissegundos
              });
              return;
            }

            const novaDocumentoFiscal = {
              ...props.documentoFiscal,
              documentoFiscalItens:
                props.documentoFiscal.documentoFiscalItens?.map(notaItem => {
                  if (notaItem.id === item.id) {
                    return {
                      ...notaItem,
                      ...itemPendente,
                      valor: (itemPendente?.quantidade ?? 0) * (itemPendente?.valorunitario ?? 0),
                    };
                  }

                  return notaItem;
                }) ?? null,
            };
            props.setDocumentoFiscal(novaDocumentoFiscal);
            const novosItensEmEdicao = new Map(props.itensEmEdicao);
            novosItensEmEdicao.delete(item.id);
            props.setItensEmEdicao(novosItensEmEdicao);
          }}
        >
          <i className="pi pi-check" />
        </Button>

        <Button
          tooltip="Cancelar edição"
          tooltipOptions={{ position: "top" }}
          onClick={() => {
            const novosItensEmEdicao = new Map(props.itensEmEdicao);
            novosItensEmEdicao.delete(item.id);
            props.setItensEmEdicao(novosItensEmEdicao);
          }}
        >
          <i className="pi pi-times" />
        </Button>
      </div>
    ) : (
      <Button
        tooltip="Editar item"
        tooltipOptions={{ position: "top" }}
        onClick={() => props.setItensEmEdicao(new Map(props.itensEmEdicao).set(item.id, { ...item }))}
      >
        <i className="pi pi-pencil" />
      </Button>
    );
  };

  const templateCancelar = (item: DocumentoFiscalItem) => {
    if (naoImportada) {
      return (
        <Button
          onClick={() => {
            const novaDocumentoFiscal = {
              ...props.documentoFiscal,
              documentoFiscalItens:
                props.documentoFiscal.documentoFiscalItens?.filter(notaItem => notaItem.id !== item.id) ?? null,
            };
            props.setDocumentoFiscal(novaDocumentoFiscal);
            const novosItensEmEdicao = new Map(props.itensEmEdicao);
            novosItensEmEdicao.delete(item.id);
            props.setItensEmEdicao(novosItensEmEdicao);
          }}
        >
          <i className="pi pi-trash" />
        </Button>
      );
    } else {
      return;
    }
  };

  return (
    <>
      {props.documentoFiscal.documentoFiscalItens && (
        <Tabela
          dados={props.documentoFiscal.documentoFiscalItens}
          breakpoint={896}
          colunas={[
            {
              cabecalho: (
                <>
                  {props.documentoFiscal.tipodocumentofiscalId === TipoDocumentoFiscal.notaServicoManual
                    ? "CPS"
                    : "NCM"}
                  <i className="pi pi-question-circle" />
                </>
              ),
              titulo: `${props.documentoFiscal.tipodocumentofiscalId === TipoDocumentoFiscal.notaServicoManual ? "Código de Prestação de Serviço" : "Nomeclatura Comum do Mercosul"}`,
              conteudo: (item: DocumentoFiscalItem) => editorBuscaNcm(item),
            },
            { cabecalho: "Descrição", conteudo: (item: DocumentoFiscalItem) => editorNcmDescricao(item) },
            { cabecalho: "Quantidade", conteudo: (item: DocumentoFiscalItem) => editorQuantidade(item) },
            { cabecalho: "Unidade", conteudo: (item: DocumentoFiscalItem) => editorUnidade(item) },
            { cabecalho: "Valor Unitário", conteudo: (item: DocumentoFiscalItem) => editorValorUnitario(item) },
            {
              cabecalho: "Subtotal (AUTO)",
              conteudo: (item: DocumentoFiscalItem) => formatarMoeda(editorSubtotal(item)),
            },
            {
              cabecalho: "Tipo de Compra",
              conteudo: (item: DocumentoFiscalItem) => templateTipoCompra(item),
            },
            { cabecalho: "", conteudo: (item: DocumentoFiscalItem) => templateEdicao(item) },
            {
              cabecalho: "",
              conteudo: (item: DocumentoFiscalItem) => templateCancelar(item),
            },
          ]}
        ></Tabela>
      )}
    </>
  );
}
