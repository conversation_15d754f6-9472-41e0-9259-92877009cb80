@use "@/mixins.scss";
@use "@/variables";

@keyframes piscarSombra {
  0% {
    box-shadow: 0 0 1px 1px rgba(255, 0, 0, 0.8);
  }
  40% {
    box-shadow: 0 0 4px 4px rgba(255, 0, 0, 0.3);
  }
  80% {
    box-shadow: 0 0 1px 1px rgba(255, 0, 0, 0.8);
  }
  100% {
    box-shadow: none;
  }
}

.tituloExtrato {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: nowrap;
  :global(.p-tag) {
    white-space: nowrap;
  }
}

.declaracao {
  display: flex;
  gap: 2rem;
  align-items: center;

  @media (max-width: 1279px) {
    flex-direction: column;
    gap: 0.5rem;
  }
}

.info {
  display: flex;
  flex-direction: column;
  width: 100%;

  @media (min-width: 1280px) {
    width: 70%;
  }
  @media (min-width: variables.$limite-largura-tela-grande) {
    width: 60%;
  }
}

.label {
  display: inline-flex;
  gap: 1em;
  align-items: center;
  margin-bottom: 0.5em;
  flex-wrap: wrap;

  & > span {
    font-weight: bold;
    text-align: end;
  }

  @media (max-width: variables.$limite-largura-tela-mobile-up) {
    gap: 0.25em;
    & > span {
      justify-self: flex-start;
    }
  }
}

.caixainput {
  max-width: 25rem;

  & input {
    padding: 0.25rem;
    font-size: 1.2em;
    transition: border-color 0.3s ease;
  }

  & input:not(:first-child):not(:last-child) {
    min-width: 80px;
    width: 40%;
  }
  & input:last-child {
    min-width: 40px;
    width: 10%;
  }
  & input:first-child {
    min-width: 60px;
    width: 30%;
  }

  & input:required:invalid {
    animation: piscarSombra 4s infinite;
  }

  & input:disabled {
    color: black;
    border-color: black;
    background-color: white;
  }
}

.grupoCartao {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  width: 100%;
  gap: 1rem;
  @media (min-width: 768px) {
    flex-wrap: nowrap;
  }
}

.cartao {
  min-width: 210px;
  text-align: center;
}

.cartaoContent {
  font-size: 2em;
  text-align: center;
  padding: 0.5rem 0 0;
}

.linha {
  & h2 {
    margin: 0;
  }
}

.dropDown {
  width: auto;
  font-size: 0.9rem;
  font-weight: normal;
  text-wrap: auto;
}

.dropDownInput {
  font-size: 0.9rem;
  text-wrap: auto;
}

.containerChevron {
  display: flex;
  justify-content: start;
  align-items: center;
  gap: 0.5rem;

  button {
    display: flex;
    align-items: center;

    i[class*="chevron"] {
      padding-right: 5px;
    }
  }

  @media (max-width: 960px) {
    justify-content: end;
  }
}

.botaoCancelar {
  background-color: transparent;
  color: var(--cor-verde-escuro);
  border: var(--cor-verde-escuro);
  padding: 0 10px;
}

.botaoNaTabela {
  background-color: transparent;
  color: var(--cor-verde-escuro);
  border: var(--cor-verde-escuro);
  padding: 0;
}

.botaoNaTabelaComErro {
  @extend .botaoNaTabela;

  color: var(--cor-alerta);
}

.documentoFiscalExpandido {
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: var(--border-radius-padrao);

  .documentoFiscalHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;

    h3 {
      margin: 0;
    }
  }

  .documentoFiscalInfo {
    display: flex;
    gap: 2rem;
    margin-bottom: 1rem;

    span {
      font-weight: 500;
    }
  }

  table {
    tbody {
      tr {
        td {
          //text-align: left;
          gap: 5px; /* para tabela transposta */
        }
        //       @media (max-width: variables.$limite-largura-tela-mobile-up) {
        //         td {
        //           text-align: right;
        //           span {
        //             text-align: left;
        //           }
        //         }
        //       }
      }
    }
  }

  .tituloConfirmacao {
    color: var(--cor-texto-destacado);
  }
}

.impostos {
  font-weight: normal;
  text-align: right;
  font-size: small;
}

.containerBotao {
  display: flex;
  justify-content: end;
  gap: 2rem;

  @media (max-width: variables.$limite-largura-tela-tablet) {
    flex-direction: column;
    gap: 0;
  }

  [class*="botaoPrincipal"] {
    margin: 1rem 0;
  }
  [class*="botaoSecundario"] {
    @media (max-width: variables.$limite-largura-tela-tablet) {
      margin: 0;
    }
  }
}

.dialogos {
  width: 40vw;
  @media (max-width: 768px) {
    width: 90vw;
  }
}

.dropDown {
  width: auto;
  font-weight: normal;
  text-wrap: auto;

  @media (max-width: variables.$limite-largura-tela-mobile-down) {
    padding: 0.25rem 0.25rem;
  }
}

.semQuebra {
  white-space: nowrap;
  text-wrap: nowrap;
}
