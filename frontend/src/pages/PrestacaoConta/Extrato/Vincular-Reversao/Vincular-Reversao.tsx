import { formatarDataHora, formatarMoeda } from "@/helpers/formatacao";
import telaPadrao from "@/tela-padrao.module.scss";
import type { Movimentacao } from "cpesc-shared";
import { Button } from "primereact/button";
import { Column } from "primereact/column";
import { DataTable } from "primereact/datatable";
import { Dialog } from "primereact/dialog";
import { Message } from "primereact/message";
import { useState } from "react";
import estilo from "./Vincular-Reversao.module.scss";

interface VincularReversaoProps {
  movimentacaoId: number;
  movimentacoes: Movimentacao[];
  onSelecionar: (movimentaocaoId: number, reversaoMovimentacao: Movimentacao) => void;
  onFechar: () => void;
}

export default function VincularReversao(props: VincularReversaoProps) {
  const [movimentacoes, setMovimentacoes] = useState<Movimentacao[] | null>(props.movimentacoes);

  // Botão para selecionar a nota
  const selecionarReversaoTemplate = (rowData: Movimentacao) => {
    return (
      <Button label="Selecionar" onClick={() => selecionarReversao(rowData)} className={telaPadrao.botaoPrincipal} />
    );
  };

  // Salva o documento no banco de dados e passa resultado para o componente pai
  const selecionarReversao = (movimentacaoReversao: Movimentacao) => {
    props.onSelecionar(props.movimentacaoId, movimentacaoReversao);
    onHideDialog();
  };

  const header = () => {
    return (
      <>
        <div className={estilo.painelNotaItem}>
          <div className={estilo.tituloTela}>Seleção de Valor de Reversão</div>
        </div>
      </>
    );
  };

  const footer = () => {
    return (
      <Message
        severity="success"
        text="Reversões com os mesmos valores: confira e selecione para vincular a movimentação."
        className={estilo.mensagemInferior}
      />
    );
  };

  //Formatação da data
  const dataTemplate = (rowData: Movimentacao) => {
    return formatarDataHora(rowData.dataTransacao);
  };

  //Formatação do valor
  const valorTemplate = (rowData: Movimentacao) => {
    return formatarMoeda(rowData.valorTransacaoReal);
  };

  //Tabela com os dados de reversao
  function TabelaEscolherReversao(props: { dados: Movimentacao[] | null }) {
    if (props.dados) {
      return (
        <>
          {props.dados.length > 0 && (
            <div>
              <div className={telaPadrao.painel}>
                <DataTable
                  value={props.dados}
                  resizableColumns
                  tableStyle={{ fontSize: "13px" }}
                  className={estilo.tabela}
                  responsiveLayout="stack"
                  breakpoint="768px"
                >
                  <Column field="dataEmissao" header="Data" body={dataTemplate} />
                  <Column field="valor" header="Valor" body={valorTemplate} />
                  <Column body={selecionarReversaoTemplate} />
                </DataTable>
              </div>
            </div>
          )}
          {props.dados.length == 0 && (
            <Message
              severity="warn"
              text="Atenção: Ainda nenhuma reversão encontrada com este valor. Vincule uma nota/cupom ou tente novamente mais tarde."
              className={estilo.mensagemInferior}
            />
          )}
        </>
      );
    }
    return null;
  }

  function onHideDialog() {
    // Reseta todos os estados para seus valores iniciais
    setMovimentacoes(null);
    props.onFechar();
  }

  return (
    <>
      <div className={telaPadrao.container}>
        <Dialog
          visible={movimentacoes ? true : false}
          modal
          style={{ width: "50vw" }}
          breakpoints={{ "1536px": "60vw", "1280px": "70vw", "1024px": "80vw", "896px": "90vw", "768px": "100vw" }}
          contentStyle={{ background: "hsl(45, 80%, 98%)" }}
          onHide={onHideDialog}
          closeOnEscape={true}
          header={header}
          headerStyle={{ background: "hsl(45, 80%, 98%)" }}
          footer={footer}
        >
          <TabelaEscolherReversao dados={movimentacoes} />
        </Dialog>
      </div>
    </>
  );
}
