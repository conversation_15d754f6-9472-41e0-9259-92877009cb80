import Breadcrumb from "@/components/Breadcrumb/Breadcrumb";
import TelaCarregando from "@/components/TelaCarregando";
import { useAuth } from "@/contexts/auth/useAuth";
import { chamarApi } from "@/helpers/api";
import { DescricaoSituacaoFimLimiteCartao, SeveridadeSituacaoFimLimiteCartao } from "@/helpers/constantes";
import { aplicarMascaraCartao, formatarMoeda } from "@/helpers/formatacao";
import { useMutationAlterarDocumentoFiscal } from "@/hooks/useMutationAlterarDocumentoFiscal";
import { useMutationCriarDocumentoFiscal } from "@/hooks/useMutationCriarDocumentoFiscal";
import { useMutationCriarReversao } from "@/hooks/useMutationCriarReversao";
import { useToast } from "@/hooks/useToast";
import { useExtratoPrestacaoContaStore } from "@/stores/ExtratoPrestacaoContaStore";
import telaPadrao from "@/tela-padrao.module.scss";
import { useSuspenseQuery } from "@tanstack/react-query";
import {
  type Credito,
  type CreditoCartao,
  type Movimentacao,
  Perfil,
  SituacaoFimLimiteCartao,
  TipoDocumentoFiscal,
  type UsuarioAutenticado,
} from "cpesc-shared";
import { endpoints } from "cpesc-shared/out/endpoints/main";
import type { PayloadCriarDocFiscal, PayloadCriarReversao } from "cpesc-shared/src/endpoints/documento-fiscal";
import { Button } from "primereact/button";
import { Card } from "primereact/card";
import { Checkbox } from "primereact/checkbox";
import { ConfirmDialog } from "primereact/confirmdialog";
import { Dialog } from "primereact/dialog";
import { InputText } from "primereact/inputtext";
import { Message } from "primereact/message";
import { Tag } from "primereact/tag";
import { Suspense, useEffect, useState } from "react";
import { Navigate, useParams } from "react-router-dom";
import estilo from "./ExtratoPrestacaoConta.module.scss";
import { type DadosFinalizarCredito, finalizarCredito, imprimirExtrato } from "./FinalizarCredito";
import { TabelaCredito } from "./Tabelas/TabelaCredito";
import { TabelaGastos } from "./Tabelas/TabelaGastos";

function montarDadosFinalizarCredito(
  credito: Credito,
  movimentacoes: Movimentacao[],
  usuario: UsuarioAutenticado,
): DadosFinalizarCredito {
  const { situacao, orgao, processo, ano, gastosSelecionados } = useExtratoPrestacaoContaStore.getState();

  if (situacao === null) {
    throw new Error("Situação do fim limite cartão não definida.");
  }

  const movimentacoesSelecionadas = movimentacoes.filter(m => gastosSelecionados.has(m.id));
  const totalSelecionados = getTotalSelecionados(credito, movimentacoesSelecionadas, situacao);
  return {
    credito,
    movimentacoes: movimentacoesSelecionadas,
    totalGastos: totalSelecionados,
    orgao,
    processo,
    ano: ano?.toString() ?? "",
    usuario,
    id: credito.fimLimiteCartao?.id,
  };
}

function getTotalSelecionados(
  credito: CreditoCartao,
  movimentacoesSelecionadas: Movimentacao[],
  situacao: SituacaoFimLimiteCartao,
): number {
  if (situacao !== SituacaoFimLimiteCartao.Finalizado) {
    return movimentacoesSelecionadas.reduce((total, m) => {
      return total + m.valorTransacaoReal;
    }, 0);
  }

  return credito.fimLimiteCartao ? credito.fimLimiteCartao.valorGasto : 0;
}

export default function ExtratoPrestacaoConta() {
  const { usuario } = useAuth();
  const params = useParams();
  const idCreditoSelecionado: number = parseInt(params.id ?? "0");
  const situacao = useExtratoPrestacaoContaStore(state => state.situacao);
  const creditoSelecionado = useExtratoPrestacaoContaStore(state => state.creditoSelecionado);
  const setSituacao = useExtratoPrestacaoContaStore(state => state.setSituacao);
  const setOrgao = useExtratoPrestacaoContaStore(state => state.setOrgao);
  const setProcesso = useExtratoPrestacaoContaStore(state => state.setProcesso);
  const setAno = useExtratoPrestacaoContaStore(state => state.setAno);
  const setCreditoSelecionado = useExtratoPrestacaoContaStore(state => state.setCreditoSelecionado);
  const setSelecaoGasto = useExtratoPrestacaoContaStore(state => state.setSelecaoGasto);
  const limparGastosSelecionados = useExtratoPrestacaoContaStore(state => state.limparGastosSelecionados);

  const [movimentacoes, setMovimentacoes] = useState<Movimentacao[]>([]);
  const [confirmacaoZeramento, setConfirmacaoZeramento] = useState(false);
  const [confirmacaoLiberarEdicao, setConfirmacaoLiberarEdicao] = useState(false);

  const [dialogConfirmaFinalizar, setDialogConfirmaFinalizar] = useState<boolean>(false);
  const [dialogConfirmaLiberarEdicao, setDialogConfirmaLiberarEdicao] = useState<boolean>(false);
  const [dialogConfirmaFinalizarEdicao, setDialogConfirmaFinalizarEdicao] = useState<boolean>(false);

  const [motivoLiberarEdicao, setMotivoLiberarEdicao] = useState<string>("");
  const toast = useToast();

  const { data, isFetching, refetch } = useSuspenseQuery({
    queryKey: ["/limite-cartao/gastos/", idCreditoSelecionado],
    queryFn: () => chamarApi(endpoints.buscarGastosCartao, { idCredito: idCreditoSelecionado.toString() }),
  });

  const mutationCriarNota = useMutationCriarDocumentoFiscal();
  const mutationAtualizarNota = useMutationAlterarDocumentoFiscal();
  const mutationCriarReversao = useMutationCriarReversao();

  useEffect(() => {
    if (!idCreditoSelecionado) {
      toast.show({
        severity: "error",
        summary: "Crédito inválido",
        detail: "Não é possível exibir extrato sem um crédito válido selecionado.",
        life: 5000,
      });
    }
  }, [idCreditoSelecionado, toast]);

  useEffect(() => {
    const situacaoFimLimite = data.fimLimiteCartao?.situacao ?? SituacaoFimLimiteCartao.Pendente;
    const novasMovimentacoes =
      situacaoFimLimite === SituacaoFimLimiteCartao.Finalizado ? (data.movimentacoes ?? []) : data.cartao.movimentacoes;

    limparGastosSelecionados();
    const reversoesVinculadas = novasMovimentacoes
      .filter(m => m.documentoFiscal?.tipodocumentofiscalId === TipoDocumentoFiscal.reversao)
      .map(m => m.id);

    for (const gasto of novasMovimentacoes) {
      if (
        situacaoFimLimite === SituacaoFimLimiteCartao.Finalizado ||
        reversoesVinculadas.includes(gasto.id) ||
        gasto.valorTransacaoReal === gasto.documentoFiscal?.valor
      ) {
        setSelecaoGasto(gasto.id, true);
      }
    }
    // Atualiza processo SGPE
    if (
      data.fimLimiteCartao &&
      [SituacaoFimLimiteCartao.Finalizado, SituacaoFimLimiteCartao.Edicao].includes(situacaoFimLimite)
    ) {
      setAno(isNaN(parseInt(data.fimLimiteCartao.anoSgpe)) ? null : parseInt(data.fimLimiteCartao.anoSgpe));
      setOrgao(data.fimLimiteCartao.orgaoSgpe);
      setProcesso(data.fimLimiteCartao.processoSgpe);
    } else if (creditoSelecionado?.id !== idCreditoSelecionado) {
      const anoCorrente = new Date().getFullYear();
      setAno(anoCorrente);
      setOrgao(data.cartao.portadorUnidadeAdministrativa.unidadeAdministrativa.unidadeGestora.siglaSgpe);
      setProcesso("");
    }

    // Atualiza movimentações
    setMovimentacoes(novasMovimentacoes);

    // Atualiza crédito selecionado por último
    setCreditoSelecionado(data);
    setSituacao(situacaoFimLimite);
    // eslint-disable-next-line react-hooks/exhaustive-deps -- re-executar somente quando a query for atualizada
  }, [data]);

  if (!idCreditoSelecionado) {
    return <Navigate to="/prestacao-contas/selecao-credito" replace />;
  }

  if (isFetching || mutationAtualizarNota.isPending || creditoSelecionado === null || situacao === null) {
    return <TelaCarregando />;
  }

  const tratarVinculoReversao = (reversao: PayloadCriarReversao): void => {
    mutationCriarReversao.mutate(reversao, {
      onSuccess: () => {
        void refetch();
        toast.show({
          severity: "success",
          summary: "Gasto vinculado a reversão.",
          detail: "O gasto foi vinculado com sucesso a reversão.",
          life: 5000,
        });
      },
      onError: error => {
        toast.show({
          severity: "error",
          summary: "Falha ao vincular a reversão",
          detail: "Não é possível vincular a reversão ao gasto.",
          life: 5000,
        });
        console.log("Erro ao criar reversão:", error);
      },
    });
  };

  const tratarVinculoNota = async (
    nota: PayloadCriarDocFiscal,
    atualizarTela?: boolean,
  ): Promise<Movimentacao | null> => {
    return new Promise(resolve => {
      mutationCriarNota.mutate(nota, {
        onSuccess: response => {
          if (response.documentoFiscalGerado) {
            const movimentacaoVinculada = movimentacoes.find(m => m.id === nota.movimentacaoId) ?? null;
            if (movimentacaoVinculada) {
              movimentacaoVinculada.documentoFiscal = response.documentoFiscalGerado;
              if (atualizarTela) {
                void refetch();
              }
              resolve(movimentacaoVinculada);
            } else {
              resolve(null);
            }
          } else {
            resolve(null);
          }
        },
        onError: error => {
          toast.show({
            severity: "error",
            summary: "Falha ao vincular o documento fiscal",
            detail: "Não é possível vincular o documento fiscal ao gasto.",
            life: 5000,
          });
          console.log("Erro ao criar documento fiscal:", error);
          resolve(null);
        },
      });
    });
  };

  const queryLiberarEdicao = async (motivo: string) => {
    try {
      const fimLimiteId: number = creditoSelecionado.fimLimiteCartao?.id ?? 0;
      if (!fimLimiteId) {
        throw new Error("fimLimiteCartao não está definido.");
      }
      const response = await chamarApi(
        endpoints.liberarEdicaoFimLimiteCartao,
        {},
        {
          fimLimiteCartaoId: fimLimiteId,
          motivo,
        },
      );

      if (!response) {
        throw new Error("Erro ao liberar a edição do Demonstrativo.");
      }

      void refetch();
    } catch (error) {
      console.error(error);
    }
  };

  const aceitarZeramento = async () => {
    setConfirmacaoZeramento(false);
    toast.show({
      severity: "success",
      summary: "Confirmado",
      detail: "Foi encaminhado o pedido de ZERAMENTO de limite do seu cartão.",
      life: 7000,
    });

    const dadosFinalizarCredito = montarDadosFinalizarCredito(creditoSelecionado, movimentacoes, usuario);

    if (await finalizarCredito(dadosFinalizarCredito)) {
      setSituacao(SituacaoFimLimiteCartao.Finalizado);
      void refetch();
    }
  };

  const rejeitarZeramento = () => {
    setConfirmacaoZeramento(false);
    toast.show({
      severity: "warn",
      summary: "AVISO",
      detail: "O crédito não foi finalizado e caso houver saldo, ainda será possível utilizá-lo.",
      life: 7000,
    });
  };

  const footerZeramento = (
    <div>
      <Button
        label="Não"
        icon="pi pi-times"
        onClick={() => {
          setDialogConfirmaFinalizar(false);
          rejeitarZeramento();
        }}
        className={telaPadrao.botaoSecundario}
      />
      <Button
        label={confirmacaoZeramento ? "Sim" : "Aguardando Aceite..."}
        icon="pi pi-check"
        onClick={() => {
          setDialogConfirmaFinalizar(false);
          void aceitarZeramento();
        }}
        disabled={!confirmacaoZeramento}
        className={telaPadrao.botaoPrincipal}
      />
    </div>
  );

  const aceitarLiberarEdicao = async () => {
    setConfirmacaoLiberarEdicao(false);

    await queryLiberarEdicao(motivoLiberarEdicao);

    toast.show({
      severity: "success",
      summary: "Confirmado",
      detail: "Demonstrativo para Prestação de Contas liberado para edição.",
      life: 5000,
    });
  };

  const rejeitarLiberarEdicao = () => {
    setConfirmacaoLiberarEdicao(false);
    toast.show({
      severity: "warn",
      summary: "Cancelado",
      detail: "Liberação para edição do Demonstrativo para Prestação de Contas não foi realizada.",
      life: 5000,
    });
  };

  const footerLiberarEdicao = (
    <div>
      <Button
        label="Não"
        icon="pi pi-times"
        onClick={() => {
          setDialogConfirmaLiberarEdicao(false);
          rejeitarLiberarEdicao();
        }}
        className={telaPadrao.botaoSecundario}
      />
      <Button
        label={
          confirmacaoLiberarEdicao
            ? motivoLiberarEdicao.length < 20
              ? "Preencha o motivo. "
              : "Sim"
            : "Aguardando ciência..."
        }
        icon="pi pi-check"
        onClick={() => {
          setDialogConfirmaLiberarEdicao(false);
          void aceitarLiberarEdicao();
        }}
        disabled={!confirmacaoLiberarEdicao || motivoLiberarEdicao.length < 20}
        className={telaPadrao.botaoPrincipal}
      />
    </div>
  );

  const aceitarFinalizarEdicao = async () => {
    const dadosFinalizarCredito = montarDadosFinalizarCredito(creditoSelecionado, movimentacoes, usuario);
    if (await finalizarCredito(dadosFinalizarCredito)) {
      setSituacao(SituacaoFimLimiteCartao.Finalizado);
      void refetch();
    }

    toast.show({
      severity: "success",
      summary: "Confirmado",
      detail: "A edição do Demonstrativo para Prestação de Contas foi realizada.",
      life: 5000,
    });
  };

  const rejeitarFinalizarEdicao = () => {
    toast.show({
      severity: "warn",
      summary: "Cancelado",
      detail: "A edição do Demonstrativo para Prestação de Contas não foi realizada.",
      life: 5000,
    });
  };

  return (
    <main className={telaPadrao.container}>
      <section className={telaPadrao.tituloTela}>
        <div className={estilo.tituloExtrato}>
          <h1>Demonstrativo para Prestação de Contas</h1>
          <Tag
            value={DescricaoSituacaoFimLimiteCartao[situacao]}
            severity={SeveridadeSituacaoFimLimiteCartao[situacao]}
          />
        </div>
        <Breadcrumb />
      </section>

      <section>
        <div className={telaPadrao.espaco} />
        <div className={estilo.declaracao}>
          <div className={estilo.info}>
            <label htmlFor="portador" className={estilo.label}>
              <span id="portador">Portador:</span>
              {creditoSelecionado.cartao.portadorUnidadeAdministrativa.portador.nome}
            </label>
            <label htmlFor="ug" className={estilo.label}>
              <span id="ug">Unidade Gestora:</span>
              {`${creditoSelecionado.cartao.portadorUnidadeAdministrativa.unidadeAdministrativa.unidadeGestora.descricao} - ${creditoSelecionado.cartao.portadorUnidadeAdministrativa.unidadeAdministrativa.unidadeGestora.nome}`}
            </label>
            <FormProcesso />
          </div>
          <GrandesNumeros credito={creditoSelecionado} movimentacoes={movimentacoes} />
        </div>

        <Suspense fallback={"Carregando dados..."}>
          <div className={telaPadrao.espaco} />
          <TabelaCredito credito={creditoSelecionado} />
          <div className={telaPadrao.espaco} />
          <TabelaGastos
            gastos={movimentacoes}
            situacaoFimLimite={situacao}
            onVincularNotaImportacao={tratarVinculoNota}
            onCancelarEdicaoNotaImportacao={() => void refetch()}
            onCriarNotaManual={nota => {
              void tratarVinculoNota(nota, true);
            }}
            onAlterarNota={nota => {
              mutationAtualizarNota.mutate(nota, {
                onSuccess: response => {
                  if (response.sucesso) {
                    void refetch();
                  }
                },
              });
            }}
            onExcluirNota={() => void refetch()}
            onVincularReversao={tratarVinculoReversao}
            onExcluirReversao={() => void refetch()}
          />
          <div className={telaPadrao.espaco} />
          <div className={estilo.containerBotao}>
            {SituacaoFimLimiteCartao.Finalizado === situacao && (
              <>
                {(usuario.perfil === Perfil.AdministradorCiasc ||
                  usuario.perfil === Perfil.AdministradorCpesc ||
                  usuario.perfil === Perfil.GestorSed ||
                  usuario.perfil === Perfil.Gestor) && (
                  <Button
                    className={telaPadrao.botaoSecundario}
                    label="Liberar Edição"
                    tooltip="Permite que o Portador possa editar os dados e gerar um novo Demonstrativo para Prestação de Contas."
                    tooltipOptions={{ position: "bottom" }}
                    onClick={() => {
                      setDialogConfirmaLiberarEdicao(true);
                    }}
                  ></Button>
                )}
                <Button
                  className={telaPadrao.botaoPrincipal}
                  label="Gerar Demonstrativo"
                  tooltip="Gera um arquivo PDF com o Demonstrativo para Prestação de Contas."
                  tooltipOptions={{ position: "bottom" }}
                  onClick={() => {
                    const dadosFinalizarCredito = montarDadosFinalizarCredito(
                      creditoSelecionado,
                      movimentacoes,
                      usuario,
                    );
                    void imprimirExtrato(dadosFinalizarCredito);
                  }}
                ></Button>
              </>
            )}

            {situacao === SituacaoFimLimiteCartao.Pendente && (
              <BotaoFinalizarCredito onClick={() => setDialogConfirmaFinalizar(true)} />
            )}
            {situacao === SituacaoFimLimiteCartao.Edicao && (
              <BotaoFinalizarCredito onClick={() => setDialogConfirmaFinalizarEdicao(true)} />
            )}
          </div>
        </Suspense>
      </section>

      <Dialog
        visible={dialogConfirmaFinalizar}
        onHide={() => setDialogConfirmaFinalizar(false)}
        header="AVISO"
        footer={footerZeramento}
        closable={false}
        className={estilo.dialogos}
      >
        <div className="flex flex-column w-full gap-3">
          <div className="flex align-items-center">
            <Checkbox
              inputId="confirmaZeramento"
              checked={confirmacaoZeramento}
              onChange={e => setConfirmacaoZeramento(e.checked ?? false)}
            />
            <label htmlFor="confirmaZeramento" className="ml-2">
              Estou ciente que vou finalizar o crédito de <b>{formatarMoeda(creditoSelecionado.valorCredito)}</b> do
              cartão <b>{aplicarMascaraCartao(creditoSelecionado.cartao.numero, true)}</b>.<br /> Se houver saldo, não
              será mais possível utilizá-lo.
            </label>
          </div>
        </div>
        <span>
          <br />
          Tem certeza que deseja prosseguir?
        </span>
      </Dialog>

      <Dialog
        visible={dialogConfirmaLiberarEdicao}
        onHide={() => setDialogConfirmaLiberarEdicao(false)}
        header="AVISO"
        footer={footerLiberarEdicao}
        className={estilo.dialogos}
        closable={false}
      >
        <div className="flex flex-column w-full gap-3">
          <div className="flex align-items-center">
            <Checkbox
              inputId="confirmaCancelamento"
              checked={confirmacaoLiberarEdicao}
              onChange={e => setConfirmacaoLiberarEdicao(e.checked ?? false)}
            />
            <label htmlFor="confirmaCancelamento" className="ml-2">
              Estou ciente que vou liberar para edição o Demonstrativo para Prestação de Contas do crédito do portador:{" "}
              {creditoSelecionado.cartao.portadorUnidadeAdministrativa.portador.nome} de{" "}
              <b>{formatarMoeda(creditoSelecionado.valorCredito)}</b> do cartão{" "}
              <b>{aplicarMascaraCartao(creditoSelecionado.cartao.numero, true)}</b>.
            </label>
          </div>
          <div>
            <span>Qualquer uso do demonstrativo atual precisará ser revisto.</span>
          </div>
          <div>
            <br />
            <label htmlFor="textoConfirmacao">
              Escreva uma justificativa para liberar para edição este demonstrativo:
            </label>
          </div>
          <div>
            <InputText
              id="textoConfirmacao"
              name="motivo"
              className="w-full"
              placeholder="Digite uma justificativa com, no mínimo 20 caracteres..."
              onChange={e => setMotivoLiberarEdicao(e.target.value)}
              value={motivoLiberarEdicao}
            />
          </div>
          <br />
          <span>Tem certeza que deseja liberar para edição este Demonstrativo para Prestação de Contas?</span>
          <div className={estilo.containerBotao}></div>
        </div>
      </Dialog>

      <ConfirmDialog
        group="declarative"
        visible={dialogConfirmaFinalizarEdicao}
        onHide={() => setDialogConfirmaFinalizarEdicao(false)}
        closable={false}
        header="Confirmação"
        message="Confirma a edição do crédito? Lembre-se que o saldo já foi zerado anteriomente."
        // eslint-disable-next-line @typescript-eslint/no-misused-promises -- executar função async
        accept={aceitarFinalizarEdicao}
        acceptClassName={telaPadrao.botaoPrincipal}
        acceptLabel="Sim"
        acceptIcon="pi pi-check"
        reject={rejeitarFinalizarEdicao}
        rejectLabel="Não"
        rejectClassName={telaPadrao.botaoSecundario}
        rejectIcon="pi pi-times"
        style={{ width: "25vw" }}
        breakpoints={{ "1100px": "75vw", "960px": "100vw" }}
      />
    </main>
  );
}

function FormProcesso() {
  const situacao = useExtratoPrestacaoContaStore(state => state.situacao);
  const orgao = useExtratoPrestacaoContaStore(state => state.orgao);
  const processo = useExtratoPrestacaoContaStore(state => state.processo);
  const ano = useExtratoPrestacaoContaStore(state => state.ano);
  const setOrgao = useExtratoPrestacaoContaStore(state => state.setOrgao);
  const setProcesso = useExtratoPrestacaoContaStore(state => state.setProcesso);
  const setAno = useExtratoPrestacaoContaStore(state => state.setAno);
  const somenteLeitura = situacao === SituacaoFimLimiteCartao.Finalizado;

  return (
    <label className={estilo.label}>
      <span>Processo:</span>
      <div className={`${estilo.caixainput} p-inputgroup`}>
        <InputText
          placeholder="Órgão"
          value={orgao}
          onChange={e => setOrgao(e.target.value.toUpperCase())}
          disabled={somenteLeitura}
          required
        />
        <InputText
          placeholder="Processo"
          value={processo}
          onChange={e => setProcesso(e.target.value.replace(/\D/g, ""))}
          disabled={somenteLeitura}
          required
        />
        <InputText
          placeholder="Ano"
          value={ano?.toString() ?? ""}
          onChange={e => {
            const novoAno = parseInt(e.target.value);

            if (isNaN(novoAno)) {
              setAno(null);
              return;
            }

            setAno(novoAno);
          }}
          disabled={somenteLeitura}
          required
        />
      </div>
    </label>
  );
}

function GrandesNumeros(props: { credito: CreditoCartao; movimentacoes: Movimentacao[] }) {
  const situacao = useExtratoPrestacaoContaStore(state => state.situacao);
  const gastosSelecionados = useExtratoPrestacaoContaStore(state => state.gastosSelecionados);

  if (situacao === null) {
    return null;
  }

  const movimentacoesSelecionadas = props.movimentacoes.filter(m => gastosSelecionados.has(m.id));
  const totalSelecionados = getTotalSelecionados(props.credito, movimentacoesSelecionadas, situacao);

  const ptcard = {
    root: {
      className: estilo.cartao,
    },
    content: {
      className: estilo.cartaoContent,
    },
  };

  return (
    <div className={estilo.grupoCartao}>
      <Card pt={ptcard} subTitle="Crédito">
        <p>{formatarMoeda(props.credito.valorCredito)}</p>
      </Card>
      <Card pt={ptcard} subTitle="Total de Gastos">
        <p>{formatarMoeda(totalSelecionados)}</p>
      </Card>
      <Card pt={ptcard} subTitle="Saldo a Devolver">
        <p>{formatarMoeda(props.credito.valorCredito - totalSelecionados)}</p>
      </Card>
    </div>
  );
}

function BotaoFinalizarCredito(props: { onClick: () => void }) {
  const habilitado = useExtratoPrestacaoContaStore(state => {
    const camposPreenchidos = state.orgao !== "" && state.processo !== "" && state.ano !== null;

    if (!camposPreenchidos) {
      return false;
    }

    const movimentacoes = state.creditoSelecionado?.cartao.movimentacoes ?? [];
    if (state.creditoSelecionado?.temMaisLimites === false) {
      for (const movimentacao of movimentacoes) {
        if (
          movimentacao.documentoFiscal === null ||
          movimentacao.documentoFiscal.valor !== movimentacao.valorTransacaoReal
        ) {
          //TODO por em outro lugar
          return false;
        }
      }

      if (state.gastosSelecionados.size !== movimentacoes.length) {
        return false;
      }
    }

    return true;
  });

  const temMaisLimites = useExtratoPrestacaoContaStore(state => state.creditoSelecionado?.temMaisLimites);
  const temMovimentacoes = useExtratoPrestacaoContaStore(state => {
    const movimentacoes = state.creditoSelecionado?.cartao.movimentacoes ?? [];
    return movimentacoes.length > 0;
  });
  const situacao = useExtratoPrestacaoContaStore(state => state.situacao);
  const mensagem = temMaisLimites
    ? "Você deve preencher Órgão, Processo e Ano para finalizar o crédito. VOCÊ POSSUI MAIS DE UM CRÉDITO ABERTO NESTE PERÍODO."
    : temMovimentacoes
      ? "Você deve preencher Órgão, Processo e Ano, vincular todos os documentos fiscais e selecionar todos os gastos para finalizar o crédito."
      : "Você deve preencher Órgão, Processo e Ano para finalizar o crédito.";

  return (
    <>
      <Message severity="warn" text={mensagem} style={{ flex: 1, alignSelf: "center" }} />
      <Button
        className={telaPadrao.botaoPrincipal}
        tooltip={
          situacao === SituacaoFimLimiteCartao.Pendente
            ? "Finaliza o crédito e gera o Demonstrativo para Prestação de Contas."
            : "Finaliza a edição do Demonstrativo para Prestação de Contas."
        }
        tooltipOptions={{ position: "bottom" }}
        style={{ flex: "0 0 auto" }}
        label={situacao === SituacaoFimLimiteCartao.Pendente ? "Finalizar Crédito" : "Finalizar Edição"}
        onClick={props.onClick}
        disabled={!habilitado}
      />
    </>
  );
}
