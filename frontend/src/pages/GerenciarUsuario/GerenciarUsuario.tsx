import Breadcrumb from "@/components/Breadcrumb/Breadcrumb";
import ContainerQuery from "@/components/ContainerQuery";
import SpinnerPrestadores from "@/components/SpinnerCpesc";
import TextoErro from "@/components/TextoErro";
import { chamar<PERSON>pi } from "@/helpers/api";
import telaPadrao from "@/tela-padrao.module.scss";
import { useQuery } from "@tanstack/react-query";
import { endpoints } from "cpesc-shared/out/endpoints/main";
import { Button } from "primereact/button";
import { Card } from "primereact/card";
import { InputSwitch } from "primereact/inputswitch";
import { InputText } from "primereact/inputtext";
import { MultiSelect, type MultiSelectChangeEvent } from "primereact/multiselect";
import { useState } from "react";

interface Opcao {
  descricao: string;
  id: string;
}

export default function GerenciarUsuario() {
  const { data, isPending, error } = useQuery({
    queryKey: ["unidadegestora"],
    queryFn: () => chamarApi(endpoints.buscarUnidadesGestoras, {}),
  });
  const [email, setEmail] = useState("");
  const [ativo, setAtivo] = useState(true);

  const [ugVisivel, setUgVisivel] = useState(true);

  const tipoAcesso = [
    { descricao: "Prestadores", id: "acessoPrestadores" },
    { descricao: "APP", id: "acessoApp" },
    { descricao: "Sequestros Judiciais", id: "acessoSequestros" },
    { descricao: "Administrador", id: "acessoAdmin" },
  ];

  const [tipoAcessoSelecionado, setTipoAcessoSelecionado] = useState<Opcao[]>([]);
  const [ugSelecionada, setUgSelecionada] = useState<Opcao[]>([]);

  const pt = {
    root: {
      style: { maxWidth: "80vw" },
    },
  };

  const opcoes =
    data?.map((ug): Opcao => {
      return { descricao: ug.codigo + " - " + ug.descricao, id: ug.id.toString() };
    }) ?? [];

  const controlaTipoAcesso = (e: MultiSelectChangeEvent) => {
    const selecaoTipoAcesso = e.value as Opcao[];
    if (selecaoTipoAcesso.find(u => u.id === "acessoAdmin")) {
      setUgVisivel(false);
      setUgSelecionada([]);
      setTipoAcessoSelecionado(selecaoTipoAcesso.filter(u => u.id === "acessoAdmin"));
    } else {
      setTipoAcessoSelecionado(selecaoTipoAcesso);
      setUgVisivel(true);
    }
  };

  const permiteSalvar =
    email !== "" &&
    tipoAcessoSelecionado.length > 0 &&
    (ugSelecionada.length > 0 || tipoAcessoSelecionado.find(u => u.id === "acessoAdmin"));

  function salvar(event: React.MouseEvent<HTMLButtonElement>): void {
    if (!permiteSalvar) {
      return;
    }
    console.log("Método salvar", event);
  }

  function cancelar(event: React.MouseEvent<HTMLButtonElement>): void {
    console.log("Método Cancelar", event);
    return;
  }

  return (
    <main className={telaPadrao.container}>
      <section className={telaPadrao.tituloTela}>
        <h1>Gerenciar Usuário</h1>
        <Breadcrumb />
      </section>
      <section>
        <div className={telaPadrao.espaco} />
        <div className="flex flex-row justify-content-between">
          <div className="flex gap-2">
            <div className="flex justify-content-around align-content-end flex-column column-gap-4">
              <div className="flex align-items-end flex-column gap-4">
                <b>
                  <label htmlFor="email">E-mail:</label>
                </b>
                <b>Ativo:</b>
                <b>Permissão de acesso:</b>
                {ugVisivel && <b>Unidade gestora:</b>}
              </div>
            </div>
            <div className="flex flex-column gap-2">
              <ContainerQuery
                carregando={isPending}
                fallbackCarregamento={<SpinnerPrestadores />}
                erro={error}
                fallbackErro={<TextoErro mensagem="Falha ao obter dados" />}
              >
                <InputText
                  required
                  placeholder="Informe o e-mail"
                  value={email}
                  onChange={v => setEmail(v.target.value)}
                />
                <InputSwitch checked={ativo} onChange={e => setAtivo(e.value)} id="inputAtivo" />
                <MultiSelect
                  pt={pt}
                  value={tipoAcessoSelecionado}
                  onChange={controlaTipoAcesso}
                  options={tipoAcesso}
                  optionLabel="descricao"
                  required
                  placeholder="Selecione a permissão de acesso"
                  display="chip"
                  id="inputAcesso"
                />
                {ugVisivel && (
                  <MultiSelect
                    pt={pt}
                    value={ugSelecionada}
                    options={opcoes}
                    optionLabel="descricao"
                    placeholder="Selecione a unidade gestora"
                    onChange={e => setUgSelecionada(e.value as Opcao[])}
                    display="chip"
                    filter
                  />
                )}
              </ContainerQuery>
            </div>
          </div>
        </div>
        <div className={telaPadrao.espaco}></div>
        <Card className={telaPadrao.painel}>
          <div className="flex gap-2">
            <div className="flex align-items-end flex-row gap-2">
              <Button label="Salvar" onClick={salvar} disabled={!permiteSalvar} />
              <Button label="Cancelar" onClick={cancelar} />
            </div>
          </div>
        </Card>
      </section>
    </main>
  );
}
