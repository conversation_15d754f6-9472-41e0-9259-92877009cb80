@use "@/variables";

.filtros {
  display: flex;
  gap: 2rem;
  margin-bottom: 2rem;
  padding: 1rem 2rem;
  background: var(--cor-fundo-padrao);
  border-radius: calc(var(--border-radius-padrao) * 2);
  border: 1px solid var(--cor-verde-background);
  justify-content: space-between;
  flex-wrap: wrap;

  @media (max-width: variables.$limite-largura-tela-media-up) {
    gap: 1rem;
    padding: 1rem;
  }
}

.filtroItem {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;

  label {
    font-weight: 600;
    font-size: 0.9rem;
  }
}

.estatisticas {
  margin-bottom: 2rem;
}

.cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 2rem;
}

.cardStat {
  text-align: center;

  .statContent {
    h3 {
      margin: 0 0 1rem 0;
      color: var(--cor-texto-destacado);
      font-size: 1rem;
    }

    .statNumber {
      font-size: 2.5rem;
      font-weight: bold;
      color: var(--primary-600);
    }
  }
}

.graficos {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.graficoItem {
  min-height: 400px;
}

.tabelas {
  margin-bottom: 2rem;
}

.tabelaRotas {
  button {
    all: revert-layer;
  }
}
