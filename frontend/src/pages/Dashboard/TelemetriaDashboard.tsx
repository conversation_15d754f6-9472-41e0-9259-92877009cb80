import Breadcrumb from "@/components/Breadcrumb/Breadcrumb";
import ContainerQuery from "@/components/ContainerQuery";
import SpinnerCpesc from "@/components/SpinnerCpesc";
import TextoErro from "@/components/TextoErro";
import { chamarApi } from "@/helpers/api";
import telaPadrao from "@/tela-padrao.module.scss";
import { useQuery } from "@tanstack/react-query";
import { endpoints } from "cpesc-shared/out/endpoints/main";
import { Button } from "primereact/button";
import { Calendar } from "primereact/calendar";
import { Card } from "primereact/card";
import { Chart } from "primereact/chart";
import { Column } from "primereact/column";
import { DataTable } from "primereact/datatable";
import { Dropdown } from "primereact/dropdown";
import { useState } from "react";
import estilos from "./TelemetriaDashboard.module.scss";

interface RotaDemorada {
  rota: string;
  tempoMedio: number;
  totalConsultas: number;
}

export default function TelemetriaDashboard() {
  const [limite, setLimite] = useState(10);
  const [dataInicial, setDataInicial] = useState<Date | null>(null);
  const [dataFinal, setDataFinal] = useState<Date | null>(null);

  const documentStyle = getComputedStyle(document.documentElement);

  const buildQueryString = () => {
    const params = new URLSearchParams();
    params.append("limite", limite.toString());
    if (dataInicial) {
      params.append("dataInicial", dataInicial.toISOString().split("T")[0]);
    }
    if (dataFinal) {
      params.append("dataFinal", dataFinal.toISOString().split("T")[0]);
    }
    return `?${params.toString()}`;
  };

  const {
    data: usuariosAtivos,
    isPending: loadingUsuarios,
    error: errorUsuarios,
  } = useQuery({
    queryKey: [limite, dataInicial, dataFinal, "usuarios"],
    queryFn: () => chamarApi(endpoints.usuariosMaisAtivos, {}, buildQueryString()),
  });

  const {
    data: rotasAcessadas,
    isPending: loadingRotas,
    error: errorRotas,
  } = useQuery({
    queryKey: [limite, dataInicial, dataFinal, "rotas"],
    queryFn: () => chamarApi(endpoints.rotasMaisAcessadas, {}, buildQueryString()),
  });

  const {
    data: rotasDemoradas,
    isPending: loadingDemoradas,
    error: errorDemoradas,
  } = useQuery({
    queryKey: [limite, dataInicial, dataFinal, "demoradas"],
    queryFn: () => chamarApi(endpoints.rotasMaisDemoradas, {}, buildQueryString()),
  });

  const {
    data: estatisticas,
    isPending: loadingStats,
    error: errorStats,
  } = useQuery({
    queryKey: [limite, dataInicial, dataFinal, "stats"],
    queryFn: () => chamarApi(endpoints.estatisticasGerais, {}, buildQueryString()),
  });

  const limiteOptions = [5, 10, 20, 50].map(n => ({ label: n.toString(), value: n }));

  const chartOptionsBar = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: { legend: { display: false } },
    scales: { y: { beginAtZero: true } },
  };

  const chartOptionsPie = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: { legend: { position: "bottom" as const } },
  };

  const createUsuariosChartData = (data: typeof usuariosAtivos) => ({
    labels: data?.map(u => u.nome) ?? [],
    datasets: [
      {
        label: "Acessos",
        data: data?.map(u => u.totalAcessos) ?? [],
        backgroundColor: documentStyle.getPropertyValue("--primary-600"),
      },
    ],
  });

  const createRotasChartData = (data: typeof rotasAcessadas) => ({
    labels: data?.map(r => r.rota.split("/").pop() ?? "Unknown") ?? [],
    datasets: [
      {
        data: data?.map(r => r.totalAcessos) ?? [],
        backgroundColor: [
          documentStyle.getPropertyValue("--pink-400"),
          documentStyle.getPropertyValue("--blue-400"),
          documentStyle.getPropertyValue("--yellow-400"),
          documentStyle.getPropertyValue("--teal-300"),
          documentStyle.getPropertyValue("--orange-400"),
          documentStyle.getPropertyValue("--primary-color"),
          documentStyle.getPropertyValue("--red-400"),
          documentStyle.getPropertyValue("--gray-400"),
          documentStyle.getPropertyValue("--indigo-400"),
          documentStyle.getPropertyValue("--purple-400"),
          documentStyle.getPropertyValue("--cyan-300"),
        ],
      },
    ],
  });

  const formatTempo = (tempo: number) => `${Math.round(tempo)}ms`;

  const clearFilters = () => {
    setDataInicial(null);
    setDataFinal(null);
    setLimite(10);
  };

  const rotaLinkTemplate = (data: RotaDemorada) => {
    const handleRotaClick = () => {
      window.open(data.rota, "_blank");
    };

    return (
      <Button
        label={data.rota}
        link
        onClick={handleRotaClick}
        className="p-0 text-left"
        style={{ textDecoration: "underline", color: "#007ad9" }}
      />
    );
  };

  return (
    <>
      <section className={telaPadrao.tituloTela}>
        <h1>Dashboard de Telemetria</h1>
        <Breadcrumb />
      </section>
      <section className={estilos.filtros}>
        <div className={estilos.filtroItem}>
          <label>Limite de resultados:</label>
          <Dropdown
            value={limite}
            options={limiteOptions}
            onChange={e => setLimite(e.value as number)}
            placeholder="Selecione"
          />
        </div>

        <div className={estilos.filtroItem}>
          <label>Data inicial:</label>
          <Calendar
            value={dataInicial}
            onChange={e => setDataInicial(e.value ?? null)}
            placeholder="Selecione a data"
            dateFormat="dd/mm/yy"
            showIcon
          />
        </div>

        <div className={estilos.filtroItem}>
          <label>Data final:</label>
          <Calendar
            value={dataFinal}
            onChange={e => setDataFinal(e.value ?? null)}
            placeholder="Selecione a data"
            dateFormat="dd/mm/yy"
            showIcon
          />
        </div>

        <div className={estilos.filtroItem}>
          <Button label="Limpar Filtros" icon="pi pi-times" onClick={clearFilters} className="p-button-outlined" />
        </div>
      </section>

      <section className={estilos.estatisticas}>
        <ContainerQuery
          carregando={loadingStats}
          fallbackCarregamento={<SpinnerCpesc />}
          erro={errorStats}
          fallbackErro={<TextoErro mensagem="Erro ao carregar estatísticas" />}
        >
          <div className={estilos.cards}>
            <Card className={estilos.cardStat}>
              <div className={estilos.statContent}>
                <h3>Total de Eventos</h3>
                <span className={estilos.statNumber}>{estatisticas?.totalEventos.toLocaleString() ?? 0}</span>
              </div>
            </Card>
            <Card className={estilos.cardStat}>
              <div className={estilos.statContent}>
                <h3>Usuários Ativos</h3>
                <span className={estilos.statNumber}>{estatisticas?.totalUsuarios.toLocaleString() ?? 0}</span>
              </div>
            </Card>
          </div>
        </ContainerQuery>
      </section>

      <section className={estilos.graficos}>
        <div className={estilos.graficoItem}>
          <Card title="Usuários Mais Ativos">
            <ContainerQuery
              carregando={loadingUsuarios}
              fallbackCarregamento={<SpinnerCpesc />}
              erro={errorUsuarios}
              fallbackErro={<TextoErro mensagem="Erro ao carregar dados dos usuários" />}
            >
              {usuariosAtivos && usuariosAtivos.length > 0 ? (
                <Chart
                  type="bar"
                  data={createUsuariosChartData(usuariosAtivos)}
                  options={chartOptionsBar}
                  height="300px"
                />
              ) : (
                <p>Nenhum dado disponível</p>
              )}
            </ContainerQuery>
          </Card>
        </div>

        <div className={estilos.graficoItem}>
          <Card title="Rotas Mais Acessadas">
            <ContainerQuery
              carregando={loadingRotas}
              fallbackCarregamento={<SpinnerCpesc />}
              erro={errorRotas}
              fallbackErro={<TextoErro mensagem="Erro ao carregar dados das rotas" />}
            >
              {rotasAcessadas && rotasAcessadas.length > 0 ? (
                <Chart
                  type="pie"
                  data={createRotasChartData(rotasAcessadas)}
                  options={chartOptionsPie}
                  height="300px"
                />
              ) : (
                <p>Nenhum dado disponível</p>
              )}
            </ContainerQuery>
          </Card>
        </div>
      </section>

      <section className={estilos.tabelas}>
        <Card title="Rotas com Maior Tempo de Resposta">
          <ContainerQuery
            carregando={loadingDemoradas}
            fallbackCarregamento={<SpinnerCpesc />}
            erro={errorDemoradas}
            fallbackErro={<TextoErro mensagem="Erro ao carregar dados de performance" />}
          >
            <DataTable
              value={rotasDemoradas ?? []}
              responsiveLayout="stack"
              breakpoint="640px"
              emptyMessage="Nenhum dado disponível"
              paginator
              rows={10}
              className={estilos.tabelaRotas}
            >
              <Column field="rota" header="Rota" body={rotaLinkTemplate} sortable />
              <Column
                field="tempoMedio"
                header="Tempo Médio"
                body={(data: RotaDemorada) => formatTempo(data.tempoMedio)}
                sortable
              />
              <Column
                field="totalConsultas"
                header="Total de Consultas"
                body={(data: RotaDemorada) => data.totalConsultas.toLocaleString()}
                sortable
              />
            </DataTable>
          </ContainerQuery>
        </Card>
      </section>
    </>
  );
}
