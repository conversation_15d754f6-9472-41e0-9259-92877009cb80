import TextoErro from "@/components/TextoErro";
import { useAuth } from "@/contexts/auth/useAuth";
import telaPadrao from "@/tela-padrao.module.scss";
import { Perfil } from "cpesc-shared";
import TelemetriaDashboard from "../Dashboard/TelemetriaDashboard";

export default function VisaoGeral() {
  const { usuario } = useAuth();
  // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
  const autenticado: boolean = usuario !== null;

  return (
    <main className={telaPadrao.container}>
      {!autenticado && <TextoErro mensagem="Usuário não autenticado." />}
      {autenticado && usuario.perfil === Perfil.AdministradorCiasc && <TelemetriaDashboard />}
      {autenticado &&
        (usuario.perfil === Perfil.AdministradorCpesc ||
          usuario.perfil === Perfil.GestorSed ||
          usuario.perfil === Perfil.Gestor) && <TextoErro mensagem={`Bem vindo ${usuario.nome}.`} />}
    </main>
  );
}
