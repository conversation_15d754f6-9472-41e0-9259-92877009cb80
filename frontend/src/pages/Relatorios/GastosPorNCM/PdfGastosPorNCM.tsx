import { Document, Image, Page, Text, View } from "@react-pdf/renderer";
import React from "react";
import brasao from "../../../assets/brasaodearmasSC.jpg";
import { styles } from "./PdfGastosPorNCM.estilos";

// Tipagem esperada
export interface DocumentoFiscalItemPdf {
  id: string;
  cdunidadegestora: number;
  nmunidadegestora: string;
  tipo: string;
  ncm: string | null;
  descricao: string | null;
  quantidade: number | null;
  valor: number | null;
}

interface Props {
  itens: DocumentoFiscalItemPdf[];
}

export function PdfGastosPorNCM({ itens }: Props) {
  return (
    <Document>
      <Page size="A4" style={styles.pagina}>
        <View fixed>
          <View style={styles.secao}>
            <Image src={brasao} style={styles.brasao} />
            <View>
              <Text style={styles.titulo}>Estado de Santa Catarina</Text>
              <Text style={styles.subTitulo}>
                Relatorio de Movimentacão por NCM{"\n"}
                Cartão de Pagamentos do Estado de Santa Catarina - CPESC
              </Text>
            </View>
          </View>
        </View>
        <View style={styles.table}>
          {itens.map((g, i) => {
            return (
              <React.Fragment key={i}>
                {g.cdunidadegestora !== itens[i - 1]?.cdunidadegestora && (
                  <>
                    <View style={styles.quebraPorId}>
                      <Text style={styles.tituloSecao}>
                        Unidade Gestora: {g.cdunidadegestora} - {g.nmunidadegestora}
                      </Text>
                    </View>
                    <View style={[styles.row, styles.headerRow]}>
                      <View style={[styles.bordaPrimeiroCabecalho, styles.w12]}>
                        <Text>NCM</Text>
                      </View>
                      <View style={[styles.bordaCabecalho, styles.w60]}>
                        <Text>Descrição</Text>
                      </View>
                      <View style={[styles.bordaCabecalho, styles.w18]}>
                        <Text>Quantidade</Text>
                      </View>
                      <View style={[styles.bordaCabecalho, styles.w10]}>
                        <Text>Valor</Text>
                      </View>
                    </View>
                  </>
                )}
                <View key={i} style={[styles.row, i % 2 === 0 ? styles.backWhite : styles.backWhite]}>
                  <View style={[styles.bordaPrimeiraCelula, styles.w12, styles.centralized]}>
                    <Text>{g.ncm}</Text>
                  </View>
                  <View style={[styles.bordaCelula, styles.w60, { paddingLeft: 2 }]}>
                    <Text>{g.descricao}</Text>
                  </View>
                  <View style={[styles.bordaCelula, styles.w18, styles.value]}>
                    <Text>{aplicarMascaraValor(g.quantidade ?? 0, 4)}</Text>
                  </View>
                  <View style={[styles.bordaCelula, styles.w10, styles.value]}>
                    <Text>{aplicarMascaraValor(g.valor ?? 0)}</Text>
                  </View>
                </View>
              </React.Fragment>
            );
          })}
        </View>
        <Text
          fixed
          style={styles.footerPage}
          render={({ pageNumber, totalPages }) => `Página ${pageNumber} de ${totalPages}`}
        />
      </Page>
    </Document>
  );
}

export function aplicarMascaraValor(numero: number, casasDecimais = 2): string {
  return new Intl.NumberFormat("pt-BR", {
    minimumFractionDigits: casasDecimais,
    maximumFractionDigits: casasDecimais,
  }).format(numero);
}
