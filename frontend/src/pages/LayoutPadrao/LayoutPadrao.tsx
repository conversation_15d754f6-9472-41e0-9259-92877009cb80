import AcessoNegado from "@/components/AcessoNegado";
import Cabecalho from "@/components/Cabecalho/Cabecalho";
import Rodape from "@/components/Rodape/Rodape";
import TelaCarregando from "@/components/TelaCarregando";
import TelaErro from "@/components/TelaErro";
import { AuthProvider } from "@/contexts/auth/AuthProvider";
import { useToast } from "@/hooks/useToast";
import { HttpStatus } from "cpesc-shared";
import { ConfirmDialog } from "primereact/confirmdialog";
import { Toast } from "primereact/toast";
import { Suspense } from "react";
import { ErrorBoundary } from "react-error-boundary";
import { Outlet } from "react-router-dom";
import styles from "./LayoutPadrao.module.scss";

export default function LayoutPadrao() {
  const toast = useToast();

  return (
    <AuthProvider>
      <div className={styles.layout}>
        <Cabecalho />
        <Toast ref={toast.ref} />
        <ConfirmDialog />

        <ErrorBoundary
          fallbackRender={props => {
            const error = props.error as Error;

            if (
              "status" in error &&
              (error.status === HttpStatus.UNAUTHORIZED || error.status === HttpStatus.FORBIDDEN)
            ) {
              return <AcessoNegado />;
            }

            return <TelaErro mensagem={error.message} />;
          }}
        >
          <Suspense fallback={<TelaCarregando />}>
            <Outlet />
          </Suspense>
        </ErrorBoundary>

        <Rodape />
      </div>
    </AuthProvider>
  );
}
