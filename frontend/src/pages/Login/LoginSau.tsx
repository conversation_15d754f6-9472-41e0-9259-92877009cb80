import SpinnerCpesc from "@/components/SpinnerCpesc";
import { useOptionalAuth } from "@/contexts/auth/useAuth";
import telaPadrao from "@/tela-padrao.module.scss";
import classNames from "classnames";
import type { PayloadLoginSau } from "cpesc-shared";
import { Button } from "primereact/button";
import { useEffect, useRef } from "react";
import estilos from "./Login.module.scss";

export function LoginSau(props: { onVoltar: () => void }) {
  const janelaAberta = useRef<Window>(null);
  const auth = useOptionalAuth();

  useEffect(() => {
    if (janelaAberta.current === null) {
      janelaAberta.current = window.open(
        "/proxy/sau",
        "_blank",
        "width=600,height=400,popup=yes,status=no,location=no",
      );

      if (janelaAberta.current === null) {
        return;
      }
    }

    const callbackMensagem = (event: MessageEvent): void => {
      if (event.origin !== location.origin || event.source !== janelaAberta.current) {
        return;
      }

      if ("rota" in event.data && "ticket" in event.data) {
        const dados = event.data as { rota: string; ticket: string };
        const payload: PayloadLoginSau = {
          tipo: "sau",
          rota: dados.rota,
          ticket: dados.ticket,
        };

        auth.login(payload);
      }
    };

    window.addEventListener("message", callbackMensagem);

    return () => window.removeEventListener("message", callbackMensagem);
  }, [auth]);

  return (
    <section className={classNames(estilos.cardLogin, "flex flex-column align-items-center")}>
      <div className={estilos.label}>Autenticação via SAU</div>

      {auth.isLoginPending ? (
        <>
          <div className="my-4">Realizando autenticação...</div>
          <SpinnerCpesc />
        </>
      ) : (
        <>
          <div className="my-4">Uma janela de autenticação para o SAU será aberta.</div>

          <Button
            className={classNames(telaPadrao.botaoSecundario, "m-0")}
            label="Voltar"
            type="button"
            onClick={props.onVoltar}
          />
        </>
      )}
    </section>
  );
}
