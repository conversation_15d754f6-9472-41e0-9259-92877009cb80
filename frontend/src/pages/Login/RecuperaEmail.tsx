import SpinnerCpesc from "@/components/SpinnerCpesc";
import { chamarApi } from "@/helpers/api";
import { useToast } from "@/hooks/useToast";
import telaPadrao from "@/tela-padrao.module.scss";
import { useMutation } from "@tanstack/react-query";
import classNames from "classnames";
import { endpoints } from "cpesc-shared/out/endpoints/main";
import { Button } from "primereact/button";
import { InputMask } from "primereact/inputmask";
import { useState } from "react";
import estilos from "./Login.module.scss";

export default function RecuperaEmail(props: { onVoltar: () => void }) {
  const [cpf, setCpf] = useState("");
  const [cartaoCpesc, setCartaoCpesc] = useState("");
  const [emailRegistrado, setEmailRegistrado] = useState<string | null>(null);
  const toast = useToast();

  const mutationRecuperarEmail = useMutation({
    mutationFn: async (dados: { cpf: string; cartaoCpesc: string }) => {
      const cpfLimpo = dados.cpf.replace(/\D/g, "");
      const cartaoLimpo = dados.cartaoCpesc.replace(/\D/g, "");

      const json = await chamarApi(
        endpoints.recuperarEmail,
        {},
        {
          cpf: cpfLimpo,
          cartao: cartaoLimpo,
        },
      );

      return json.email;
    },
  });

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();

    mutationRecuperarEmail.mutate(
      { cpf, cartaoCpesc },
      {
        onSuccess: email => {
          sessionStorage.setItem("cpesc-email", email);
          setEmailRegistrado(email);
        },
        onError: error => {
          toast.show({
            severity: "error",
            summary: error.message,
            life: 7000,
          });
        },
      },
    );
  };

  return (
    <section className={estilos.cardLogin}>
      <form className={estilos.formulario} onSubmit={handleSubmit}>
        <span className={estilos.label}>Recuperar e-mail</span>

        <div className={estilos.tabelaRecuperarEmail}>
          <div className={estilos.campo}>
            <label htmlFor="cpf" className="text-right">
              CPF:
            </label>

            <InputMask
              id="cpf"
              mask="999.999.999-99"
              value={cpf}
              onChange={e => setCpf((e.target as HTMLInputElement).value)}
              className={estilos.inputShort}
              placeholder="___.___.___-__"
              maxLength={14} // Conforme a máscara com pontos e hífen
              autoFocus
              disabled={mutationRecuperarEmail.isPending || mutationRecuperarEmail.isSuccess}
            />
          </div>

          <div className={estilos.campo}>
            <label htmlFor="cartaoCpesc" className="text-right">
              Número do Cartão CPESC:
            </label>

            <InputMask
              id="cartaoCpesc"
              mask="9999 9999 9999 9999"
              value={cartaoCpesc}
              onChange={e => setCartaoCpesc((e.target as HTMLInputElement).value)}
              className={estilos.inputShort}
              placeholder="____ ____ ____ ____"
              maxLength={19} //  Espaços entre os grupos de 4 dígitos
              disabled={mutationRecuperarEmail.isPending || mutationRecuperarEmail.isSuccess}
            />
          </div>
        </div>

        <div className={`${estilos.botaoContainer} flex-row-reverse`}>
          {mutationRecuperarEmail.isPending ? (
            <SpinnerCpesc
              style={{
                width: "40px",
                height: "40px",
                marginTop: "1rem",
              }}
            />
          ) : (
            <Button
              className={telaPadrao.botaoPrincipal}
              label="Localizar Email"
              type="submit"
              disabled={mutationRecuperarEmail.isSuccess}
            />
          )}

          {emailRegistrado !== null && (
            <>
              <p>O email registrado para este CPF e Cartão é:</p>
              <p>[{emailRegistrado}]</p>
              <p>Este email foi notificado desta busca.</p>
            </>
          )}

          <Button
            className={classNames(emailRegistrado === null ? telaPadrao.botaoSecundario : telaPadrao.botaoPrincipal)}
            label="Voltar"
            type="button"
            onClick={props.onVoltar}
          />
        </div>
      </form>
    </section>
  );
}
