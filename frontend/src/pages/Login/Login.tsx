import icone from "@/assets/icone.png";
import TelaCarregando from "@/components/TelaCarregando";
import TelaErro from "@/components/TelaErro";
import { useOptionalAuth } from "@/contexts/auth/useAuth";
import { switchExaustivo } from "cpesc-shared/out/assertions";
import { type ReactNode, useState } from "react";
import { Navigate, useSearchParams } from "react-router-dom";
import { FormEmail } from "./FormEmail";

import { FormOtp } from "./FormOtp";
import estilos from "./Login.module.scss";
import { LoginSau } from "./LoginSau";
import RecuperaEmail from "./RecuperaEmail";

type Tela = { tela: "email" } | { tela: "otp"; email: string } | { tela: "recupera-email" } | { tela: "login-sau" };

export default function Login() {
  const [searchParams] = useSearchParams();
  const [dadosTela, setDadosTela] = useState<Tela>({ tela: "email" });
  const auth = useOptionalAuth();
  let componente: ReactNode;

  if (auth.carregando) {
    return <TelaCarregando />;
  }

  if (auth.erro !== null) {
    return <TelaErro mensagem={auth.erro.message} />;
  }

  if (auth.usuario !== null) {
    let rotaAlvo = searchParams.get("redirectTo") ?? "/";

    if (rotaAlvo === "/") {
      rotaAlvo = auth.usuario.rotaInicial;
    }

    return <Navigate to={rotaAlvo} replace />;
  }

  switch (dadosTela.tela) {
    case "email":
      componente = (
        <FormEmail
          onEnviar={email => setDadosTela({ tela: "otp", email })}
          onEsqueciEmail={() => setDadosTela({ tela: "recupera-email" })}
          onLoginSau={() => setDadosTela({ tela: "login-sau" })}
        />
      );
      break;
    case "otp":
      componente = <FormOtp email={dadosTela.email} onVoltar={() => setDadosTela({ tela: "email" })} />;
      break;
    case "recupera-email":
      componente = <RecuperaEmail onVoltar={() => setDadosTela({ tela: "email" })} />;
      break;
    case "login-sau":
      componente = <LoginSau onVoltar={() => setDadosTela({ tela: "email" })} />;
      break;
    default:
      return switchExaustivo(dadosTela);
  }

  return (
    <main className={estilos.container}>
      <img src={icone} alt="Ícone" className={estilos.icone} />
      <h1 className="mb-5">Bem-vindo ao CPESC</h1>
      {componente}
    </main>
  );
}
