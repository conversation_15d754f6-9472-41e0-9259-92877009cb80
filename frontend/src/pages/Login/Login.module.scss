@use "@/variables.scss";

.container {
  align-items: center;
  justify-content: center;
}

.icone {
  height: 100px;
  width: auto;
  display: block;
  margin: 1rem auto;
}

.cardLogin {
  border: 1px solid var(--cor-borda);
  border-radius: 0.25rem;
  font-size: 1rem;
  margin: 0 2rem 2rem;
  padding: 2rem;
}

.formulario {
  align-items: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.label {
  margin-bottom: 0.5rem;
  font-weight: bold;
}

.input {
  border-radius: 0.25rem;
  font-size: 1.25rem;
}

.botaoContainer {
  display: flex;
  gap: 1rem;
  margin: 1rem;
}

.informacaoEmail {
  width: 60%;
  margin: 2rem auto;
  text-align: center;

  @media (max-width: variables.$limite-largura-tela-tablet) {
    width: 80%;
  }
}

.esqueciEmailLink {
  border-radius: 0.25rem;
  color: #6c757d;
  display: flex;
  font-size: 0.85rem;
  gap: 0.5rem;

  &:hover {
    text-decoration: underline;
  }

  &:focus {
    outline: 1px solid #6c757d;
    outline-offset: 0.25rem;
  }
}

.alternativaLogin {
  align-items: center;
  display: flex;
  margin: 1rem auto;
}

.divisor {
  border-bottom: 1px none;
  flex: 1;
}

.tabelaRecuperarEmail {
  display: table;

  .campo {
    display: table-row;

    & > * {
      display: table-cell;
      padding: 0.5rem;
    }
  }
  @media (max-width: variables.$limite-largura-tela-tablet) {
    display: block;

    .campo {
      display: block;
      margin: 1rem;
    }
  }
}
