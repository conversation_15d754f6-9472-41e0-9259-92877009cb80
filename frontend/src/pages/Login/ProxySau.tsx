import TelaCarregando from "@/components/TelaCarregando";
import { environment } from "@/env";
import styles from "@/pages/LayoutPadrao/LayoutPadrao.module.scss";
import { useRef } from "react";
import { Navigate, useSearchParams } from "react-router-dom";

export default function ProxySau() {
  const [searchParams] = useSearchParams();
  const ticketEnviado = useRef(false);

  if (window.opener === null) {
    // Se não houver uma janela de origem (ou seja, esta rota foi acessada diretamente),
    // redireciona para a página inicial.
    return <Navigate to="/" replace />;
  }

  const ticket = searchParams.get("ticket");
  const urlLimpa = location.href.replace(location.search, "").replace(location.hash, "");

  if (ticket === null) {
    location.href = environment.sauUrlLogin + urlLimpa;
  } else if (!ticketEnviado.current) {
    ticketEnviado.current = true;
    (window.opener as Window).postMessage({ rota: urlLimpa, ticket }, environment.frontend);
    window.close();
  }

  return (
    <div className={styles.layout}>
      <TelaCarregando />
    </div>
  );
}
