@use "./mixins.scss";
@use "@/variables";
@import url("https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");

* {
  box-sizing: border-box;
}

body {
  @include mixins.altura-tela-inteira;
  font-size: 12px;
  margin: 0;
  background-color: var(--cor-fundo-padrao);
  font-family: var(--fonte-padrao);
  color: var(--cor-texto-destacado);
  min-block-size: 100vh;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  line-height: 1.2;
}

a,
button {
  all: unset;
  cursor: pointer;
}

p {
  line-height: 1;
  margin: 0;
}

.p-datatable-table {
  font-family: var(--fonte-padrao);
  font-size: 0.75rem;
  tr {
    th,
    td {
      padding: 0.75rem;
    }
  }
}

.p-datatable-thead {
  tr {
    th {
      background-color: var(--cor-verde-background);
      font-weight: bold;
    }
  }
}

.p-divider-content {
  background-color: var(--cor-fundo-padrao);
}

@media (max-width: variables.$limite-largura-tela-media-down) {
  .p-dropdown-label.p-inputtext {
    text-wrap: auto;
  }
}
