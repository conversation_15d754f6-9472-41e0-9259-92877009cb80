import { chamarApi } from "@/helpers/api";
import { useMutation } from "@tanstack/react-query";
import { endpoints } from "cpesc-shared/out/endpoints/main";
import type { PayloadCriarDocFiscal } from "cpesc-shared/src/endpoints/documento-fiscal";

export function useMutationCriarDocumentoFiscal() {
  return useMutation({
    mutationFn: (nota: PayloadCriarDocFiscal) => chamarApi(endpoints.criarDocumentoFiscal, {}, nota),
    onError: error => {
      console.error(error);
    },
  });
}
