import { chamar<PERSON>pi } from "@/helpers/api";
import { useMutation } from "@tanstack/react-query";
import type { DocumentoFiscal } from "cpesc-shared";
import { endpoints } from "cpesc-shared/out/endpoints/main";

export function useMutationAlterarDocumentoFiscal() {
  return useMutation({
    mutationFn: (nota: DocumentoFiscal) => chamarApi(endpoints.editarDocumentoFiscal, { id: nota.id.toString() }, nota),
    onError: error => {
      console.error(error);
    },
  });
}
