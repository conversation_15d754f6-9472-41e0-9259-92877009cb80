import { chamarApi } from "@/helpers/api";
import { useMutation } from "@tanstack/react-query";
import { endpoints } from "cpesc-shared/out/endpoints/main";
import type { PayloadCriarReversao } from "cpesc-shared/src/endpoints/documento-fiscal";

export function useMutationCriarReversao() {
  return useMutation({
    mutationFn: (reversao: PayloadCriarReversao) => chamarApi(endpoints.criarReversao, {}, reversao),
    onError: error => {
      console.error(error);
    },
  });
}
