import type { Toast, ToastMessage } from "primereact/toast";
import { createRef, useMemo, type RefObject } from "react";
import { create } from "zustand";

interface ToastState {
  toastRef: RefObject<Toast | null>;
}

const useToastStore = create<ToastState>()(() => ({
  toastRef: createRef(),
}));

export function useToast() {
  const toastRef = useToastStore(state => state.toastRef);

  return useMemo(
    () => ({
      ref: toastRef,
      show: (message: ToastMessage | ToastMessage[]) => {
        toastRef.current?.show(message);
      },
      clear: () => {
        toastRef.current?.clear();
      },
    }),
    [toastRef],
  );
}
