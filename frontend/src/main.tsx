import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import "primeflex/primeflex.css";
import "primeicons/primeicons.css";
import { addLocale, PrimeReactProvider } from "primereact/api";
import "primereact/resources/primereact.css";
import "primereact/resources/themes/saga-green/theme.css";
import React from "react";
import ReactDOM from "react-dom/client";
import "./index.scss";
import Cpesc2Router from "./routes.tsx";

addLocale("pt_BR", {
  firstDayOfWeek: 1,
  dayNames: ["<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>uarta", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Sábado"],
  dayNamesShort: ["dom", "seg", "ter", "qua", "qui", "sex", "sáb"],
  dayNamesMin: ["D", "S", "T", "Q", "Q", "S", "S"],
  monthNames: [
    "Janeiro-",
    "Fevereiro-",
    "Março-",
    "Abril-",
    "Mai<PERSON>-",
    "Junho-",
    "Julho-",
    "Agosto-",
    "Setembro-",
    "Outubro-",
    "Novembro-",
    "Dezembro-",
  ],
  monthNamesShort: ["Jan", "Fev", "Mar", "Abr", "Mai", "Jun", "Jul", "Ago", "Set", "Out", "Nov", "Dez"],
  today: "Hoje",
  clear: "Limpar",
});

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 0,
      refetchOnWindowFocus: false,
    },
  },
});

const root = document.getElementById("root");

if (root === null) {
  throw new Error("Elemento raiz não encontrado");
}

ReactDOM.createRoot(root).render(
  <React.StrictMode>
    <PrimeReactProvider>
      <QueryClientProvider client={queryClient}>
        <Cpesc2Router />
      </QueryClientProvider>
    </PrimeReactProvider>
  </React.StrictMode>,
);
