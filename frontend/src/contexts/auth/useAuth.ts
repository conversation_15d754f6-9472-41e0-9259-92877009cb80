import type { PayloadLogin, UsuarioAutenticado } from "cpesc-shared";
import { createContext, useContext } from "react";

export interface AuthData {
  usuario: UsuarioAutenticado | null;
  carregando: boolean;
  erro: Error | null;
  ipAddress?: string | null;
  isLoginPending: boolean;
  isLogoutPending: boolean;
}

export type AuthContext = AuthData & {
  login: (payload: PayloadLogin) => void;
  logout: () => void;
};

// eslint-disable-next-line @typescript-eslint/naming-convention -- permitido PascalCase para contextos
export const Auth = createContext<AuthContext>({
  usuario: null,
  carregando: true,
  erro: null,
  ipAddress: null,
  isLoginPending: false,
  isLogoutPending: false,
  login: () => {},
  logout: () => {},
});

export function useAuth(): AuthContext & { usuario: UsuarioAutenticado } {
  const auth = useContext(Auth);

  if (auth.usuario === null) {
    throw Error("Hook useAuth() utilizado em componente sem guarda de autenticação");
  }

  return auth as AuthContext & { usuario: UsuarioAutenticado };
}

export function useOptionalAuth(): AuthContext {
  return useContext(Auth);
}
