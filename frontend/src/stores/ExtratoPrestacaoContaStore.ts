import type { Credito, SituacaoFimLimiteCartao } from "cpesc-shared";
import { create } from "zustand";

export interface ExtratoPrestacaoContaState {
  situacao: SituacaoFimLimiteCartao | null;
  orgao: string;
  processo: string;
  ano: number | null;
  creditoSelecionado: Credito | null;
  gastosSelecionados: Set<number>;
  totalGastosSelecionados: number;
  setSituacao: (situacao: SituacaoFimLimiteCartao) => void;
  setOrgao: (orgao: string) => void;
  setProcesso: (processo: string) => void;
  setAno: (ano: number | null) => void;
  setCreditoSelecionado: (credito: Credito | null) => void;
  setSelecaoGasto: (gastoId: number, selecionado: boolean) => void;
  limparGastosSelecionados: () => void;
  setTotalGastosSelecionados: (total: number) => void;
}

export const useExtratoPrestacaoContaStore = create<ExtratoPrestacaoContaState>()(set => ({
  situacao: null,
  orgao: "",
  processo: "",
  ano: null,
  creditoSelecionado: null,
  gastosSelecionados: new Set(),
  totalGastosSelecionados: 0,
  setSituacao: (situacao: SituacaoFimLimiteCartao) => set({ situacao }),
  setOrgao: (orgao: string) => set({ orgao }),
  setProcesso: (processo: string) => set({ processo }),
  setAno: (ano: number | null) => set({ ano }),
  setCreditoSelecionado: (credito: Credito | null) => set({ creditoSelecionado: credito }),
  setSelecaoGasto: (gastoId: number, selecionado: boolean) =>
    set(state => {
      const novosGastosSelecionados = new Set(state.gastosSelecionados);

      if (selecionado) {
        novosGastosSelecionados.add(gastoId);
      } else {
        novosGastosSelecionados.delete(gastoId);
      }

      return { gastosSelecionados: novosGastosSelecionados };
    }),
  limparGastosSelecionados: () => set({ gastosSelecionados: new Set(), totalGastosSelecionados: 0 }),
  setTotalGastosSelecionados: (total: number) => set({ totalGastosSelecionados: total }),
}));
