import { environment } from "@/env";
import type { Perfil } from "cpesc-shared";
import type { ReactNode } from "react";
import { Navigate } from "react-router-dom";
import { useOptionalAuth } from "../contexts/auth/useAuth";
import AcessoNegado from "./AcessoNegado";
import TelaCarregando from "./TelaCarregando";
import TelaErro from "./TelaErro";
import WrapperTelemetria from "./WrapperTelemetria";

export interface AuthGuardProps {
  perfisRequisito: Perfil[];
  children: ReactNode;
}

export default function AuthGuard(props: AuthGuardProps) {
  const { usuario, carregando, erro } = useOptionalAuth();

  if (carregando) {
    return <TelaCarregando />;
  }

  if (erro !== null) {
    return <TelaErro mensagem={erro.message} />;
  }

  if (usuario === null) {
    const rotaAtualRelativa = location.href.replace(environment.frontend, "");
    return <Navigate to={`/login?redirectTo=${rotaAtualRelativa}`} replace />;
  }

  if (!props.perfisRequisito.includes(usuario.perfil)) {
    return <AcessoNegado />;
  }

  return <WrapperTelemetria>{props.children}</WrapperTelemetria>;
}
