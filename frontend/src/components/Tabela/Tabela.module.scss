.tabela {
  background: white;
  border-collapse: collapse;
  border-spacing: 0;
  font-size: 0.75rem;
  margin-bottom: 1rem;
  width: 100%;

  thead {
    background: var(--cor-verde-background);
    text-align: left;
  }

  th,
  td {
    border-bottom: 1px solid var(--cor-borda-tabela);
    padding: 0.75rem;
    & i {
      margin-left: 0.25rem;
      font-size: 0.8rem;
    }
  }
}

.tabelaTransposta {
  min-width: fit-content;
  font-size: 0.85rem;

  table {
    width: 100%;
    border-bottom: 1px solid var(--cor-borda-tabela);
  }

  th {
    text-align: left;
    & i {
      margin-left: 0.25rem;
      font-size: 0.8rem;
    }
  }

  td {
    text-align: right;
  }
}
