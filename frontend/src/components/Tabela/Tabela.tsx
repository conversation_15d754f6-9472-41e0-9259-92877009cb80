import { Fragment, type ReactNode, useEffect, useState } from "react";
import estilo from "./Tabela.module.scss";

export interface TabelaProps<TLinha> {
  dados: TLinha[];
  breakpoint?: number;
  linhaExpandida?: (linha: TLinha) => ReactNode;
  colunas: DefinicaoColuna<TLinha>[];
  mensagemSemDados?: ReactNode;
}

export interface DefinicaoColuna<TLinha> {
  cabecalho: ReactNode | ((transpor: boolean) => ReactNode);
  conteudo: ReactNode | ((linha: TLinha) => ReactNode);
  titulo?: string;
}

export default function Tabela<TLinha>(props: TabelaProps<TLinha>) {
  const [transpor, setTranspor] = useState(false);

  useEffect(() => {
    const tratarRedimensionamentoTela = () => {
      if (props.breakpoint === undefined) {
        setTranspor(false);
        return;
      }

      setTranspor(document.body.offsetWidth < props.breakpoint);
    };

    window.addEventListener("resize", tratarRedimensionamentoTela);
    tratarRedimensionamentoTela();

    return () => window.removeEventListener("resize", tratarRedimensionamentoTela);
  }, [props.breakpoint]);

  if (transpor) {
    return (
      <div className={estilo.tabelaTransposta}>
        {props.dados.map((linha, index) => {
          let linhaExpandida: ReactNode;
          if (props.linhaExpandida) {
            linhaExpandida = props.linhaExpandida(linha);
          }

          return (
            <Fragment key={`tabela-${index}`}>
              <table>
                <tbody>
                  {props.colunas.map((coluna, index) => {
                    return (
                      <tr key={`linha-${index}`}>
                        <th scope="row" title={coluna.titulo}>
                          {coluna.cabecalho instanceof Function ? coluna.cabecalho(transpor) : coluna.cabecalho}
                        </th>

                        <td>{coluna.conteudo instanceof Function ? coluna.conteudo(linha) : coluna.conteudo}</td>
                      </tr>
                    );
                  })}

                  {linhaExpandida && (
                    <tr>
                      <td colSpan={props.colunas.length}>{linhaExpandida}</td>
                    </tr>
                  )}
                </tbody>
              </table>
            </Fragment>
          );
        })}
      </div>
    );
  }

  return (
    <table className={estilo.tabela}>
      <thead>
        <tr>
          {props.colunas.map((coluna, index) => (
            <th key={index} scope="col" title={coluna.titulo}>
              {coluna.cabecalho instanceof Function ? coluna.cabecalho(transpor) : coluna.cabecalho}
            </th>
          ))}
        </tr>
      </thead>
      <tbody>
        {props.dados.length === 0 && (
          <tr>
            <td colSpan={props.colunas.length}>{props.mensagemSemDados ?? "Nenhum valor encontrado"}</td>
          </tr>
        )}

        {props.dados.map((dadosLinha, i) => {
          let linhaExpandida: ReactNode;
          if (props.linhaExpandida) {
            linhaExpandida = props.linhaExpandida(dadosLinha);
          }

          return (
            <Fragment key={i}>
              <tr>
                {props.colunas.map((coluna, index) => (
                  <td key={index}>
                    {coluna.conteudo instanceof Function ? coluna.conteudo(dadosLinha) : coluna.conteudo}
                  </td>
                ))}
              </tr>

              {linhaExpandida && (
                <tr>
                  <td colSpan={props.colunas.length}>{linhaExpandida}</td>
                </tr>
              )}
            </Fragment>
          );
        })}
      </tbody>
    </table>
  );
}
