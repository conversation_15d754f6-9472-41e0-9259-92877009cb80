import type { ReactNode } from "react";

export default function ContainerQuery(props: {
  carregando: boolean;
  fallbackCarregamento: ReactNode;
  erro: Error | null;
  fallbackErro: ReactNode;
  children: ReactNode;
}) {
  if (props.carregando) {
    return props.fallbackCarregamento;
  }

  if (props.erro !== null) {
    return (
      <>
        ${props.fallbackErro} ${props.erro.message}
      </>
    );
  }

  return props.children;
}
