@use "@/variables";

.cabecalho {
  border-bottom: 2px solid var(--cor-texto-destacado);
  & .conteudo {
    display: flex;
    justify-content: space-between;
    padding: calc(var(--padding-principal) / 2) var(--padding-principal);
    @media (max-width: variables.$limite-largura-tela-mobile-down) {
      padding: var(--padding-principal) / 2;
    }
  }
}

.conteudo {
  align-items: center;
  padding: 0 calc(var(--padding-principal) / 2);

  @media (max-width: variables.$limite-largura-tela-mobile-down) {
    padding: var(--padding-principal) / 2;
  }
}

.titulo {
  text-decoration: none;

  h1 {
    align-items: center;
    display: flex;
    font-size: 1.25rem;
    gap: 1.5rem;
    @media (max-width: variables.$limite-largura-tela-mobile) {
      & > span {
        display: none;
      }
    }
  }
}

.link {
  align-items: center;
  border-radius: var(--border-radius-padrao);
  display: flex;
  gap: 0.5rem;
  margin-left: clamp(8px, 1.5vw, 128px);

  &:hover {
    cursor: pointer;
    outline: 0.125rem solid var(--cor-borda-tabela);
    outline-offset: 0.25rem;
  }
}

.logo {
  height: 32px;
  cursor: pointer;
}

.icone {
  font-size: 20px;
  padding-right: 10px;
}

/* barra do menu superior */
.menuBar {
  font-family: var(--font-padrao);
  font-size: 1.25em;
  background-color: transparent;
  border: none;
  padding: inherit;
  ul {
    li {
      padding: 0;
      border: 1px transparent solid;
      &[aria-expanded="true"] {
        background-color: var(--cor-menu-fundo);
        border: 1px var(--cor-borda-tabela) solid;
        border-radius: var(--border-radius-padrao);
      }
    }
  }
}

/* parte atras do menu suspenso */
.subMenu {
  padding: 6px 10px;
  border-radius: var(--border-radius-padrao);
  box-shadow: var(--sombra-padrao);
  width: fit-content;
  text-wrap: nowrap;

  @media screen and (max-width: 960px) {
    width: inherit;
    text-wrap: wrap;
  }

  ul {
    li[aria-expanded="true"] .menuContent .menuAction svg {
      transform: rotate(-90deg);
    }
  }
}

/*  parte interna dos menus */
.menuContent {
  background-color: transparent;
  padding: 10px;
  border: 1px solid transparent;
  border-radius: var(--border-radius-padrao);

  a {
    border: 1px solid var(--cor-menu-fundo);
  }

  &:hover {
    text-decoration: none;
    background-color: var(--cor-menu-hover);
    border: 1px solid var(--cor-borda-tabela);
  }
}

/* Labels dos menus */
.menuLabel {
  padding: 5px;
  text-decoration: none;
  color: var(--cor-texto-destacado);

  &:hover,
  span:hover,
  a:hover {
    text-decoration: none;
  }
}

/* Botão hamburguer do menu */
.menuButton {
  display: none;
  @media screen and (max-width: 960px) {
    display: block;
    padding-top: calc(var(--padding-principal) / 2);
  }
}
