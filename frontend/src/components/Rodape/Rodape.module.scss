@use "@/variables";

footer {
  font-size: 0.8rem;

  a {
    color: var(--cor-verde-escuro);
    text-decoration: none;
    background-color: transparent;
    &:hover {
      color: var(--cor-link);
      text-decoration: underline;
    }
  }

  .info {
    background: var(--cor-fundo-rodape);
    color: var(--cor-fundo-padrao);
  }

  .conteudo {
    --largura-maxima-rodape: 100%;
    display: flex;
    margin: auto;
    max-width: var(--largura-maxima-rodape);

    @media (min-width: variables.$limite-largura-tela-padrao) {
      --largura-maxima-rodape: #{variables.$limite-largura-tela-padrao};
    }
    @media (min-width: variables.$limite-largura-tela-grande) {
      --largura-maxima-rodape: #{variables.$limite-largura-tela-grande};
    }
  }

  .bloco {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    gap: 1rem;

    @media (max-width: variables.$limite-largura-tela-mobile) {
      flex-direction: column;
      align-items: start;
    }
  }

  .logo {
    background-image: url(../../assets/marca-digital-sc.png);
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    display: flex;
    height: 100%;
    width: 100%;
    max-width: 150px;
    min-height: 90px;
    flex: 0 0 auto;
    margin-top: 0.5rem;
  }

  .link,
  .link:hover {
    color: var(--cor-link-light);
    font-weight: 700;
    text-decoration: none;
  }

  .espaco {
    padding-left: 8px;
  }

  .copyright {
    background: #161616;
    color: white;
    align-items: center;
    @media (max-width: variables.$limite-largura-tela-mobile) {
      padding-top: 1rem;
    }

    .ico {
      width: 48px;
      height: 28px;
      margin-left: 3px;
      display: inline-block;
      background-image: url("@/assets/ciasc_favicon.png");
      background-position: 0 center;
      background-repeat: no-repeat;
      vertical-align: middle;
    }
  }
}
