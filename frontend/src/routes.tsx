import { Perfil } from "cpesc-shared";
import { lazy, type ReactNode } from "react";
import {
  createBrowserRouter,
  Link,
  Navigate,
  Outlet,
  RouterProvider,
  type NonIndexRouteObject,
} from "react-router-dom";
import AuthGuard from "./components/AuthGuard";
import TelaCarregando from "./components/TelaCarregando";
import TelaErro from "./components/TelaErro";
import TelemetriaDashboard from "./pages/Dashboard/TelemetriaDashboard";

// não importa as paginas aqui --> faze fazer no lazy import LayoutPrestacaoContas from "./pages/LayoutPrestacaoConta/LayoutPrestacaoConta";

interface AppRoute extends NonIndexRouteObject {
  handle?: RouteData;
  children?: AppRoute[];
}

export interface RouteData {
  breadcrumb?: () => ReactNode;
}

const perfisVisaoGeral: Perfil[] = [
  Perfil.GestorSed,
  Perfil.Gestor,
  Perfil.<PERSON>ta,
  Perfil.AdministradorCpesc,
  Perfil.AdministradorCiasc,
];

/**
 * Componentes representando cada página do sistema. As páginas são carregadas de forma lazy, ou seja, só são
 * carregadas quando a página é acessada. Isso melhora a performance do carregamento inicial da aplicação.
 */

/* eslint-disable @typescript-eslint/naming-convention -- essas constantes representam componentes */
const LayoutPadrao = lazy(() => import("./pages/LayoutPadrao/LayoutPadrao"));
const AcessoNegado = lazy(() => import("./components/AcessoNegado"));
const RequisicaoInvalida = lazy(() => import("./components/RequisicaoInvalida"));
const VisaoGeral = lazy(() => import("./pages/VisaoGeral/VisaoGeral"));
const Login = lazy(() => import("./pages/Login/Login"));
const GerenciarUsuario = lazy(() => import("./pages/GerenciarUsuario/GerenciarUsuario"));
const ProxySau = lazy(() => import("./pages/Login/ProxySau"));
const RootErrorBoundary = lazy(() => import("./components/RootErrorBoundary"));
const SelecaoCreditos = lazy(() => import("./pages/PrestacaoConta/SelecaoCreditos/SelecaoCreditos"));
const PrestacaoContas = lazy(() => import("./pages/PrestacaoConta/Extrato/ExtratoPrestacaoConta"));
const RelatorioGastosPorNCM = lazy(() => import("./pages/Relatorios/GastosPorNCM/RelatorioGastosPorNCM"));
/* eslint-enable @typescript-eslint/naming-convention */

/**
 * Rotas da aplicação. Utilizar a versão lazy de cada página (acima) para não prejudicar a performance.
 **/

const router = createBrowserRouter([
  {
    path: "/",
    element: <LayoutPadrao />,
    errorElement: <RootErrorBoundary />,
    handle: {
      breadcrumb: () => <Link to="/">Início</Link>,
    },
    children: [
      {
        path: "/acessonegado",
        element: <AcessoNegado />,
      },
      {
        path: "/erro",
        element: <TelaErro linkVoltar="/" />,
      },
      {
        path: "/requisicaoinvalida",
        element: <RequisicaoInvalida />,
      },
      {
        path: "/",
        element: (
          <AuthGuard perfisRequisito={perfisVisaoGeral}>
            <TelaCarregando />
            <Navigate to="/visaogeral" />,
          </AuthGuard>
        ),
      },
      {
        path: "/visaogeral",
        element: (
          <AuthGuard perfisRequisito={perfisVisaoGeral}>
            <VisaoGeral />
          </AuthGuard>
        ),
      },
      {
        path: "/login",
        element: <Login />,
      },
      {
        path: "/admin",
        element: (
          <AuthGuard
            perfisRequisito={[Perfil.GestorSed, Perfil.Gestor, Perfil.AdministradorCpesc, Perfil.AdministradorCiasc]}
          >
            <Outlet />
          </AuthGuard>
        ),
        handle: { breadcrumb: () => "Administração" },
        children: [
          {
            path: "/admin/gerenciar-usuario",
            element: <GerenciarUsuario />,
            handle: { breadcrumb: () => "Gerenciar Usuário" },
          },
          {
            path: "/admin/telemetria",
            element: <TelemetriaDashboard />,
            handle: { breadcrumb: () => "Dashboard de Telemetria" },
          },
        ],
      },
    ],
  },
  {
    path: "/proxy/sau",
    element: <ProxySau />,
  },
  {
    path: "/prestacao-contas",
    element: <LayoutPadrao />,
    errorElement: <RootErrorBoundary />,
    handle: {
      breadcrumb: () => <Link to="/prestacao-contas">Início</Link>,
    },
    children: [
      {
        path: "/prestacao-contas",
        element: (
          <AuthGuard
            perfisRequisito={[
              Perfil.Portador,
              Perfil.GestorSed,
              Perfil.Gestor,
              Perfil.Consulta,
              Perfil.AdministradorCpesc,
              Perfil.AdministradorCiasc,
            ]}
          >
            <TelaCarregando />
            <Navigate to="/prestacao-contas/selecao-credito" />,
          </AuthGuard>
        ),
      },
      {
        path: "/prestacao-contas/selecao-credito",
        element: (
          <AuthGuard
            perfisRequisito={[
              Perfil.Portador,
              Perfil.GestorSed,
              Perfil.Gestor,
              Perfil.Consulta,
              Perfil.AdministradorCpesc,
              Perfil.AdministradorCiasc,
            ]}
          >
            <SelecaoCreditos />
          </AuthGuard>
        ),
      },
      {
        path: "/prestacao-contas/extrato/:id",
        element: (
          <AuthGuard
            perfisRequisito={[
              Perfil.Portador,
              Perfil.GestorSed,
              Perfil.Gestor,
              Perfil.Consulta,
              Perfil.AdministradorCpesc,
              Perfil.AdministradorCiasc,
            ]}
          >
            <PrestacaoContas />
          </AuthGuard>
        ),
        handle: { breadcrumb: () => "Demonstrativo para Prestação de Contas" },
      },
      {
        path: "/prestacao-contas/relatorio-gastos-ncm",
        element: (
          <AuthGuard
            perfisRequisito={[
              Perfil.Portador,
              Perfil.GestorSed,
              Perfil.Gestor,
              Perfil.Consulta,
              Perfil.AdministradorCpesc,
              Perfil.AdministradorCiasc,
            ]}
          >
            <RelatorioGastosPorNCM />
          </AuthGuard>
        ),
        handle: { breadcrumb: () => "Relatório de Gastos por NCM" },
      },
    ],
  },
] satisfies AppRoute[]);

export default function Cpesc2Router() {
  return <RouterProvider router={router} />;
}
