import { environment } from "@/env";
import { HttpStatus } from "cpesc-shared";
import type { MetodoHttp, Rota } from "cpesc-shared/out/endpoints/main";
import {
  type EventoTelemetria,
  type EventoTelemetriaResposta,
  TipoEventoTelemetria,
} from "cpesc-shared/src/endpoints/telemetria";
import type { RouteParameters } from "express-serve-static-core";
import type { z, ZodType } from "zod";

export async function chamarApi<TUri extends string, TResposta extends ZodType>(
  rota: Rota<TUri, "get", ZodType, TResposta>,
  queryParams: RouteParameters<TUri>,
  sufixoUrl?: string,
  init?: RequestInit,
): Promise<z.infer<TResposta>>;

export async function chamarApi<
  TUri extends string,
  TMetodo extends Exclude<MetodoHttp, "get">,
  TPayload extends ZodType,
  TResposta extends ZodType,
>(
  rota: Rota<TUri, TMetodo, TPayload, TResposta>,
  queryParams: RouteParameters<TUri>,
  payload: z.infer<TPayload>,
  init?: RequestInit,
): Promise<z.infer<TResposta>>;

export async function chamarApi<
  TUri extends string,
  TMetodo extends MetodoHttp,
  TPayload extends ZodType,
  TResposta extends ZodType,
>(
  rota: Rota<TUri, TMetodo, TPayload, TResposta>,
  queryParams: RouteParameters<TUri>,
  sufixoUrlOuPayload?: string | z.infer<TPayload>,
  init?: RequestInit,
): Promise<z.infer<TResposta>> {
  const sufixoUrl = rota.metodo === "get" ? (sufixoUrlOuPayload as string | undefined) : undefined;
  const payload = rota.metodo === "get" ? undefined : (sufixoUrlOuPayload as z.infer<TPayload>);
  const uri = rota.uri.replace(/:(\w+)/g, (_, chave: string) => queryParams[chave]);
  // Promise intencionalmente sem await (telemetria não deve bloquear o carregamento de outras rotas)
  const promiseTelemetriaId = enviarTelemetriaConsulta(uri, rota.metodo);
  const inicioExecucao = performance.now();
  const resposta = await fetch(environment.api + uri + (sufixoUrl ?? ""), {
    method: rota.metodo.toUpperCase(),
    body: payload === undefined ? undefined : JSON.stringify(payload),
    headers:
      payload === undefined
        ? undefined
        : {
            "Content-Type": "application/json",
          },
    credentials: "include",
    ...init,
  });
  const status: HttpStatus = resposta.status;
  const eventoTelemetriaResposta: EventoTelemetriaResposta = {
    statusCode: status,
    tempoExecucaoMs: performance.now() - inicioExecucao,
  };
  void promiseTelemetriaId.then(id => {
    void fetch(environment.api + `/telemetria/${id}`, {
      method: "POST",
      body: JSON.stringify(eventoTelemetriaResposta),
      headers: {
        "Content-Type": "application/json",
      },
      credentials: "include",
    });
  });

  if (status === HttpStatus.UNAUTHORIZED) {
    const rotaAtualRelativa = location.href.replace(environment.frontend, "");
    location.href = `/login?redirectTo=${rotaAtualRelativa}`;
    throw new Error("Usuário não autenticado");
  }

  if (status === HttpStatus.FORBIDDEN) {
    location.href = "/acessonegado";
    throw new Error("Usuário não possui permissão para esta funcionalidade");
  }

  if (!resposta.ok) {
    await resposta.json().then(
      (json: Record<string, unknown>) => {
        if ("message" in json && typeof json.message === "string") {
          throw new Error(json.message);
        }

        console.error(json);
      },
      async () => console.error(await resposta.text()),
    );

    const error = new Error(`Erro na rota ${rota.uri}: ${resposta.status} ${resposta.statusText}`);
    (error as Error & { status: HttpStatus }).status = resposta.status;
    throw error;
  }

  return resposta.json() as z.infer<TResposta>;
}

export async function enviarTelemetriaAcessoRota(): Promise<number> {
  const urlAtual = location.href.split("?")[0];
  const eventoTelemetria: EventoTelemetria<TipoEventoTelemetria.AcessoRota> = {
    tipo: TipoEventoTelemetria.AcessoRota,
    url: urlAtual,
  };

  const response = await fetch(environment.api + "/telemetria", {
    method: "POST",
    body: JSON.stringify(eventoTelemetria),
    headers: {
      "Content-Type": "application/json",
    },
    credentials: "include",
  });

  return response.json() as Promise<number>;
}

async function enviarTelemetriaConsulta(rotaApi: string, verboHttp: MetodoHttp): Promise<number> {
  const urlAtual = location.href.split("?")[0];
  const eventoTelemetria: EventoTelemetria<TipoEventoTelemetria.Consulta> = {
    tipo: TipoEventoTelemetria.Consulta,
    url: urlAtual,
    rotaApi,
    verboHttp,
  };

  const response = await fetch(environment.api + "/telemetria", {
    method: "POST",
    body: JSON.stringify(eventoTelemetria),
    headers: {
      "Content-Type": "application/json",
    },
    credentials: "include",
  });

  if (!response.ok) {
    return Promise.reject();
  }

  return response.json() as Promise<number>;
}
