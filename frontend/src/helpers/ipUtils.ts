/**
 * Normalizes an IP address, converting IPv6 to IPv4 when possible
 * @param ip The IP address to normalize
 * @returns The normalized IP address
 */
export function normalizeIp(ip: string | null): string | null {
  if (!ip) return null;

  // Convert IPv6 localhost to IPv4 localhost
  if (ip === "::1") return "127.0.0.1";

  // Remove IPv6 prefix if present (::ffff:*********** -> ***********)
  if (ip.startsWith("::ffff:")) {
    return ip.substring(7);
  }

  // If it's a valid IPv4 address, return it
  const ipv4Regex = /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/;
  if (ipv4Regex.test(ip)) {
    return ip;
  }

  // For other IPv6 addresses, we can't easily convert to IPv4
  // So we'll just return the original IP
  return ip;
}
