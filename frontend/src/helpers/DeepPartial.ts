/**
 * Tipo similar ao Partial, mas aplicado recursivamente a todos os níveis de um objeto.
 */
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends (infer U)[] | undefined // Array que possa ser undefined
    ? DeepPartial<U>[] | undefined
    : T[P] extends (infer U)[] | null | undefined // Array que possa ser null ou undefined
      ? DeepPartial<U>[] | null | undefined
      : T[P] extends object // Objetos
        ? DeepPartial<T[P]>
        : T[P]; // Valores simples
};
