import { TipoDocumentoFiscal } from "cpesc-shared";

export interface OpcaoDocumentoFiscal {
  descricao: string;
  codigo: TipoDocumentoFiscal;
}

export const opcoesDocumentoFiscal: OpcaoDocumentoFiscal[] = [
  { descricao: "Importar Nota Fiscal Eletrônica", codigo: TipoDocumentoFiscal.notaFiscalEletronicaImportacao },
  { descricao: "Digitar Nota Fiscal Eletrônica", codigo: TipoDocumentoFiscal.notaFiscalEletronicaManual },
  { descricao: "Digitar Nota Fiscal de Serviço", codigo: TipoDocumentoFiscal.notaServicoManual },
  { descricao: "Digitar Cupom Fiscal", codigo: TipoDocumentoFiscal.cupomFiscalManual },
  { descricao: "Vincular Reversão", codigo: TipoDocumentoFiscal.reversao },
];

export enum DescricaoSituacaoFimLimiteCartao {
  P = "Pendente",
  F = "Finalizado",
  E = "Em edição",
}
export enum SeveridadeSituacaoFimLimiteCartao {
  P = "warning",
  F = "success",
  E = "info",
}
