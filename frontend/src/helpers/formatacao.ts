export function formatarData(valor: string | Date): string {
  const data = new Date(valor);
  const retorno = data.toLocaleDateString("pt-br", { timeZone: "UTC" });
  return retorno ? retorno : "";
}

export function formatarDataHora(valor: string | Date): string {
  const data = new Date(valor);
  const dia = String(data.getDate()).padStart(2, "0");
  const mes = String(data.getMonth() + 1).padStart(2, "0");
  const ano = data.getFullYear();
  const hora = String(data.getHours()).padStart(2, "0");
  const minuto = String(data.getMinutes()).padStart(2, "0");
  return `${dia}/${mes}/${ano} ${hora}:${minuto}`;
}

export function formatarMoeda(valor: number | undefined | null): string {
  if (valor == 0 || valor === undefined || valor === null || isNaN(valor)) {
    return "R$ 0,00";
  }

  return valor.toFixed(2) === "-0.00"
    ? "R$ 0,00"
    : valor.toLocaleString("pt-BR", { style: "currency", currency: "BRL" });
}

export function aplicarMascaraCNPJ(cnpj: string) {
  if (cnpj) {
    cnpj = cnpj.padStart(14, "0");
    return cnpj.replace(/([0-9]{2})([0-9]{3})([0-9]{3})([0-9]{4})([0-9]{2})/gi, "$1.$2.$3/$4-$5");
  } else {
    return "";
  }
}

export function formatarNome(texto: string): string {
  if (texto.trim().length === 0) {
    return "";
  }

  return texto
    .split(" ")
    .map(palavra => palavra.charAt(0).toUpperCase() + palavra.slice(1).toLowerCase())
    .join(" ");
}

export function getDataValidada(data: string): Date | null {
  // Verifica se a data está no formato dd/MM/yyyy
  const regex = /^\d{2}\/\d{2}\/\d{4}$/;

  if (!regex.test(data)) {
    return null;
  }

  // Divide a data em dia, mês e ano
  const [dia, mes, ano] = data.split("/").map(Number);

  // Verifica se os valores de dia, mês e ano são válidos
  if (dia < 1 || dia > 31 || mes < 1 || mes > 12 || ano < 1000 || ano > 9999) {
    return null;
  }

  // Cria um objeto Date e verifica se a data é válida
  const dataValida = new Date(ano, mes - 1, dia);

  if (dataValida.getFullYear() !== ano || dataValida.getMonth() !== mes - 1 || dataValida.getDate() !== dia) {
    return null;
  }

  return dataValida;
}

export function aplicarMascaraCartao(cartao: string, ofuscar: boolean) {
  if (cartao) {
    cartao = cartao.padStart(16, "0");
    return ofuscar
      ? cartao.replace(/([0-9]{4})([0-9]{4})([0-9]{4})([0-9]{4})/gi, "$1.****.****.$4")
      : cartao.replace(/([0-9]{4})([0-9]{4})([0-9]{4})([0-9]{4})/gi, "$1.$2.$3.$4");
  } else {
    return "";
  }
}

export function formatarDataYYYYMMDD(data: Date): string {
  const ano = data.getFullYear();
  const mes = String(data.getMonth() + 1).padStart(2, "0");
  const dia = String(data.getDate()).padStart(2, "0");
  const horas = String(data.getHours()).padStart(2, "0");
  const minutos = String(data.getMinutes()).padStart(2, "0");
  const segundos = String(data.getSeconds()).padStart(2, "0");

  return `${ano}${mes}${dia}_${horas}${minutos}${segundos}`;
}

export function formatarDataHoraExtenso(data: Date) {
  const dia = String(data.getDate()).padStart(2, "0");
  const meses = [
    "janeiro",
    "fevereiro",
    "março",
    "abril",
    "maio",
    "junho",
    "julho",
    "agosto",
    "setembro",
    "outubro",
    "novembro",
    "dezembro",
  ];
  const mes = meses[data.getMonth()];
  const ano = data.getFullYear();
  const horas = String(data.getHours()).padStart(2, "0");
  const minutos = String(data.getMinutes()).padStart(2, "0");

  return `${dia} de ${mes} de ${ano} às ${horas}:${minutos}`;
}

export function formatarValor(valor: number) {
  return valor.toLocaleString("pt-BR", { minimumFractionDigits: 2 });
}
