@use "@/variables";

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 30vh;
  --largura-container: 100%;
  --largura-container-disponivel: calc(var(--largura-container) - 2 * var(--padding-principal));
  align-self: center;
  padding: var(--padding-principal);
  width: var(--largura-container);
  margin: auto;
  @media (min-width: variables.$limite-largura-tela-padrao) {
    --largura-container: #{variables.$limite-largura-tela-padrao};
  }
  @media (min-width: variables.$limite-largura-tela-grande) {
    --largura-container: #{variables.$limite-largura-tela-grande};
  }
}

.codigoErro {
  font-size: 9rem;
  color: #641515;
}

.mensagemErro {
  font-size: 2rem;
}

.mensagemAjuda {
  margin-top: 2rem;
  max-width: 50%;
}

.mensagem {
  background-color: #f5f5f5;
  padding: 1rem;
  border-radius: 0.5rem;
  margin-top: 2rem;
  max-width: 50%;
  text-align: center;
}

.linkVoltar {
  margin-top: 2rem;
  text-decoration: none;
}
