// Variável SCSS pois é utilizado em media queries
$limite-largura-tela-mobile-down: 384px;
$limite-largura-tela-mobile: 512px;
$limite-largura-tela-mobile-up: 640px;
$limite-largura-tela-tablet: 768px;
$limite-largura-tela-media-down: 896px;
$limite-largura-tela-media: 1024px;
$limite-largura-tela-media-up: 1152px;
$limite-largura-tela-padrao: 1280px;
$limite-largura-tela-padrao-up: 1408px;
$limite-largura-tela-grande: 1536px;

/**
 * Palheta de cores do sistema
 */
:root {
  --cor-fundo-padrao: hsl(45, 80%, 98%);
  --cor-fundo-rodape: hsl(0, 0%, 15%);
  --cor-texto-destacado: hsl(147, 29%, 20%);
  --cor-verde-claro: hsl(79, 53%, 54%);
  --cor-verde-medio: hsl(115, 37%, 52%);
  --cor-verde-escuro: hsl(147, 98%, 23%);
  --cor-alerta: hsla(0, 90%, 44%, 0.75);
  --cor-erro: hsl(14, 54%, 89%);
  --cor-link: hsl(147, 29%, 20%);
  --cor-link-light: hsl(0, 0%, 88%);
  --cor-link-hover: hsl(164, 95%, 26%);
  --cor-menu-fundo: hsl(150, 50%, 99%);
  --cor-menu-hover: hsl(0, 0%, 98%);
  --cor-verde-background: hsl(110, 39%, 91%);
  --cor-inativo: hsl(0, 0%, 78%);
  --cor-divisor: hsl(44, 38%, 81%);
  --cor-ativo: hsl(147deg 25.94% 29.97%);
  --cor-borda: hsl(0, 0%, 79%);
  --cor-borda-tabela: hsl(210, 16%, 93%);
}

/**
 * Outras configurações
 */
:root {
  --fonte-padrao: "Poppins", sans-serif;
  --padding-principal: 2em; /* utilizado no cabeçalho e nos containers de conteúdo */
  --border-radius-padrao: 4px;
  --sombra-padrao: 3px 2px 10px hsla(0, 0%, 75%, 0.7);
  --tamanho-fonte-pequena: 0.8rem;
}
