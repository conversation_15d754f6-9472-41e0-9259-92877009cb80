{
  "extends": "../tsconfig-base",
  "references": [{ "path": "./tsconfig.node.json" }, { "path": "../shared" }],
  "include": ["src/**/*"],
  "compilerOptions": {
    // "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["es2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    // "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "noUnusedParameters": true,

    "paths": {
      "@/*": ["./src/*"]
    }
  }
}
