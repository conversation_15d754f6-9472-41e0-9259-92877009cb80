#!/bin/bash

rm -rf api/dist frontend/dist shared/out
rm -rf node_modules api/node_modules frontend/node_modules shared/node_modules
rm -rf package-lock.json api/package-lock.json frontend/package-lock.json shared/package-lock.json
rm -rf *.tsbuildinfo api/*.tsbuildinfo frontend/*.tsbuildinfo shared/*.tsbuildinfo

echo ""
echo "Limpeza concluída!"
echo ""
printf "Efetuar Rebuild? (Y/n): "
read resposta

if [ "$resposta" = "Y" ] || [ "$resposta" = "y" ] || [ -z "$resposta" ]; then
    echo "Executando rebuild..."
    sh rebuild.sh
else
    echo "Rebuild cancelado."
fi
