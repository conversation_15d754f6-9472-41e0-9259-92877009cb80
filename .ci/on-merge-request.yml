mr-lint-frontend:
  stage: mr-check
  image: ${NODE_IMAGE}
  cache:
    key:
      files:
        - ./package-lock.json
    paths:
      - .npm/
  script:
    - SKIP_PREINSTALL=1 npm ci --cache .npm --prefer-offline
    - npm --prefix frontend run copy-env:des
    - npm run build:shared
    - ls -la shared/out
    - npm run lint:check:frontend
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TITLE =~ /^Draft:/
      when: never
    - changes:
      - frontend/**/*
      - shared/**/*
      - eslint.config.js
      - .prettierrc.js
      - .prettierignore

mr-lint-api:
  stage: mr-check
  image: ${NODE_IMAGE}
  cache:
    key:
      files:
        - ./package-lock.json
    paths:
      - .npm/
  script:
    - SKIP_PREINSTALL=1 npm ci --cache .npm --prefer-offline
    - npm run build:shared
    - ls -la shared/out
    - npm run lint:check:api
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TITLE =~ /^Draft:/
      when: never
    - changes:
      - api/**/*
      - shared/**/*
      - eslint.config.js
      - .prettierrc.js
      - .prettierignore

mr-api-test:
  stage: mr-api-test
  image: ${NODE_IMAGE}
  dependencies:
    - mr-lint-api
  cache:
    key:
      files:
        - ./package-lock.json
    paths:
      - .npm/
  variables:
    ENV_FILE: .env-ci.env
    DB_PASSWORD: $DB_CI_PASSWORD
    EMAIL_PASSWORD: $EMAIL_CI_PASSWORD
  script:
    - SKIP_PREINSTALL=1 npm ci --cache .npm --prefer-offline
    - npm --prefix shared run build
    - cd api
    - cat ${ENV_FILE} | sed "s/{{DB_PASSWORD}}/${DB_PASSWORD}/" | sed "s/{{EMAIL_PASSWORD}}/${EMAIL_PASSWORD}/" > .env
    - npm test
  coverage: /^\s*All files\s+\|\s+([\d\.]+)/
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_TITLE =~ /^Draft:/
      when: never
    - changes:
      - api/**/*
      - shared/**/*
