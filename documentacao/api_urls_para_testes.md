# URLs para testes

## Arquivo Prestadores

### Combo de unidades gestoras

[/unidade-gestora](http://localhost:3001/unidade-gestora)

Retorna lista:

```js
id: numero;
codigo: numero;
nome: texto;
```

### Combo de origem, com filtro de UG

[/origem-prestadores/ug/5](http://localhost:3001/origem-prestadores/ug/5)

Parâmetro:

- ug: numero

Retorna lista:

```js
id: numero;
codigo: numero;
descricao: texto;
```

### Combo de origem

[/origem-prestadores](http://localhost:3001/origem-prestadores)

Retorna lista:

```js
id: numero;
codigo: numero;
descricao: texto;
```

### Listagem de arquivos

[/arquivo-prestadores?pagina=11&tamanho=2](http://localhost:3001/arquivo-prestadores?pagina=11&tamanho=2)

Parâmetros da query de consulta:

- pagina: numero
- tamanho: numero, opcional, default: 10
- ordenarPor: texto (default: 'criadoEm')
- sentido: 'ASC' ou 'DESC'

Retorna lista:

```js
id: numero
nome: texto
criadoPor: texto
criadoEm: texto
unidadeGestora: {
  id: numero
  codigo: numero
  nome: texto
},
origem: {
  id: numero
  codigo: texto
  descricao: texto
}
```

### Detalhes de arquivo Prestadores

[/arquivo-prestadores/detalhes/121282](http://localhost:3001/arquivo-prestadores/detalhes/121282)

Parâmetro:

- id: numero
- valores para teste:
  - 121282 (versão 2, 3 linhas)
  - 116529 (versão 2, 2 linhas)
  - 116460 (versão 2, 28 linhas)
  - 116753 (versão 2, 27 linhas)
  - 76538 (versão 2, com 1 linha de erro)
  - 59536 (versão 1, 21 linhas)

Retorna lista:

```js
id: numero
nome: numero
criadoEm: texto
unidadeGestora:
  id: numero
  codigo: numero
  nome: texto
conteudoJSON:
  { -- se tipo de registro = 1
    tipoRegistro: numero,
    dataGeracao: numero,
    horaGeracao: numero,
    rotuloArquivo: texto,
    origemArquivo: texto,
    unidadeGestora: numero,
    gestao: numero,
    expansao:
  },
  { -- se tipo de registro = 2
    tipoRegistro: numero,
    tipoDocumento: numero,
    numeroDocumento: numero,
    númeroSerie: numero,
    numeroSubserie: numero,
    modelo: numero,
    sigla: numero,
    dataEmissao: numero,
    dataApresentacao: numero,
    tipoCredor: numero,
    cpfCnpj: numero,
    codigoBarras: numero,
    tipoCodigoBarras: numero,
    valor: numero,
    gpsCodigoPagamento: numero,
    gpsCompetencia: numero,
    gpsIdentificador: numero,
    categoriaGFIP: numero,
    ocorrenciaGFIP: numero,
    cboGFIP: numero,
    expansao: texto
  },
  { -- se tipo de registro = 3
    tipoRegistro: numero,
    tipoDocumento: numero,
    numeroDocumento: numero,
    tipoCredor: numero,
    cpfCnpj: numero,
    codigoRetencao: numero,
    credorRetencao: numero,
    codigoPagamento: numero,
    gpsCompetencia: numero,
    gpsIdentificador: numero,
    valorBaseRetencao: numero,
    percentualRetencao: numero,
    valorRetencao: numero,
    expansao: texto
  },
  { -- se tipo de registro = 7
    tipoRegistro: numero,
    tipoDocumento: numero,
    numeroDocumento: numero,
    tipoCredorBeneficiario: numero,
    cpfCnpjBeneficiario: numero,
    cmc: numero,
    aidf: numero,
    cnae: numero,
    cfps: numero,
    cst: numero,
    observacao: texto,
    expansao: texto
  },
  { -- se tipo de registro = 9
    tiporegistro: numero,
    somacontroleregistros: numero,
    expansao: texto
  }
erros: texto[]
```

### Envio de arquivo Prestadores

[/arquivo-prestadores/envio](http://localhost:3001/arquivo-prestadores/envio)

Payload - Multipart Form:

- arquivo: `anexar o arquivo`

```js
nomeArquivo: texto
tamanho: numero
conteudo: {
  arquivo: [
    {
      nome: texto,
      tipoCpfCnpj: numero,
      cpfCnpj: texto,
      valor: numero,
      valorBaseInss: numero,
      valorInss: numero,
      aliquotaInss: numero,
      gpsCodigo: numero,
      gpsCompetencia: numero,
      gpsIdentificador: numero,
      gfipCategoria: numero,
      gfipOcorrencia: numero,
      gfipCbo: numero,
      tipoDocumento: numero,
      numeroDocumento: numero,
      dataEmissao: numero,
      dataApresentacao: numero,
      deObservacao: texto,
      nuCpfAtestador: numero
    },
    ...
  ],
  validacao: {
    validacaoArquivo: [
      {
        linha: numero,
        campo: texto,
        mensagem: texto
      }
    ],
    validacaoLinhas: [
      {
        linha: numero,
        validacaoCampos: [
          {
            campo: texto,
            validacao: {
                valido: booleano,
                errosValidacao: [
                  texto,
                  ...
                ]
            }
          },
          ...
        ]
      },
      ...
    ],
  }
}
```

## Arquivo APP

### Combo de origem (APP), com filtro de UG

[/origem-app/ug/5](http://localhost:3001/origem-app/ug/5)

Parâmetro:

- ug: numero

Retorna lista:

```js
id: numero;
codigo: numero;
descricao: texto;
```

### Combo de origem (APP)

[/origem-app](http://localhost:3001/origem-prestadores)

Retorna lista:

```js
id: numero;
codigo: numero;
descricao: texto;
```
