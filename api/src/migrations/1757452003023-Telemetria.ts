import type { MigrationInterface, QueryRunner } from "typeorm";

export class Telemetria1757452003023 implements MigrationInterface {
  async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TABLE cpesc_rota (
            "id" NUMBER GENERATED BY DEFAULT ON NULL AS IDENTITY PRIMARY KEY,
            "url" VARCHAR2(255) NOT NULL,
            "possui_parametros" NUMBER(1) NOT NULL
        )`,
    );

    await queryRunner.query(
      `INSERT ALL
        INTO cpesc_rota ("id", "url", "possui_parametros") VALUES (1, '/', 0)
        INTO cpesc_rota ("id", "url", "possui_parametros") VALUES (2, '/acessonegado', 0)
        INTO cpesc_rota ("id", "url", "possui_parametros") VALUES (3, '/erro', 0)
        INTO cpesc_rota ("id", "url", "possui_parametros") VALUES (4, '/requisicaoinvalida', 0)
        INTO cpesc_rota ("id", "url", "possui_parametros") VALUES (5, '/visaogeral', 0)
        INTO cpesc_rota ("id", "url", "possui_parametros") VALUES (6, '/login', 0)
        INTO cpesc_rota ("id", "url", "possui_parametros") VALUES (7, '/admin/gerenciar-usuario', 0)
        INTO cpesc_rota ("id", "url", "possui_parametros") VALUES (8, '/proxy/sau', 0)
        INTO cpesc_rota ("id", "url", "possui_parametros") VALUES (9, '/prestacao-contas', 0)
        INTO cpesc_rota ("id", "url", "possui_parametros") VALUES (10, '/prestacao-contas/selecao-credito', 0)
        INTO cpesc_rota ("id", "url", "possui_parametros") VALUES (11, '/prestacao-contas/extrato/:id', 1)
        INTO cpesc_rota ("id", "url", "possui_parametros") VALUES (12, '/prestacao-contas/relatorio-por-produto', 0)
      SELECT * FROM dual`,
    );

    await queryRunner.query(
      `CREATE TABLE cpesc_telemetria_evento (
            "id" NUMBER GENERATED BY DEFAULT ON NULL AS IDENTITY PRIMARY KEY,
            "tipo_evento" NUMBER NOT NULL,
            "rota_id" NUMBER NOT NULL REFERENCES cpesc_rota ("id"),
            "rota_frontend" VARCHAR2(255) NOT NULL,
            "rota_api" VARCHAR2(255),
            "verbo_http" VARCHAR2(10),
            "usuario_id" NUMBER NOT NULL,
            "created_at" TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
        )`,
    );

    await queryRunner.query(
      `CREATE TABLE cpesc_telemetria_evento_resposta (
            "id" NUMBER GENERATED BY DEFAULT ON NULL AS IDENTITY PRIMARY KEY,
            "evento_id" NUMBER NOT NULL REFERENCES cpesc_telemetria_evento ("id"),
            "status_code" NUMBER NOT NULL,
            "tempo_execucao" NUMBER NOT NULL
        )`,
    );
  }

  async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query("DROP TABLE cpesc_telemetria_evento_resposta");
    await queryRunner.query("DROP TABLE cpesc_telemetria_evento");
    await queryRunner.query("DROP TABLE cpesc_rota");
  }
}
