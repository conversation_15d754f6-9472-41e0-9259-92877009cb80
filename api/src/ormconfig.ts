import * as dotenv from "dotenv";
import type { DataSourceOptions } from "typeorm";

dotenv.config();

export const ormconfig: DataSourceOptions = {
  type: "oracle",
  username: process.env.DB_USERNAME,
  password: process.env.DB_PASSWORD,
  connectString: process.env.DB_CONNECTION_STRING,
  logging: true,
  entities: ["dist/**/*.entity{.ts,.js}"],
  migrations: ["dist/src/migrations/*.js"],
  migrationsTableName: "CPESC_MIGRATIONS",
};
