import { Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { AppController } from "./app.controller.js";
import { AppService } from "./app.service.js";
import { AuthModule } from "./modules/auth/auth.module.js";
import { CartaoModule } from "./modules/cartao/cartao.module.js";
import { CentroCustoModule } from "./modules/centro-custo/centro-custo.module.js";
import { ContaBancoModule } from "./modules/conta-banco/conta-banco.module.js";
import { DocumentoFiscalItemModule } from "./modules/documento-fiscal-item/documento-fiscal-item.module.js";
import { DocumentoFiscalModule } from "./modules/documento-fiscal/documento-fiscal.module.js";
import { FimLimiteCartaoModule } from "./modules/fim-limite-cartao/fim-limite-cartao.module.js";
import { ItemFiscalModule } from "./modules/item-fiscal/item-fiscal.module.js";
import { LimiteCartaoModule } from "./modules/limite-cartao/limite-cartao.module.js";
import { MovimentacaoModule } from "./modules/movimentacao/movimentacao.module.js";
import { MunicipioModule } from "./modules/municipio/municipio.module.js";
import { OrgaosModule } from "./modules/orgaos/orgaos.module.js";
import { PortadorModule } from "./modules/portador/portador.module.js";
import { RelatoriosModule } from "./modules/relatorios/relatorios.module.js";
import { SharedModule } from "./modules/shared/shared.module.js";
import { SubelementoModule } from "./modules/subelemento/subelemento.module.js";
import { TelemetriaModule } from "./modules/telemetria/telemetria.module.js";
import { UnidadeAdministrativaModule } from "./modules/unidade-administrativa/unidade-administrativa.module.js";
import { UnidadeGestoraModule } from "./modules/unidade-gestora/unidade-gestora.module.js";
import { UsuarioModule } from "./modules/usuario/usuario.module.js";
import { ormconfig } from "./ormconfig.js";

@Module({
  imports: [
    AuthModule,
    SharedModule,
    TypeOrmModule.forRoot(ormconfig),
    UsuarioModule,
    PortadorModule,
    OrgaosModule,
    CentroCustoModule,
    UnidadeGestoraModule,
    ContaBancoModule,
    MunicipioModule,
    UnidadeAdministrativaModule,
    CartaoModule,
    LimiteCartaoModule,
    SubelementoModule,
    DocumentoFiscalModule,
    DocumentoFiscalItemModule,
    MovimentacaoModule,
    ItemFiscalModule,
    RelatoriosModule,
    FimLimiteCartaoModule,
    TelemetriaModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
