import { NestFactory } from "@nestjs/core";
import cookieParser from "cookie-parser";
import { AppModule } from "./app.module.js";
import { EnvService } from "./modules/shared/env.service.js";

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const envService = app.get(EnvService);
  const port = envService.get("PORT");

  if (port === undefined) {
    throw new Error('Parâmetro "PORT" não foi definido');
  }

  app.enableCors({
    credentials: true,
    origin: envService.get("FRONTEND_URL"),
  });

  app.use(cookieParser());
  await app.listen(port);
}

void bootstrap();
