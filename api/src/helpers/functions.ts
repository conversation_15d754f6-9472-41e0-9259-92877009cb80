/**
 * Retorna apenas os números existentes em um texto
 * @param texto Conteúdo de string
 * @returns Número restante após o processamento
 */
export function apenasNumeros(texto: string): number {
  return parseInt(texto.replace(/\D/g, "")); /* \D = qualquer caractere que não seja um dígito */
}

export function validarEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function validarCPF(cpf: string): boolean {
  cpf = cpf.replace(/[^\d]+/g, ""); // Remove caracteres não numéricos

  if (cpf.length !== 11 || /^(\d)\1+$/.test(cpf)) {
    return false; // Verifica se o CPF tem 11 dígitos ou se todos os dígitos são iguais
  }

  let soma = 0;
  let digitoVerificador1;
  let digitoVerificador2;

  /**
   * Cálculo do primeiro dígito verificador
   */

  for (let i = 1; i <= 9; i++) {
    soma += parseInt(cpf.substring(i - 1, i)) * (11 - i);
  }

  digitoVerificador1 = (soma * 10) % 11;
  if (digitoVerificador1 === 10 || digitoVerificador1 === 11) {
    digitoVerificador1 = 0;
  }
  if (digitoVerificador1 !== parseInt(cpf.substring(9, 10))) {
    return false;
  }

  /**
   * Cálculo do segundo dígito verificador
   */

  soma = 0;

  for (let i = 1; i <= 10; i++) {
    soma += parseInt(cpf.substring(i - 1, i)) * (12 - i);
  }

  digitoVerificador2 = (soma * 10) % 11;
  if (digitoVerificador2 === 10 || digitoVerificador2 === 11) {
    digitoVerificador2 = 0;
  }
  if (digitoVerificador2 !== parseInt(cpf.substring(10, 11))) {
    return false;
  }

  return true;
}

export function aplicarMascaraCartao(cartao: string, ofuscar: boolean = false): string {
  if (cartao) {
    cartao = cartao.padStart(16, "0");
    return ofuscar
      ? cartao.replace(/([0-9]{4})([0-9]{4})([0-9]{4})([0-9]{4})/gi, "$1.****.****.$4")
      : cartao.replace(/([0-9]{4})([0-9]{4})([0-9]{4})([0-9]{4})/gi, "$1.$2.$3.$4");
  } else {
    return "";
  }
}

export function aplicarMascaraCPF(cpf: string, ofuscar = false): string {
  cpf = cpf.replace(/([0-9*]{3})([0-9*]{3})([0-9*]{3})([0-9*]{2})/gi, "$1.$2.$3-$4");

  if (ofuscar) {
    cpf = "***" + cpf.substring(3, 12) + "**";
  }

  return cpf;
}

export function aplicarMascaraValor(numero: number): string {
  return new Intl.NumberFormat("pt-BR", {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(numero);
}
