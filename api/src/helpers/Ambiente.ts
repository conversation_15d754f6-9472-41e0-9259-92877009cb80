import z, { type ZodType } from "zod";

export interface Ambiente {
  DB_USERNAME: string;
  DB_PASSWORD: string;
  DB_CONNECTION_STRING: string;
  FRONTEND_URL: string;
  SAU_LOGIN_URL: string;
  ROTA_INICIAL_PORTADOR: string;
  JWT_EXPIRATION_TIME: number; // em segundos
  JWT_SECRET: string;
  EXPIRA_OTP: number; // em segundos
  PORT?: number; // opcional, 3001 por padrão
  EMAIL_HOST: string;
  EMAIL_PORT: number;
  EMAIL_USER: string;
  EMAIL_PASS: string;
  EMAIL_FROM: string;
}

export const envSchema: ZodType<Ambiente> = z.object({
  DB_USERNAME: z.string(),
  DB_PASSWORD: z.string(),
  DB_CONNECTION_STRING: z.string(),
  FRONTEND_URL: z.string(),
  SAU_LOGIN_URL: z.string(),
  ROTA_INICIAL_PORTADOR: z.string(),
  JWT_EXPIRATION_TIME: z.coerce.number().int().positive(),
  JWT_SECRET: z.string(),
  EXPIRA_OTP: z.coerce.number().int().positive(),
  PORT: z.coerce.number().int().positive().default(3001),
  EMAIL_HOST: z.string(),
  EMAIL_PORT: z.coerce.number().int().positive(),
  EMAIL_USER: z.string(),
  EMAIL_PASS: z.string(),
  EMAIL_FROM: z.string(),
});
