import { BadRequestException } from "@nestjs/common";
import type { MetodoHttp } from "cpesc-shared/out/endpoints/main.js";
import type { z, ZodType } from "zod";

interface Rota<
  TUri extends string = string,
  TMetodo extends MetodoHttp = MetodoHttp,
  TPayload extends ZodType = ZodType,
  TResposta extends ZodType = ZodType,
> {
  uri: TUri;
  metodo: TMetodo;
  tipoPayload: TPayload;
  tipoResposta: TResposta;
}

export function validarPayload<TPayload extends ZodType>(
  payload: unknown,
  rota: Rota<string, MetodoHttp, TPayload>,
): z.infer<TPayload> {
  const resultado = rota.tipoPayload.safeParse(payload);

  if (!resultado.success) {
    throw new BadRequestException({ sucesso: false, mensagem: `Payload inválido: ${resultado.error.message}` });
  }

  return resultado.data;
}
