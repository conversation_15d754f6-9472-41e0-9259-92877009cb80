import { jest } from "@jest/globals";
import { Perfil } from "cpesc-shared";
import type { Response } from "express";
import type { Socket } from "net";
import type { RequisicaoAutenticada } from "../modules/auth/auth.guard.js";

export function getMockRequest(): RequisicaoAutenticada {
  return {
    cookies: {},
    headers: {},
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- objeto mockado
    socket: { remoteAddress: undefined } as Socket,
    usuario: {
      id: 1,
      perfil: Perfil.AdministradorCpesc,
      nome: "teste",
      email: "<EMAIL>",
      ipAddress: null,
      tipoLogin: "otp",
      rotaInicial: "/",
    },
  } satisfies Partial<RequisicaoAutenticada> as RequisicaoAutenticada;
}

interface MockedResponse extends Response {
  getJson: <T>() => T | undefined;
}

export function getMockResponse(): MockedResponse {
  let json: object | undefined;

  return {
    status: jest.fn().mockReturnThis(),
    json: jest.fn((data: object | undefined) => (json = data)),
    send: jest.fn(),
    end: jest.fn(),
    getJson: () => json,
  } as unknown as MockedResponse;
}
