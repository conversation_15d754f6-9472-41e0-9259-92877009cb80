import { Modu<PERSON> } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { envSchema } from "../../helpers/Ambiente.js";
import { EnvService } from "./env.service.js";

@Module({
  imports: [
    ConfigModule.forRoot({
      validate: config => {
        const result = envSchema.safeParse(config);

        if (!result.success) {
          throw new Error(`Erro ao validar variáveis de ambiente: ${result.error.message}`);
        }

        return result.data;
      },
    }),
  ],
  exports: [EnvService],
  providers: [EnvService],
})
export class SharedModule {}
