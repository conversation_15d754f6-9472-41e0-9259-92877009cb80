import {
  BadRequestException,
  HttpStatus,
  Injectable,
  InternalServerErrorException,
  RequestTimeoutException,
} from "@nestjs/common";
import * as nodemailer from "nodemailer";

export interface RetornoComStatus {
  message: string;
  status: HttpStatus;
}
@Injectable()
export class EmailService {
  private transporter = nodemailer.createTransport({
    host: process.env.EMAIL_HOST,
    port: Number(process.env.EMAIL_PORT),
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS,
    },
  });

  async sendOtpEmail(to: string, otp: string, validate: string, ipAddress: string | null = null): Promise<void> {
    const senderName = "Autenticação CPESC";
    const mailOptions = {
      from: `"${senderName}" <${process.env.EMAIL_FROM}>`,
      to,
      subject: "Código de autenticação para acesso ao CPESC",
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2>Autenticação CPESC</h2>
          <p>Seu código para acesso é: <span style="font-size: 24px; font-weight: bold;">${otp}</span></p>
          <p>Válido até ${validate}</p>
          ${ipAddress ? `<p>Acesso solicitado do IP: ${ipAddress}</p>` : ""}
          <hr>
          <p style="font-size: 12px; color: #666;">Este é um email automático, não responda.</p>
        </div>
      `,
      text: `Autenticação CPESC \nSeu código para acesso é: ${otp}. Válido até ${validate}\n${ipAddress ? `Acesso solicitado do IP: ${ipAddress}` : ""}`,
    };

    try {
      const info = await this.transporter.sendMail(mailOptions);

      if (!info.response.startsWith("250")) {
        throw new InternalServerErrorException(`Erro ao enviar email: ${info.response}`);
      }
    } catch (err) {
      const error = err as Error;

      if (error.message.includes("Invalid login")) {
        throw new BadRequestException("Erro ao enviar OTP: Credenciais de email inválidas.");
      } else if (error.message.includes("connect ETIMEDOUT")) {
        throw new RequestTimeoutException("Erro ao enviar OTP: Não foi possível conectar ao servidor de email.");
      } else {
        throw new InternalServerErrorException(`Erro ao enviar OTP: ${error.message}`);
      }
    }
  }

  async sendNotificacao(to: string, subject: string, message: string): Promise<void> {
    const senderName = "Notificação CPESC";
    const mailOptions = {
      from: `"${senderName}" <${process.env.EMAIL_FROM}>`,
      to: `${to}`,
      subject: `${subject}`,
      html: `${message}`,
      text: `${message}`,
    };

    try {
      const info = await this.transporter.sendMail(mailOptions);

      if (!info.response.startsWith("250")) {
        throw new InternalServerErrorException(`Erro ao enviar email: ${info.response}`);
      }
    } catch (err) {
      const error = err as Error;

      if (error.message.includes("Invalid login")) {
        throw new BadRequestException("Erro ao enviar OTP: Credenciais de email inválidas.");
      } else if (error.message.includes("connect ETIMEDOUT")) {
        throw new RequestTimeoutException("Erro ao enviar OTP: Não foi possível conectar ao servidor de email.");
      } else {
        throw new InternalServerErrorException(`Erro ao enviar OTP: ${error.message}`);
      }
    }
  }
}
