import { Controller, Delete, Get, Param, UseGuards } from "@nestjs/common";
import { AuthGuard } from "../auth/auth.guard.js";
import { DocumentoFiscalItemService } from "./documento-fiscal-item.service.js";

@Controller("documento-fiscal-item")
@UseGuards(AuthGuard)
export class DocumentoFiscalItemController {
  constructor(private readonly documentoFiscalItemService: DocumentoFiscalItemService) {}

  @Get()
  async findAll() {
    return this.documentoFiscalItemService.findAll();
  }

  @Get(":id")
  async findOne(@Param("id") id: string) {
    return this.documentoFiscalItemService.findOneByDynamic({ id: parseInt(id) });
  }

  @Delete(":id")
  async remove(@Param("id") id: string) {
    return this.documentoFiscalItemService.remove(parseInt(id));
  }
}
