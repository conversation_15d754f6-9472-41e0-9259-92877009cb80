import { DocumentoFiscalItem } from "cpesc-shared";
import {
  <PERSON>umn,
  CreateDate<PERSON><PERSON>umn,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>um<PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { ApiDocumentoFiscal } from "../documento-fiscal/documento-fiscal.entity.js";

@Entity("CPESC_DOCUMENTOFISCALITEM")
export class ApiDocumentoFiscalItem implements DocumentoFiscalItem {
  @PrimaryGeneratedColumn({ name: "ID" })
  id: number;

  @Column({ name: "DOCUMENTOFISCAL_ID" })
  documentoFiscalId: number;

  @Column({ name: "TIPOCOMPRA_ID" })
  tipocompraId: number;

  @Column({ name: "NCM", nullable: true, type: String })
  ncm: string;

  @Column({ name: "DESCRICAO", nullable: true, type: String })
  descricao: string;

  @Column({ name: "QUANTIDADE" })
  quantidade: number;

  @Column({ name: "UNIDADE" })
  unidade: string;

  @Column({ name: "VALORUNITAR<PERSON>", type: "decimal" })
  valorunitario: number | null;

  @Column({ name: "VALOR", type: "decimal" })
  valor: number;

  @Column({ name: "PROCESSOTIPOCOMPRA" })
  processotipocompra: string;

  @Column({ name: "CRIADOPOR" })
  criadoPor: string;

  @CreateDateColumn({ name: "CRIADOEM" })
  criadoEm: Date;

  @Column({ name: "ATUALIZADOPOR" })
  atualizadoPor: string;

  @UpdateDateColumn({ name: "ATUALIZADOEM" })
  atualizadoEm: Date;

  @ManyToOne("ApiDocumentoFiscal", (nf: ApiDocumentoFiscal) => nf.id)
  @JoinColumn({ name: "DOCUMENTOFISCAL_ID" })
  documentofiscal: ApiDocumentoFiscal;
}
