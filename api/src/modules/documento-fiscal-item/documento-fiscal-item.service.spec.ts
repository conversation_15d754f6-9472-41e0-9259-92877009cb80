import { jest } from "@jest/globals";
import { Test, type TestingModule } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import { DataSource } from "typeorm";
import { createDataSourceMock } from "../../test-utils/mock-datasource.js";
import { ApiItemFiscal } from "../item-fiscal/item-fiscal.entity.js";
import { ItemFiscalService } from "../item-fiscal/item-fiscal.service.js";
import { ApiDocumentoFiscalItem } from "./documento-fiscal-item.entity.js";
import { DocumentoFiscalItemService } from "./documento-fiscal-item.service.js";

describe("DocumentoFiscalItemService", () => {
  let service: DocumentoFiscalItemService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ItemFiscalService,
        DocumentoFiscalItemService,
        { provide: getRepositoryToken(ApiItemFiscal), useFactory: jest.fn },
        { provide: getRepositoryToken(ApiDocumentoFiscalItem), useFactory: jest.fn },
        { provide: DataSource, useFactory: createDataSourceMock },
      ],
    }).compile();

    service = module.get<DocumentoFiscalItemService>(DocumentoFiscalItemService);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });
});
