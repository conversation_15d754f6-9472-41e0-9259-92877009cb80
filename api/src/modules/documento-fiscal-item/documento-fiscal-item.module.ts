import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { AuthModule } from "../auth/auth.module.js";
import { ItemFiscalModule } from "../item-fiscal/item-fiscal.module.js";
import { PortadorModule } from "../portador/portador.module.js";
import { SharedModule } from "../shared/shared.module.js";
import { UsuarioModule } from "../usuario/usuario.module.js";
import { DocumentoFiscalItemController } from "./documento-fiscal-item.controller.js";
import { ApiDocumentoFiscalItem } from "./documento-fiscal-item.entity.js";
import { DocumentoFiscalItemService } from "./documento-fiscal-item.service.js";

@Module({
  controllers: [DocumentoFiscalItemController],
  providers: [DocumentoFiscalItemService],
  imports: [
    TypeOrmModule.forFeature([ApiDocumentoFiscalItem]),
    AuthModule,
    PortadorModule,
    UsuarioModule,
    SharedModule,
    ItemFiscalModule,
  ],
  exports: [DocumentoFiscalItemService],
})
export class DocumentoFiscalItemModule {}
