import { Injectable, InternalServerErrorException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { PayloadCriarDocFiscalItem } from "cpesc-shared/src/endpoints/documento-fiscal-item.js";
import { DataSource, FindOptionsWhere, Repository } from "typeorm";
import { ApiDocumentoFiscalItem } from "./documento-fiscal-item.entity.js";

@Injectable()
export class DocumentoFiscalItemService {
  constructor(
    @InjectRepository(ApiDocumentoFiscalItem) private documentoFiscalItemRepository: Repository<ApiDocumentoFiscalItem>,
    private dataSource: DataSource,
  ) {}

  async criarItemManual(
    dadosDocumentoFiscalItens: PayloadCriarDocFiscalItem[],
    documentoFiscalId: number,
    email: string,
  ): Promise<void> {
    try {
      const itens = dadosDocumentoFiscalItens.map(
        (item): Omit<ApiDocumentoFiscalItem, "id" | "documentofiscal" | "criadoEm" | "atualizadoEm"> => ({
          ...item,
          documentoFiscalId,
          criadoPor: email,
          atualizadoPor: email,
        }),
      );

      const nf = this.documentoFiscalItemRepository.create(itens);
      await this.documentoFiscalItemRepository.save(nf);
    } catch (error) {
      console.log("Erro ao salvar item de documento fiscal", error);
    }
  }

  async atualizarItem(
    dadosDocumentoFiscalItem: ApiDocumentoFiscalItem[],
    documentoFiscalId: number,
    email: string,
  ): Promise<void> {
    try {
      // 1. Buscar todos os IDs de itens existentes para esta nota fiscal
      const itensExistentes = await this.documentoFiscalItemRepository.find({
        where: { documentoFiscalId: documentoFiscalId },
        select: { id: true },
      });

      // 2. Separar itens para atualizar e itens novos
      const itensParaAtualizar: ApiDocumentoFiscalItem[] | null = [];
      const itensNovos: Omit<ApiDocumentoFiscalItem, "id" | "itemfiscal">[] = [];

      // Mapear IDs dos itens recebidos que já existem
      const idsRecebidos = new Set<number>();

      dadosDocumentoFiscalItem.forEach(item => {
        if ("id" in item && item.id > 0) {
          idsRecebidos.add(item.id);
          itensParaAtualizar.push({
            ...item,
            atualizadoPor: email,
          });
        } else {
          itensNovos.push({
            ...item,
            documentoFiscalId: documentoFiscalId,
            criadoPor: email,
            atualizadoPor: email,
          });
        }
      });

      // 3. Identificar IDs para remover (existentes mas não recebidos)
      const idsParaRemover = itensExistentes.filter(item => !idsRecebidos.has(item.id)).map(item => item.id);

      // 4. Executar as operações sequencialmente
      // Remover itens que não existem mais
      if (idsParaRemover.length > 0) {
        await this.documentoFiscalItemRepository.delete(idsParaRemover);
      }

      // Atualizar itens existentes
      if (itensParaAtualizar.length > 0) {
        await this.documentoFiscalItemRepository.save(itensParaAtualizar);
      }

      // Inserir novos itens
      if (itensNovos.length > 0) {
        const novosItens = this.documentoFiscalItemRepository.create(itensNovos);
        await this.documentoFiscalItemRepository.save(novosItens);
      }
    } catch (error) {
      console.log("Erro ao atualizar itens de nota fiscal", error);
    }
  }

  async findAll() {
    return this.documentoFiscalItemRepository.find();
  }

  async findOneByDynamic(params: FindOptionsWhere<ApiDocumentoFiscalItem>) {
    return this.documentoFiscalItemRepository.findOneBy(params);
  }

  async remove(idPar: number) {
    return this.documentoFiscalItemRepository.delete({ id: idPar });
  }

  async removeByDocumentoFiscalId(documentoFiscalId: number): Promise<void> {
    await this.documentoFiscalItemRepository.delete({ documentoFiscalId: documentoFiscalId });
  }

  async getDocumentoFiscalItemNoSIGEF(
    codigoSigef: number,
    iddoc: number,
    email: string,
  ): Promise<Partial<ApiDocumentoFiscalItem>[]> {
    try {
      const dados: {
        NCMPRODUTO: string | null;
        DESCRICAO: string;
        QUANTIDADE: number;
        VALOR: number;
        VALORUNITARIO: number;
        UNIDADE: string;
      }[] = await this.dataSource.query(
        `SELECT CDNCMPRODUTO AS NCMPRODUTO, DEPRODUTO AS DESCRICAO, QTCOMERCIAL AS QUANTIDADE, DEUNIDCOMERCIAL AS UNIDADE, VLTOTAL AS VALOR, VLUNITCOMERCIAL AS VALORUNITARIO
           FROM SIGEF.EFINNOTAFISCALSATITEM@SIGEFCPESC
           WHERE NUNFEID = :pCodSigef`,
        [codigoSigef],
      );

      const itens: Partial<ApiDocumentoFiscalItem>[] = dados.map(element => {
        return {
          ncm: element.NCMPRODUTO ?? undefined,
          descricao: element.DESCRICAO,
          quantidade: element.QUANTIDADE,
          valor: element.VALOR,
          valorunitario: element.VALORUNITARIO,
          unidade: element.UNIDADE,
          criadoEm: new Date(),
          atualizadoEm: new Date(),
          documentoFiscalId: iddoc,
          criadoPor: email,
          atualizadoPor: email,
        };
      });

      return itens;
    } catch (error) {
      console.error("Erro ao buscar itens da Nota Fiscal no SIGEF:", error);
      throw new InternalServerErrorException("Falha ao buscar itens da Nota Fiscal no SIGEF");
    }
  }

  async criarItemBuscandoSigef(codigoSigef: number, idDocumentoFiscal: number, email: string) {
    try {
      const itens = await this.getDocumentoFiscalItemNoSIGEF(codigoSigef, idDocumentoFiscal, email);
      if (itens.length > 0) {
        const nfi = this.documentoFiscalItemRepository.create(itens);
        await this.documentoFiscalItemRepository.save(nfi);
      }
      return;
    } catch (error) {
      console.log("Erro ao criar item de nota fiscal", error);
      return;
    }
  }
}
