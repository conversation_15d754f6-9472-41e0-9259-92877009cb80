import {
  BadRequestException,
  Body,
  ClassSerializerInterceptor,
  Controller,
  Get,
  Param,
  Patch,
  Req,
  UseGuards,
  UseInterceptors,
} from "@nestjs/common";
import { endpoints, type RespostaRota } from "cpesc-shared/out/endpoints/main.js";
import { AuthGuard, type RequisicaoAutenticada } from "../auth/auth.guard.js";
import { ApiLimiteCartao } from "./limite-cartao.entity.js";
import { LimiteCartaoService } from "./limite-cartao.service.js";

@Controller("limite-cartao")
@UseGuards(AuthGuard)
export class LimiteCartaoController {
  constructor(private readonly limiteCartaoService: LimiteCartaoService) {}
  //Para fazer funcionar o decorator @Exclude() do campo na entity portador é necessário utilizar a linha abaixo
  @UseInterceptors(ClassSerializerInterceptor)
  @Get()
  async findAll() {
    return this.limiteCartaoService.getLimiteCartoes();
  }

  @UseInterceptors(ClassSerializerInterceptor)
  @Get("gastos/:id")
  async findGastosCartaoPorId(
    @Req() request: RequisicaoAutenticada,
    @Param("id") id: string,
  ): RespostaRota<typeof endpoints.buscarGastosCartao> {
    const idN = parseInt(id);
    if (isNaN(idN) || idN < 1) {
      throw new BadRequestException("Id inválido");
    }
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    return this.limiteCartaoService.getGastosPorCreditoId(request.usuario!, idN);
  }

  @UseInterceptors(ClassSerializerInterceptor)
  @Get("portador/:id")
  async findSelecaoCreditoPorPortador(
    @Param("id") id: number,
  ): RespostaRota<typeof endpoints.buscarSelecaoCreditoPorPortador> {
    return this.limiteCartaoService.getLimitesPorPortadorId(id);
  }

  @UseInterceptors(ClassSerializerInterceptor)
  @Get("portador/:idPortador/ua/:idUa")
  async findSelecaoCreditoPorPortadorUa(
    @Param("idPortador") idPortador: number,
    @Param("idUa") idUa: number,
  ): RespostaRota<typeof endpoints.buscarSelecaoCreditoPorPortadorUA> {
    return this.limiteCartaoService.getLimitesPorPortadorUa(idPortador, idUa);
  }

  @Patch(":id/finalizar")
  async finalizarLimiteCartao(@Param("id") id: string): RespostaRota<typeof endpoints.finalizarLimiteCartao> {
    await this.limiteCartaoService.finalizarLimiteCartao(+id);

    return { sucesso: true };
  }

  @Patch(":id")
  async update(@Param("id") id: string, @Body() updateSelecaoCredito: Partial<ApiLimiteCartao>) {
    return this.limiteCartaoService.updateGastosCartao(+id, updateSelecaoCredito);
  }
}
