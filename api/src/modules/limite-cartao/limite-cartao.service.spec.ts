import { jest } from "@jest/globals";
import { Test, type TestingModule } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import { DataSource } from "typeorm";
import { createDataSourceMock } from "../../test-utils/mock-datasource.js";
import { ApiDocumentoFiscalItem } from "../documento-fiscal-item/documento-fiscal-item.entity.js";
import { DocumentoFiscalItemService } from "../documento-fiscal-item/documento-fiscal-item.service.js";
import { ApiDocumentoFiscal } from "../documento-fiscal/documento-fiscal.entity.js";
import { DocumentoFiscalService } from "../documento-fiscal/documento-fiscal.service.js";
import { ApiItemFiscal } from "../item-fiscal/item-fiscal.entity.js";
import { ItemFiscalService } from "../item-fiscal/item-fiscal.service.js";
import { ApiMovimentacao } from "../movimentacao/movimentacao.entity.js";
import { MovimentacaoService } from "../movimentacao/movimentacao.service.js";
import { ApiUnidadeGestora } from "../unidade-gestora/unidade-gestora.entity.js";
import { UnidadeGestoraService } from "../unidade-gestora/unidade-gestora.service.js";
import { ApiLimiteCartao } from "./limite-cartao.entity.js";
import { LimiteCartaoService } from "./limite-cartao.service.js";

describe("LimiteCartaoService", () => {
  let service: LimiteCartaoService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DocumentoFiscalService,
        DocumentoFiscalItemService,
        ItemFiscalService,
        LimiteCartaoService,
        MovimentacaoService,
        UnidadeGestoraService,
        { provide: getRepositoryToken(ApiDocumentoFiscal), useFactory: jest.fn },
        { provide: getRepositoryToken(ApiDocumentoFiscalItem), useFactory: jest.fn },
        { provide: getRepositoryToken(ApiItemFiscal), useFactory: jest.fn },
        { provide: getRepositoryToken(ApiLimiteCartao), useFactory: jest.fn },
        { provide: getRepositoryToken(ApiMovimentacao), useFactory: jest.fn },
        { provide: getRepositoryToken(ApiUnidadeGestora), useFactory: jest.fn },
        { provide: DataSource, useFactory: createDataSourceMock },
      ],
    }).compile();

    service = module.get<LimiteCartaoService>(LimiteCartaoService);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });
});
