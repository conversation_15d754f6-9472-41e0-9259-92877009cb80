import { Credito } from "cpesc-shared";
import {
  <PERSON><PERSON><PERSON>,
  CreateDateColumn,
  Entity,
  <PERSON>inC<PERSON><PERSON>n,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import type { ApiCartao } from "../cartao/cartao.entity.js";
import { ApiFimLimiteCartao } from "../fim-limite-cartao/fim-limite-cartao.entity.js";
import type { ApiMovimentacao } from "../movimentacao/movimentacao.entity.js";
import type { ApiSubelemento } from "../subelemento/subelemento.entity.js";

@Entity("CPESC_LIMITECARTAO")
export class ApiLimiteCartao implements Credito {
  @PrimaryGeneratedColumn({ name: "ID" })
  id: number;

  @Column({
    name: "DATACREDITO",
  })
  dataCredito: Date;

  @Column({ name: "DATAVENCIMENTO" })
  dataVencimento: Date;

  @Column({ name: "DATAINICIOMOVIMENTACAO" })
  dataInicioMovimentacao: Date;

  @Column({ name: "DATALIMITEMOVIMENTACAO" })
  dataLimiteMovimentacao: Date;

  @Column({
    name: "DATAZERAMENTO",
    nullable: true,
  })
  dataZeramento: Date;

  @Column({ name: "VALOR" })
  valorCredito: number;

  @Column({ name: "NUNOTAEMPENHO" })
  notaEmpenho: string;

  @Column({ name: "NUNOTALANCAMENTO" })
  notaLancamento: string;

  @Column({ name: "NUPREPARACAOPAGAMENTO" })
  preparacaopagamento: string;

  @Column({ name: "NUORDEMBANCARIA" })
  ordemBancaria: string;

  @Column({ name: "CDFONTE" })
  fonteRecurso: string;

  @Column({ name: "CDUNIDADEGESTORA" })
  cdUnidadeGestora: string;

  @Column({ name: "ATUALIZADOPOR" })
  atualizadoPor: string;

  @Column({ name: "CRIADOPOR" })
  criadoPor: string;

  @CreateDateColumn({ name: "CRIADOEM" })
  criadoEm: Date;

  @UpdateDateColumn({ name: "ATUALIZADOEM" })
  atualizadoEm: Date;

  @ManyToOne("ApiSubelemento")
  @JoinColumn({ name: "SUBELEMENTO_ID" })
  subelemento: ApiSubelemento;

  @OneToOne("ApiFimLimiteCartao", (pc: ApiFimLimiteCartao) => pc.limite)
  fimLimiteCartao: ApiFimLimiteCartao | null;

  @ManyToOne("ApiCartao")
  @JoinColumn({ name: "CARTAO_ID" })
  cartao: ApiCartao;

  @OneToMany("ApiMovimentacao", (m: ApiMovimentacao) => m.limiteCartao)
  movimentacoes: ApiMovimentacao[];
}
