import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { SharedModule } from "../shared/shared.module.js";
import { SubelementoController } from "./subelemento.controller.js";
import { ApiSubelemento } from "./subelemento.entity.js";
import { SubelementoService } from "./subelemento.service.js";

@Module({
  imports: [TypeOrmModule.forFeature([ApiSubelemento]), SharedModule],
  controllers: [SubelementoController],
  providers: [SubelementoService],
  exports: [SubelementoService],
})
export class SubelementoModule {}
