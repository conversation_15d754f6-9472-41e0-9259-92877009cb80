import { jest } from "@jest/globals";
import { Test, type TestingModule } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import { ApiSubelemento } from "./subelemento.entity.js";
import { SubelementoService } from "./subelemento.service.js";

describe("SubelementoService", () => {
  let service: SubelementoService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [SubelementoService, { provide: getRepositoryToken(ApiSubelemento), useFactory: jest.fn }],
    }).compile();

    service = module.get<SubelementoService>(SubelementoService);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });
});
