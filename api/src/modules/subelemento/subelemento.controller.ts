import { Controller, Get, Param } from "@nestjs/common";
import { SubelementoService } from "./subelemento.service.js";

@Controller("subelemento")
//@UseGuards(AuthGuard)
export class SubelementoController {
  constructor(private readonly subelemntoService: SubelementoService) {}

  @Get()
  async findAll() {
    return this.subelemntoService.getSubelemntos();
  }

  @Get("/:id")
  async findSubelementoPorId(@Param("id") id: string) {
    return this.subelemntoService.getSubelementoPorId(+id);
  }
}
