import { Subelemento } from "cpesc-shared";
import { <PERSON><PERSON><PERSON>, CreateDate<PERSON>olumn, <PERSON>tity, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";

@Entity("CPESC_SUBELEMENTO")
export class ApiSubelemento implements Subelemento {
  @PrimaryGeneratedColumn({ name: "ID" })
  id: number;

  @Column({ name: "CODIG<PERSON>" })
  subelemento: string;

  @Column({ name: "NOME" })
  nome: string;

  @Column({ name: "ATIVO" })
  ativo: string;

  @Column({ name: "ATUALIZADOPOR" })
  updatedBy: string;

  @Column({ name: "CRIADOP<PERSON>" })
  createdBy: string;

  @UpdateDateColumn({ name: "ATUALI<PERSON><PERSON><PERSON><PERSON>" })
  updatedAt: Date;

  @CreateDateColumn({ name: "CRIADOEM" })
  createdAt: Date;
}
