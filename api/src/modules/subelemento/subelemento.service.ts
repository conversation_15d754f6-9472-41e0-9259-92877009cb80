import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { ApiSubelemento } from "./subelemento.entity.js";

@Injectable()
export class SubelementoService {
  constructor(@InjectRepository(ApiSubelemento) private subelementoRepository: Repository<ApiSubelemento>) {}

  async getSubelemntos(): Promise<ApiSubelemento[]> {
    return this.subelementoRepository.find();
  }

  async getSubelementoPorId(id: number): Promise<ApiSubelemento | null> {
    return this.subelementoRepository.findOneBy({ id });
  }

  async setSubelemento(subelemento: ApiSubelemento): Promise<void> {
    await this.subelementoRepository.save(subelemento);
  }
}
