import { jest } from "@jest/globals";
import { Test, type TestingModule } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import { ApiItemFiscal } from "./item-fiscal.entity.js";
import { ItemFiscalService } from "./item-fiscal.service.js";

describe("ItemFiscalService", () => {
  let service: ItemFiscalService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ItemFiscalService, { provide: getRepositoryToken(ApiItemFiscal), useFactory: jest.fn }],
    }).compile();

    service = module.get<ItemFiscalService>(ItemFiscalService);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });
});
