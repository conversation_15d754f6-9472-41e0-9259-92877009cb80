import { BadRequestException, Controller, Delete, Get, Param, Query, UseGuards } from "@nestjs/common";
import { limparTexto, MIN_BUSCA } from "cpesc-shared";
import { endpoints, type RespostaRota } from "cpesc-shared/out/endpoints/main.js";
import { AuthGuard } from "../auth/auth.guard.js";
import { ItemFiscalService } from "./item-fiscal.service.js";

@Controller("item-fiscal")
@UseGuards(AuthGuard)
export class ItemFiscalController {
  constructor(private readonly itemFiscalService: ItemFiscalService) {}

  @Get()
  async findAll(): RespostaRota<typeof endpoints.buscarItensFiscais> {
    return this.itemFiscalService.findAll();
  }

  @Get("/:busca/:tipo")
  async find(
    @Param("busca") busca: string,
    @Param("tipo") tipoGasto: string,
    @Query("skip") skip?: string,
    @Query("take") take?: string,
    @Query("ncm") ncm?: string,
  ): RespostaRota<typeof endpoints.buscarItensFiscaisPorNcmETipoGasto> {
    const skipNum = skip ? parseInt(skip) : 0;
    const takeNum = take ? parseInt(take) : 10;
    if (isNaN(skipNum) || isNaN(takeNum)) {
      throw new BadRequestException("Parâmetros inválidos");
    }
    const buscaNcm: boolean = ncm === "True";

    if (busca.length < MIN_BUSCA) {
      return { items: [], total: 0 };
    }
    return this.itemFiscalService.getNcmBySearch(
      limparTexto(busca).toUpperCase(),
      tipoGasto.toUpperCase(),
      skipNum,
      takeNum,
      buscaNcm,
    );
  }

  @Delete("/:id")
  async remove(@Param("id") id: string) {
    return this.itemFiscalService.remove(+id);
  }
}
