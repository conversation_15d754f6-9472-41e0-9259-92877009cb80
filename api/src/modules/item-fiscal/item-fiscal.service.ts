import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { MIN_BUSCA } from "cpesc-shared";
import { Like, Repository } from "typeorm";
import { ApiItemFiscal } from "./item-fiscal.entity.js";

@Injectable()
export class ItemFiscalService {
  constructor(@InjectRepository(ApiItemFiscal) private documentoFiscalItemRepository: Repository<ApiItemFiscal>) {}

  async findAll() {
    return this.documentoFiscalItemRepository.find();
  }

  async getNcmBySearch(
    params: string,
    tipoGasto: string,
    skip = 0,
    take = 10,
    buscaNcm = false,
  ): Promise<{ items: ApiItemFiscal[]; total: number }> {
    const paramsUpper = params.toUpperCase();

    const numericRegex = /\d+/g;
    const matchResult = params.match(numericRegex);
    const paramsNum = matchResult?.[0] ?? "";

    //testa se é um número inteiro
    if (Number.isInteger(parseInt(paramsNum, 10))) {
      const ncmByNumber = await this.getNCMByNumber(paramsNum, tipoGasto, buscaNcm); //passa a string para nao perder o zero no incio
      return { items: ncmByNumber, total: ncmByNumber.length };
    } else {
      //caso nao encontrar por ncm, busca no campo pesquisa
      const [ncmsEncontrados, total] = await this.documentoFiscalItemRepository.findAndCount({
        where: [{ pesquisa: Like(`% ${paramsUpper}%`), tipo: tipoGasto, ncm: Like(`${"_".repeat(MIN_BUSCA)}%`) }],
        order: {
          ncm: "ASC",
        },
        skip,
        take,
      });
      return { items: ncmsEncontrados, total };
    }
  }

  async getNCMByNumber(ncm: string, tipoGasto: string, buscaNcm: boolean): Promise<ApiItemFiscal[]> {
    const ncmNumber = ncm.slice(0, 8);

    if (!isNaN(parseInt(ncm, 10))) {
      if (buscaNcm) {
        //se for busca exata por ncm, traz correspondencia exata
        const ncm = await this.documentoFiscalItemRepository.findOne({
          where: { ncm: ncmNumber, tipo: tipoGasto },
        });
        return ncm ? [ncm] : [];
      } else {
        //se for busca por aproximacao, traz todos que comecem com os digitos informados
        const ncms = await this.documentoFiscalItemRepository.find({
          where: { ncm: Like(`${ncmNumber}%`), tipo: tipoGasto },
          order: {
            ncm: "ASC",
          },
        });
        if (ncms.length > 0) {
          return ncms;
        }
      }
    }
    return [];
  }

  async remove(idPar: number) {
    return this.documentoFiscalItemRepository.delete({ id: idPar });
  }
}
