import { jest } from "@jest/globals";
import { Test, type TestingModule } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import { ApiUnidadeGestora } from "./unidade-gestora.entity.js";
import { UnidadeGestoraService } from "./unidade-gestora.service.js";

describe("UnidadeGestoraService", () => {
  let service: UnidadeGestoraService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [UnidadeGestoraService, { provide: getRepositoryToken(ApiUnidadeGestora), useFactory: jest.fn }],
    }).compile();

    service = module.get<UnidadeGestoraService>(UnidadeGestoraService);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });
});
