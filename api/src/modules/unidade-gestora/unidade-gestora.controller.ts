import { Controller, Get, Req, UseGuards } from "@nestjs/common";
import { assertNotNull } from "cpesc-shared/out/assertions.js";
import { endpoints, type RespostaRota } from "cpesc-shared/out/endpoints/main.js";
import { AuthGuard, type RequisicaoAutenticada } from "../auth/auth.guard.js";
import { UnidadeGestoraService } from "./unidade-gestora.service.js";

@Controller("unidade-gestora")
@UseGuards(AuthGuard)
export class UnidadeGestoraController {
  constructor(private readonly unidadeGestoraService: UnidadeGestoraService) {}

  @Get()
  async findUnidadeGestora(): RespostaRota<typeof endpoints.buscarUnidadesGestoras> {
    return this.unidadeGestoraService.findUnidadeGestora();
  }

  @Get("usuario")
  async findUnidadeGestoraPorUsuario(
    @Req() request: RequisicaoAutenticada,
  ): RespostaRota<typeof endpoints.buscarUnidadesGestorasPorUsuario> {
    assertNotNull(request.usuario, "Usuário não autenticado");
    return this.unidadeGestoraService.findUnidadeGestoraPorUsuario(request.usuario);
  }
}
