import { jest } from "@jest/globals";
import { ConfigService } from "@nestjs/config";
import { JwtService } from "@nestjs/jwt";
import { Test, type TestingModule } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import { DataSource } from "typeorm";
import { TokenService } from "../../helpers/token.service.js";
import { createDataSourceMock } from "../../test-utils/mock-datasource.js";
import { ApiDocumentoFiscalItem } from "../documento-fiscal-item/documento-fiscal-item.entity.js";
import { DocumentoFiscalItemService } from "../documento-fiscal-item/documento-fiscal-item.service.js";
import { ApiDocumentoFiscal } from "../documento-fiscal/documento-fiscal.entity.js";
import { DocumentoFiscalService } from "../documento-fiscal/documento-fiscal.service.js";
import { ApiItemFiscal } from "../item-fiscal/item-fiscal.entity.js";
import { ItemFiscalService } from "../item-fiscal/item-fiscal.service.js";
import { ApiLimiteCartao } from "../limite-cartao/limite-cartao.entity.js";
import { LimiteCartaoService } from "../limite-cartao/limite-cartao.service.js";
import { ApiMovimentacao } from "../movimentacao/movimentacao.entity.js";
import { MovimentacaoService } from "../movimentacao/movimentacao.service.js";
import { ApiPortador } from "../portador/portador.entity.js";
import { PortadorService } from "../portador/portador.service.js";
import { EnvService } from "../shared/env.service.js";
import { Usuario } from "../usuario/usuario.entity.js";
import { UsuarioService } from "../usuario/usuario.service.js";
import { UnidadeGestoraController } from "./unidade-gestora.controller.js";
import { ApiUnidadeGestora } from "./unidade-gestora.entity.js";
import { UnidadeGestoraService } from "./unidade-gestora.service.js";

describe("UnidadeGestoraController", () => {
  let controller: UnidadeGestoraController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [UnidadeGestoraController],
      providers: [
        ConfigService,
        DocumentoFiscalService,
        DocumentoFiscalItemService,
        EnvService,
        ItemFiscalService,
        JwtService,
        LimiteCartaoService,
        MovimentacaoService,
        PortadorService,
        TokenService,
        UnidadeGestoraService,
        UsuarioService,
        { provide: getRepositoryToken(ApiDocumentoFiscal), useFactory: jest.fn },
        { provide: getRepositoryToken(ApiDocumentoFiscalItem), useFactory: jest.fn },
        { provide: getRepositoryToken(ApiItemFiscal), useFactory: jest.fn },
        { provide: getRepositoryToken(ApiLimiteCartao), useFactory: jest.fn },
        { provide: getRepositoryToken(ApiMovimentacao), useFactory: jest.fn },
        { provide: getRepositoryToken(ApiPortador), useFactory: jest.fn },
        { provide: getRepositoryToken(ApiUnidadeGestora), useFactory: jest.fn },
        { provide: getRepositoryToken(Usuario), useFactory: jest.fn },
        { provide: DataSource, useFactory: createDataSourceMock },
      ],
    }).compile();

    controller = module.get<UnidadeGestoraController>(UnidadeGestoraController);
  });

  it("should be defined", () => {
    expect(controller).toBeDefined();
  });
});
