import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Perfil, UsuarioAutenticado } from "cpesc-shared";
import { Repository } from "typeorm";
import { ApiUnidadeGestora } from "./unidade-gestora.entity.js";

@Injectable()
export class UnidadeGestoraService {
  constructor(@InjectRepository(ApiUnidadeGestora) private unidadeGestoraRepository: Repository<ApiUnidadeGestora>) {}

  async findUnidadeGestora() {
    return this.unidadeGestoraRepository.find({ where: { ativo: "S" }, order: { nomeAbreviado: "ASC" } });
  }

  async findOneByCD(cd: number): Promise<ApiUnidadeGestora | null> {
    return this.unidadeGestoraRepository.findOne({
      where: { codigo: cd },
    });
  }

  async findUnidadeGestoraAtivaPorUsuario(usuario: UsuarioAutenticado) {
    if (usuario.perfil === Perfil.AdministradorCiasc || usuario.perfil === Perfil.AdministradorCpesc) {
      return this.unidadeGestoraRepository.find({
        where: { ativo: "S" },
        order: { nomeAbreviado: "ASC" },
      });
    } else {
      const dados = this.unidadeGestoraRepository
        .createQueryBuilder("UG")
        .innerJoin("CPESC_USUARIO_UNIDADEGESTORA", "UUG", "UUG.UNIDADEGESTORA_ID = UG.ID")
        .where("UUG.USUARIO_ID = :id", { id: usuario.id })
        .andWhere("UG.ATIVO = 'S'")
        .orderBy("UG.NOMEABREVIADO");
      return dados.getMany();
    }
  }

  async findUnidadeGestoraPorUsuario(usuario: UsuarioAutenticado) {
    if (usuario.perfil === Perfil.AdministradorCiasc) {
      return this.unidadeGestoraRepository.find({
        where: { ativo: "S" },
        order: { nomeAbreviado: "ASC" },
      });
    } else if (usuario.perfil != Perfil.Portador) {
      const dados = this.unidadeGestoraRepository
        .createQueryBuilder("UG")
        .innerJoin("CPESC_USUARIO_UNIDADEGESTORA", "UUG", "UUG.UNIDADEGESTORA_ID = UG.ID")
        .where("UUG.USUARIO_ID = :id", { id: usuario.id })
        .orderBy("UG.NOMEABREVIADO");
      return dados.getMany();
    } else {
      const dados = this.unidadeGestoraRepository
        .createQueryBuilder("UG")
        .innerJoin("CPESC_UNIDADEADMINISTRATIVA", "UA", "UA.UNIDADEGESTORA_ID = UG.ID")
        .innerJoin("CPESC_PORTADORCARTAO_UNIDADEADMINISTRATIVA", "PUA", "PUA.UNIDADEADMINISTRATIVA_ID = UA.ID")
        .innerJoin("CPESC_PORTADORCARTAO", "P", "P.ID = PUA.PORTADORCARTAO_ID")
        .where("P.ID = :id", { id: usuario.id })
        .orderBy("UG.NOMEABREVIADO");
      return dados.getMany();
    }
  }
}
