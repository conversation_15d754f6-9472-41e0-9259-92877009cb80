import { UnidadeGestora } from "cpesc-shared";
import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  OneToOne,
  PrimaryColumn,
  UpdateDateColumn,
} from "typeorm";
import { ApiContaBanco } from "../conta-banco/conta-banco.entity.js";
import type { ApiMunicipio } from "../municipio/municipio.entity.js";
import { ApiUnidadeAdministrativa } from "../unidade-administrativa/unidade-administrativa.entity.js";

@Entity("CPESC_UNIDADEGESTORA")
export class ApiUnidadeGestora implements UnidadeGestora {
  @PrimaryColumn({ name: "ID" })
  id: number;

  @Column({ name: "CODIGO" })
  codigo: number;

  @Column({ name: "CODIGOGESTAO" })
  codigoGestao: number;

  @Column({ name: "NOME" })
  nome: string;

  @Column({ name: "NOMEORDENADOR" })
  nomeOrdenador: string;

  @Column({ name: "CNPJ" })
  cnpj: string;

  @Column({ name: "NOMELOGRADOURO" })
  logradouro: string;

  @Column({ name: "NUMEROLOGRADOURO" })
  numero: string;

  @Column({ name: "COMPLEMENTO" })
  complemento: string;

  @Column({ name: "BAIRRO" })
  bairro: string;

  @Column({ name: "CIDADE" })
  cidade: string;

  @Column({ name: "CEP" })
  cep: string;

  @Column({ name: "DDD" })
  ddd: string;

  @Column({ name: "FONE" })
  telefone: string;

  @Column({ name: "NOMEABREVIADO" })
  nomeAbreviado: string;

  @Column({ name: "DESCRICAO" })
  descricao: string;

  @Column({ name: "BLOQUEIO_ID" })
  bloqueado: number;

  @Column({ name: "RATEIO" })
  calcularPagamento: number;

  @Column({ name: "ATIVO" })
  ativo: string;

  @Column({ name: "SIGLASGPE" })
  siglaSgpe: string;

  @Column({ name: "CRIADOPOR" })
  criadoPor: string;

  @CreateDateColumn({ name: "CRIADOEM" })
  criadoEm: Date;

  @Column({ name: "ATUALIZADOPOR" })
  atualizadoPor: string;

  @UpdateDateColumn({ name: "ATUALIZADOEM" })
  atualizadoEm: Date;

  @ManyToOne("ApiMunicipio")
  @JoinColumn({ name: "MUNICIPIO_ID" })
  municipio: ApiMunicipio;

  @OneToOne("ApiContaBanco", (cb: ApiContaBanco) => cb.unidadeGestora)
  contaBanco: ApiContaBanco;

  @OneToMany("ApiUnidadeAdministrativa", (ua: ApiUnidadeAdministrativa) => ua.unidadeGestora)
  unidadeAdministrativa: ApiUnidadeAdministrativa[];
}
