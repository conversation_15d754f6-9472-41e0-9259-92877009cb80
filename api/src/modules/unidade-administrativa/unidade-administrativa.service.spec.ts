import { jest } from "@jest/globals";
import { Test, type TestingModule } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import { ApiUnidadeAdministrativa } from "./unidade-administrativa.entity.js";
import { UnidadeAdministrativaService } from "./unidade-administrativa.service.js";

describe("UnidadeAdministrativaService", () => {
  let service: UnidadeAdministrativaService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UnidadeAdministrativaService,
        { provide: getRepositoryToken(ApiUnidadeAdministrativa), useFactory: jest.fn },
      ],
    }).compile();

    service = module.get<UnidadeAdministrativaService>(UnidadeAdministrativaService);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });
});
