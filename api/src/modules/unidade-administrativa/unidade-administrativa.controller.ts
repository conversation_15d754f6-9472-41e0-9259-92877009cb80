import { ClassSerializerInterceptor, Controller, Get, Param, UseGuards, UseInterceptors } from "@nestjs/common";
import { endpoints, type RespostaRota } from "cpesc-shared/out/endpoints/main.js";
import { AuthGuard } from "../auth/auth.guard.js";
import { UnidadeAdministrativaService } from "./unidade-administrativa.service.js";

@Controller("unidade-administrativa")
@UseGuards(AuthGuard)
export class UnidadeAdministrativaController {
  constructor(private readonly unidadeAdministrativaService: UnidadeAdministrativaService) {}

  @Get()
  async findAll() {
    return this.unidadeAdministrativaService.getUnidadeAdministrativas();
  }

  @Get("/:id")
  async findUnidadeAdministrativaPorId(@Param("id") id: string) {
    return this.unidadeAdministrativaService.getUnidadeAdministrativaPorId(+id);
  }

  @Get("portador/:id")
  async findUnidadeAdministrativaPorPortador(
    @Param("id") id: string,
  ): RespostaRota<typeof endpoints.buscarUAsPorPortador> {
    return this.unidadeAdministrativaService.getUnidadeAdministrativaPorPortador(+id);
  }

  @UseInterceptors(ClassSerializerInterceptor)
  @Get("portador/:idPortador/ug/:idUg")
  async findUnidadeAdministrativaPorPortadorUg(
    @Param("idPortador") idPortador: number,
    @Param("idUg") idUg: number,
  ): RespostaRota<typeof endpoints.buscarUAsPorPortadorUG> {
    return this.unidadeAdministrativaService.getUnidadeAdministrativaPorPortadorUg(idPortador, idUg);
  }
}
