import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { ApiUnidadeAdministrativa } from "./unidade-administrativa.entity.js";

@Injectable()
export class UnidadeAdministrativaService {
  constructor(
    @InjectRepository(ApiUnidadeAdministrativa)
    private unidadeAdministrativaRepository: Repository<ApiUnidadeAdministrativa>,
  ) {}

  async getUnidadeAdministrativas(): Promise<ApiUnidadeAdministrativa[]> {
    return this.unidadeAdministrativaRepository.find({
      relations: {
        municipio: true,
      },
    });
  }

  async getUnidadeAdministrativaPorId(id: number): Promise<ApiUnidadeAdministrativa | null> {
    return this.unidadeAdministrativaRepository.findOneBy({ id });
  }

  async setUnidadeAdministrativa(unidadeAdministrativa: ApiUnidadeAdministrativa): Promise<void> {
    await this.unidadeAdministrativaRepository.save(unidadeAdministrativa);
  }

  async getUnidadeAdministrativaPorPortador(id: number): Promise<ApiUnidadeAdministrativa[]> {
    const query = this.unidadeAdministrativaRepository
      .createQueryBuilder("ua")
      .innerJoin("CPESC_PORTADORCARTAO_UNIDADEADMINISTRATIVA", "PUA", "PUA.UNIDADEADMINISTRATIVA_ID = ua.ID")
      //nome do campo de relacionamento precisa ser exatamente igual da entidade
      .innerJoinAndSelect("ua.municipio", "municipio")
      .where("PUA.PORTADORCARTAO_ID = :id", { id })
      .orderBy("ua.NOME");
    return query.getMany();
  }

  async getUnidadeAdministrativaPorPortadorUg(idPortador: number, idUg: number): Promise<ApiUnidadeAdministrativa[]> {
    const query = this.unidadeAdministrativaRepository
      .createQueryBuilder("ua")
      .innerJoin("CPESC_PORTADORCARTAO_UNIDADEADMINISTRATIVA", "PUA", "PUA.UNIDADEADMINISTRATIVA_ID = ua.ID")
      //nome do campo de relacionamento precisa ser exatamente igual da entidade
      .innerJoinAndSelect("ua.municipio", "municipio")
      .where("PUA.PORTADORCARTAO_ID = :id", { id: idPortador })
      .andWhere("ua.unidadeGestora = :idUg", { idUg })
      .orderBy("ua.NOME");
    return query.getMany();
  }
}
