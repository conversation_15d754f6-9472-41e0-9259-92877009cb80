import { UnidadeAdministrativa } from "cpesc-shared";
import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import type { ApiContaBanco } from "../conta-banco/conta-banco.entity.js";
import type { ApiMunicipio } from "../municipio/municipio.entity.js";
import type { ApiPortadorUnidadeAdministrativa } from "../portador-unidadeadministrativa/portador-unidadeadministrativa.entity.js";
import type { ApiUnidadeGestora } from "../unidade-gestora/unidade-gestora.entity.js";

@Entity("CPESC_UNIDADEADMINISTRATIVA")
export class ApiUnidadeAdministrativa implements UnidadeAdministrativa {
  @PrimaryGeneratedColumn({ name: "ID" })
  id: number;

  @Column({ name: "CNPJ" })
  cnpj: string;

  @Column({ name: "NOME" })
  nome: string;

  @Column({ name: "CODIGO" })
  codigo: number;

  @Column({ name: "CEP" })
  cep: string;

  @Column({ name: "ENDERECO" })
  endereco: string;

  @Column({ name: "BAIRRO" })
  bairro: string;

  @Column({ name: "CIDADE" })
  cidade: string;

  @Column({ name: "ATIVO" })
  ativo: number;

  @Column({ name: "CRIADOPOR" })
  criadoPor: string;

  @CreateDateColumn({ name: "CRIADOEM" })
  criadoEm: Date;

  @Column({ name: "ATUALIZADOPOR" })
  atualizadoPor: string;

  @UpdateDateColumn({ name: "ATUALIZADOEM" })
  atualizadoEm: Date;

  @ManyToOne("ApiMunicipio")
  @JoinColumn({ name: "MUNICIPIO_ID" })
  municipio: ApiMunicipio;

  @ManyToOne("ApiUnidadeGestora")
  @JoinColumn({ name: "UNIDADEGESTORA_ID" })
  unidadeGestora: ApiUnidadeGestora;

  @ManyToOne("ApiContaBanco")
  @JoinColumn({ name: "CONTABANCO_ID" })
  contaBanco: ApiContaBanco;

  @OneToMany("ApiPortadorUnidadeAdministrativa", (pua: ApiPortadorUnidadeAdministrativa) => pua.unidadeAdministrativa)
  portadoreUnidadeAdministrativa: ApiPortadorUnidadeAdministrativa[];
}
