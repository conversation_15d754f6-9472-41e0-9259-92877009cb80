import { Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { AuthModule } from "../auth/auth.module.js";
import { PortadorModule } from "../portador/portador.module.js";
import { SharedModule } from "../shared/shared.module.js";
import { UsuarioModule } from "../usuario/usuario.module.js";
import { UnidadeAdministrativaController } from "./unidade-administrativa.controller.js";
import { ApiUnidadeAdministrativa } from "./unidade-administrativa.entity.js";
import { UnidadeAdministrativaService } from "./unidade-administrativa.service.js";

@Module({
  imports: [
    TypeOrmModule.forFeature([ApiUnidadeAdministrativa]),
    AuthModule,
    UsuarioModule,
    PortadorModule,
    SharedModule,
  ],
  controllers: [UnidadeAdministrativaController],
  providers: [UnidadeAdministrativaService],
  exports: [UnidadeAdministrativaService],
})
export class UnidadeAdministrativaModule {}
