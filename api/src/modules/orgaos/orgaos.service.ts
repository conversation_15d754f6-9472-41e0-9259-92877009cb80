import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { ApiOrgaos } from "./orgaos.entity.js";

@Injectable()
export class OrgaosService {
  constructor(@InjectRepository(ApiOrgaos) private orgaosRepository: Repository<ApiOrgaos>) {}

  async getOrgaoss(): Promise<ApiOrgaos[]> {
    return this.orgaosRepository.find();
  }
}
