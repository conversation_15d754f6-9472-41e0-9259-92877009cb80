import { jest } from "@jest/globals";
import { Test, type TestingModule } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import { ApiOrgaos } from "./orgaos.entity.js";
import { OrgaosService } from "./orgaos.service.js";

describe("OrgaosService", () => {
  let service: OrgaosService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [OrgaosService, { provide: getRepositoryToken(ApiOrgaos), useFactory: jest.fn }],
    }).compile();

    service = module.get<OrgaosService>(OrgaosService);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });
});
