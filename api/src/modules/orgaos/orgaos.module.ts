import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { SharedModule } from "../shared/shared.module.js";
import { OrgaosController } from "./orgaos.controller.js";
import { ApiOrgaos } from "./orgaos.entity.js";
import { OrgaosService } from "./orgaos.service.js";

@Module({
  imports: [TypeOrmModule.forFeature([ApiOrgaos]), SharedModule],
  controllers: [OrgaosController],
  providers: [OrgaosService],
  exports: [OrgaosService],
})
export class OrgaosModule {}
