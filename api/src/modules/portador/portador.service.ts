import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Perfil, UsuarioAutenticado } from "cpesc-shared";
import { Repository } from "typeorm";
import { ApiCentroCusto } from "../centro-custo/centro-custo.entity.js";
import { ApiPortador } from "./portador.entity.js";

export interface PortadorComCentroCustosAtivos extends ApiPortador {
  centroCustosAtivos: ApiCentroCusto[];
}

@Injectable()
export class PortadorService {
  constructor(@InjectRepository(ApiPortador) private readonly portadorRepository: Repository<ApiPortador>) {}

  async getPortadores(): Promise<ApiPortador[]> {
    const dados = await this.portadorRepository
      .createQueryBuilder("P")
      //nome do campo de relacionamento precisa ser exatamente igual da entidade
      .innerJoin("P.portadorUnidadeAdministrativas", "PUA")
      .innerJoin("PUA.unidadeAdministrativa", "UA")
      //.where("PUA.ATIVO = 'S'")
      .orderBy("P.NOME")
      .getMany();
    return dados;
  }

  async getPortadorPorUnidadeGestora(usuario: UsuarioAutenticado, idUg: number): Promise<ApiPortador[]> {
    if (usuario.perfil == Perfil.Portador) {
      const dados = this.portadorRepository
        .createQueryBuilder("P")
        .innerJoin("P.portadorUnidadeAdministrativas", "PUA")
        .innerJoin("PUA.unidadeAdministrativa", "UA")
        .where("UA.UNIDADEGESTORA_ID = :id", { id: idUg })
        .andWhere("PUA.PORTADORCARTAO_ID = :idPortador", { idPortador: usuario.id })
        .orderBy("P.NOME");
      return dados.getMany();
    } else {
      const dados = this.portadorRepository
        .createQueryBuilder("P")
        .innerJoin("P.portadorUnidadeAdministrativas", "PUA")
        .innerJoin("PUA.unidadeAdministrativa", "UA")
        .where("UA.UNIDADEGESTORA_ID = :id", { id: idUg })
        .orderBy("P.NOME");
      return dados.getMany();
    }
  }

  async getPortadorPorCpf(cpf: string): Promise<ApiPortador | null> {
    return this.portadorRepository.findOne({ where: { cpf } });
  }

  async getPortadorPorId(id: number): Promise<ApiPortador | null> {
    return this.portadorRepository.findOneBy({ id });
  }

  async getPortadorAtivoPorEmail(email: string): Promise<ApiPortador | null> {
    return this.portadorRepository.findOne({
      where: {
        email,
        portadorCentroCustos: {
          ativo: "S",
        },
      },
      relations: {
        portadorCentroCustos: true,
      },
    });
  }

  async getPortadorPorEmail(email: string): Promise<ApiPortador | null> {
    return this.portadorRepository.findOne({
      where: {
        email,
      },
    });
  }

  async getPortadorAtivoPorId(id: number): Promise<ApiPortador | null> {
    const portador = await this.portadorRepository.findOne({
      where: {
        id,
        portadorCentroCustos: {
          ativo: "S",
        },
      },
      relations: {
        portadorCentroCustos: true,
      },
    });

    return portador;
  }

  async setOtpPortador(portador: ApiPortador): Promise<void> {
    await this.portadorRepository.save(portador);
  }

  async setPortador(portador: ApiPortador): Promise<void> {
    await this.portadorRepository.save(portador);
  }
}
