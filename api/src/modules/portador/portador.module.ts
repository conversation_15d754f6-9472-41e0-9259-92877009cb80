import { forwardRef, Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { AuthModule } from "../auth/auth.module.js";
import { SharedModule } from "../shared/shared.module.js";
import { UsuarioModule } from "../usuario/usuario.module.js";
import { PortadorController } from "./portador.controller.js";
import { ApiPortador } from "./portador.entity.js";
import { PortadorService } from "./portador.service.js";
@Module({
  imports: [TypeOrmModule.forFeature([ApiPortador]), SharedModule, UsuarioModule, forwardRef(() => AuthModule)],
  controllers: [PortadorController],
  providers: [PortadorService],
  exports: [PortadorService],
})
export class PortadorModule {}
