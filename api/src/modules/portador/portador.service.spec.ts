import { jest } from "@jest/globals";
import { Test, type TestingModule } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import { ApiPortador } from "./portador.entity.js";
import { PortadorService } from "./portador.service.js";

describe("PortadorService", () => {
  let service: PortadorService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [PortadorService, { provide: getRepositoryToken(ApiPortador), useFactory: jest.fn }],
    }).compile();

    service = module.get<PortadorService>(PortadorService);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });
});
