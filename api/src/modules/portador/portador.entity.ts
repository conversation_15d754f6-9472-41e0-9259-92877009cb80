import { Exclude } from "class-transformer";
import { Portador } from "cpesc-shared";
import {
  AfterLoad,
  Column,
  CreateDateColumn,
  Entity,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import { aplicarMascaraCPF } from "../../helpers/functions.js";
import type { ApiPortadorCentroCusto } from "../portador-centro-custo/portador-centro-custo.entity.js";
import type { ApiPortadorUnidadeAdministrativa } from "../portador-unidadeadministrativa/portador-unidadeadministrativa.entity.js";

@Entity("CPESC_PORTADORCARTAO")
export class ApiPortador implements Portador {
  @PrimaryGeneratedColumn({ name: "ID" })
  id: number;

  @Exclude({ toPlainOnly: true })
  @Column({ name: "CPF" })
  cpf: string;

  cpfOfuscado: string;

  @Column({ name: "NOME" })
  nome: string;

  @Column({ name: "DATANASCIMENTO" })
  dataNascimento: Date;

  @Column({ name: "NOMEABREVIADO" })
  nomeAbreviado: string;

  @Column({ name: "EMAIL" })
  email: string;

  @Column({ name: "CRIADOPOR" })
  criadoPor: string;

  @CreateDateColumn({ name: "CRIADOEM" })
  criadoEm: Date;

  @Column({ name: "ATUALIZADOPOR" })
  atualizadoPor: string;

  @UpdateDateColumn({ name: "ATUALIZADOEM" })
  atualizadoEm: Date;

  @Column({ name: "CODIGOOTP" })
  codigoOtp: string;

  @Column({ name: "EXPIRACAOOTP" })
  otpExpiracao: Date;

  @Column({ name: "ULTIMOACESSO" })
  dtUltimoAcesso: Date;

  @OneToMany("ApiPortadorCentroCusto", (pcc: ApiPortadorCentroCusto) => pcc.portador)
  portadorCentroCustos: ApiPortadorCentroCusto[];

  @OneToMany("ApiPortadorUnidadeAdministrativa", (pua: ApiPortadorUnidadeAdministrativa) => pua.portador)
  portadorUnidadeAdministrativas: ApiPortadorUnidadeAdministrativa[];

  @AfterLoad()
  generateFields(): void {
    this.cpfOfuscado = this.cpf ? aplicarMascaraCPF(this.cpf, true) : "";
  }
}
