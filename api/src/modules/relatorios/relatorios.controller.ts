import { ClassSerializerInterceptor, Controller, Get, Query, Req, UseGuards, UseInterceptors } from "@nestjs/common";
import { AuthGuard, type RequisicaoAutenticada } from "../auth/auth.guard.js";
import { ParametrosBuscaGastos, RelatoriosService } from "./relatorios.service.js";

@Controller("relatorios")
@UseGuards(AuthGuard)
export class RelatoriosController {
  constructor(private readonly relatoriosService: RelatoriosService) {}

  @UseInterceptors(ClassSerializerInterceptor)
  @Get("gastos")
  async findGastosProduto(@Req() req: RequisicaoAutenticada, @Query() params: ParametrosBuscaGastos) {
    return this.relatoriosService.getGastosProdutos(params, req);
  }
}
