import { BadRequestException, Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Perfil } from "cpesc-shared";
import { Between, FindOptionsWhere, ILike, In, Repository } from "typeorm";
import type { RequisicaoAutenticada } from "../auth/auth.guard.js";
import { UnidadeGestoraService } from "../unidade-gestora/unidade-gestora.service.js";
import { ApiGastosPorNCM } from "./gastos-por-ncm.entity.js";

export interface ParametrosBuscaGastos {
  ncm?: string;
  tipo?: string;
  descricao?: string;
  dataInicial?: Date;
  dataFinal?: Date;
}

@Injectable()
export class RelatoriosService {
  constructor(
    @InjectRepository(ApiGastosPorNCM) private gastosPorNCMRepository: Repository<ApiGastosPorNCM>,
    private unidadeGestoraService: UnidadeGestoraService,
  ) {}

  async getGastosProdutos(params: ParametrosBuscaGastos, req: RequisicaoAutenticada): Promise<ApiGastosPorNCM[]> {
    if (req.usuario?.perfil === Perfil.Portador) {
      throw new BadRequestException({ sucesso: false, mensagem: "Usuário sem acesso a esta consulta." });
    }
    // Tratamento de parâmetros de busca
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    const ugs = await this.unidadeGestoraService.findUnidadeGestoraAtivaPorUsuario(req.usuario!);
    const { ncm, tipo, descricao, dataInicial, dataFinal } = params;
    const where: FindOptionsWhere<ApiGastosPorNCM> = {
      ...(ncm && { ncm: ILike(`%${ncm}%`) }),
      ...(tipo && { tipo: ILike(`%${tipo}%`) }),
      ...(descricao && { descricao: ILike(`%${descricao}%`) }),
      ...(dataInicial && dataFinal && { dataTransacao: Between(new Date(dataInicial), new Date(dataFinal)) }),
      ...(ugs.length > 0 && { unidadegestoraId: In(ugs.map(ug => ug.id)) }),
    };

    const dados = await this.gastosPorNCMRepository.find({ where, order: { nmunidadegestora: "ASC", ncm: "ASC" } });

    // Agrupamento
    const agrupados = new Map<string, ApiGastosPorNCM>();

    for (const item of dados) {
      const chave = `${item.id}|${item.tipo}|${item.unidadegestoraId}|${item.nmunidadegestora}|${item.ncm}|${item.descricao}`;
      const grupo = agrupados.get(chave);

      if (grupo === undefined) {
        agrupados.set(chave, { ...item });
      } else {
        grupo.quantidade += item.quantidade;
        grupo.valor += item.valor;
      }
    }

    return Array.from(agrupados.values());
  }
}
