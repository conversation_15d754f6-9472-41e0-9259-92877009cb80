import { GastosPorNCM } from "cpesc-shared";
import { Column, Entity, PrimaryGeneratedColumn } from "typeorm";

@Entity("CPESC_V_GASTOSPORNCM")
export class ApiGastosPorNCM implements GastosPorNCM {
  @PrimaryGeneratedColumn({ name: "ID" })
  id: string;

  @Column({ name: "NCM", nullable: true, type: String })
  ncm: string;

  @Column({ name: "DESCRICAO", nullable: true, type: String })
  descricao: string;

  @Column({ name: "QUANTIDADE" })
  quantidade: number;

  @Column({ name: "VALOR", type: "decimal" })
  valor: number;

  @Column({ name: "TIPO" })
  tipo: string;

  @Column({ name: "DATATRANSACAO" })
  dataTransacao: Date;

  @Column({ name: "UNIDADEGESTORA_ID" })
  unidadegestoraId: number;

  @Column({ name: "CDUNIDADEGESTORA" })
  cdunidadegestora: number;

  @Column({ name: "NMUNIDADEGESTORA" })
  nmunidadegestora: string;
}
