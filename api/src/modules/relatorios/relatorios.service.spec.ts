import { jest } from "@jest/globals";
import { Test, type TestingModule } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import { ApiUnidadeGestora } from "../unidade-gestora/unidade-gestora.entity.js";
import { UnidadeGestoraService } from "../unidade-gestora/unidade-gestora.service.js";
import { ApiGastosPorNCM } from "./gastos-por-ncm.entity.js";
import { RelatoriosService } from "./relatorios.service.js";

describe("RelatoriosService", () => {
  let service: RelatoriosService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RelatoriosService,
        UnidadeGestoraService,
        { provide: getRepositoryToken(ApiGastosPorNCM), useFactory: jest.fn },
        { provide: getRepositoryToken(ApiUnidadeGestora), useFactory: jest.fn },
      ],
    }).compile();

    service = module.get<RelatoriosService>(RelatoriosService);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });
});
