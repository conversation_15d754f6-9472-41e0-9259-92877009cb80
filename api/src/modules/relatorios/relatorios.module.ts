import { <PERSON>du<PERSON> } from "@nestjs/common";
import { RelatoriosService } from "./relatorios.service.js";
import { RelatoriosController } from "./relatorios.controller.js";
import { ApiGastosPorNCM } from "./gastos-por-ncm.entity.js";
import { TypeOrmModule } from "@nestjs/typeorm";
import { AuthModule } from "../auth/auth.module.js";
import { SharedModule } from "../shared/shared.module.js";
import { UsuarioModule } from "../usuario/usuario.module.js";
import { PortadorModule } from "../portador/portador.module.js";
import { UnidadeGestoraModule } from "../unidade-gestora/unidade-gestora.module.js";

@Module({
  providers: [RelatoriosService],
  controllers: [RelatoriosController],
  imports: [
    TypeOrmModule.forFeature([ApiGastosPorNCM]),
    AuthModule,
    SharedModule,
    UsuarioModule,
    PortadorModule,
    UnidadeGestoraModule,
  ],
})
export class RelatoriosModule {}
