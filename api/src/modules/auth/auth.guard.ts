import {
  Injectable,
  InternalServerErrorException,
  NotAcceptableException,
  NotFoundException,
  UnauthorizedException,
  type CanActivate,
  type ExecutionContext,
} from "@nestjs/common";
import { JwtService, TokenExpiredError } from "@nestjs/jwt";
import { Perfil, UsuarioAutenticado } from "cpesc-shared";
import type { Request } from "express";
import { TokenService } from "../../helpers/token.service.js";
import { PortadorService } from "../portador/portador.service.js";
import { EnvService } from "../shared/env.service.js";
import { UsuarioService } from "../usuario/usuario.service.js";
import { getClientIp, jwtPayloadSchema } from "./auth.service.js";
import { AUTH_NOME_COOKIE } from "./config.js";

export interface RequisicaoAutenticada extends Request {
  usuario: UsuarioAutenticado | null;
}

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(
    private envService: EnvService,
    private readonly jwtService: JwtService,
    private usuarioService: UsuarioService,
    private portadorService: PortadorService,
    private tokenService: TokenService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();
    const credenciais = await this.getCredenciais(request);

    if (credenciais === null) {
      throw new UnauthorizedException();
    }

    // Permite que as rotas acessem o usuário logado
    const req = request as RequisicaoAutenticada;
    req.usuario = credenciais;

    return true;
  }

  async getCredenciais(request: Request): Promise<UsuarioAutenticado | null> {
    const token = this.getToken(request);

    if (!token || this.tokenService.isTokenInvalid(token)) {
      return null;
    }

    try {
      const rawPayload = await this.jwtService.verifyAsync<object>(token, {
        secret: this.envService.get("JWT_SECRET"),
      });
      const payload = jwtPayloadSchema.safeParse(rawPayload).data;

      if (payload === undefined || !("exp" in rawPayload) || typeof rawPayload.exp !== "number") {
        throw new NotAcceptableException("Token inválido");
      }

      const expirationDate = new Date(rawPayload.exp * 1000); // Convert seconds to milliseconds

      if (expirationDate < new Date()) {
        throw new UnauthorizedException("Token expirado");
      }

      const usuario =
        payload.perfil === Perfil.Portador
          ? await this.portadorService.getPortadorPorId(payload.sub)
          : await this.usuarioService.getUsuarioPorId(payload.sub);

      if (usuario === null) {
        throw new NotFoundException("Usuário não encontrado");
      }

      return {
        id: usuario.id,
        perfil: payload.perfil,
        nome: usuario.nome,
        email: usuario.email,
        ipAddress: getClientIp(request),
        tipoLogin: payload.tipoLogin,
        rotaInicial: payload.perfil === Perfil.Portador ? this.envService.get("ROTA_INICIAL_PORTADOR") : "/",
      };
    } catch (error) {
      if (error instanceof TokenExpiredError) {
        throw new UnauthorizedException("Token expirado");
      }

      console.log(error);
      throw new InternalServerErrorException("Erro ao verificar token de autenticação");
    }
  }

  private getToken(request: Request): string | undefined {
    const authHeader = request.headers.authorization;
    if (authHeader?.startsWith("Bearer ")) {
      return authHeader.split(" ")[1];
    }
    return request.cookies[AUTH_NOME_COOKIE] as string | undefined;
  }
}
