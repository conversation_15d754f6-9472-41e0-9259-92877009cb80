import { BadRequestException, Body, Controller, Get, HttpCode, HttpStatus, Post, Req, Res } from "@nestjs/common";
import type { PayloadLogin, PayloadLoginOtp, PayloadLoginSau } from "cpesc-shared";
import { endpoints, type Resposta<PERSON><PERSON> } from "cpesc-shared/out/endpoints/main.js";
import type { Request, Response } from "express";
import { ValidadorHttp } from "../../helpers/ValidadorHttp.js";
import { AuthGuard } from "./auth.guard.js";
import { AuthService, getClientIp } from "./auth.service.js";
import { AUTH_NOME_COOKIE, getOpcoesCookieAutenticacao } from "./config.js";

@Controller("auth")
export class AuthController {
  constructor(
    private authGuard: AuthGuard,
    private authService: AuthService,
  ) {}

  @HttpCode(HttpStatus.OK)
  @Post("login")
  async login(
    @Req() request: Request,
    @Res({ passthrough: true }) response: Response,
    @Body() payload: PayloadLogin,
  ): RespostaRota<typeof endpoints.login> {
    switch (payload.tipo) {
      case "sau":
        return this.loginSau(request, response, payload);
      case "otp":
        return this.loginOtp(request, response, payload);
      default:
        throw new BadRequestException("Tipo de login inválido.");
    }
  }

  private async loginSau(
    request: Request,
    response: Response,
    payload: PayloadLoginSau,
  ): RespostaRota<typeof endpoints.login> {
    // TODO: Implementar validação via Zod
    const validador = new ValidadorHttp(payload as Record<keyof PayloadLoginSau, unknown>);
    const rota = validador.lerString("rota");
    const ticket = validador.lerString("ticket");
    const { token, credenciais } = await this.authService.loginSau(request, rota, ticket);
    const opcoes = getOpcoesCookieAutenticacao();
    response.cookie(AUTH_NOME_COOKIE, token, opcoes);

    return { credenciais };
  }

  private async loginOtp(
    request: Request,
    response: Response,
    payload: PayloadLoginOtp,
  ): RespostaRota<typeof endpoints.login> {
    // TODO: Implementar validação via Zod
    const validador = new ValidadorHttp(payload as Record<keyof PayloadLoginOtp, unknown>);
    const email = validador.lerString("email");
    const codigo = validador.lerString("codigo");
    const { token, credenciais } = await this.authService.loginOtp(request, email, codigo);
    const opcoes = getOpcoesCookieAutenticacao();
    response.cookie(AUTH_NOME_COOKIE, token, opcoes);

    return { credenciais };
  }

  @HttpCode(HttpStatus.OK)
  @Post("otp")
  async gerarCodigoOtp(
    @Req() request: Request,
    @Body() payload: { email: string },
  ): RespostaRota<typeof endpoints.otp> {
    const validador = new ValidadorHttp(payload);
    const email = validador.lerString("email");
    const ipAddress = getClientIp(request);
    await this.authService.gerarCodigoOtp(email, ipAddress);

    return { sucesso: true };
  }

  @HttpCode(HttpStatus.OK)
  @Post("recupera-email")
  async recuperaEmail(
    @Req() request: Request,
    @Body() payload: { cpf: string; cartao: string },
  ): RespostaRota<typeof endpoints.recuperarEmail> {
    const validador = new ValidadorHttp(payload);
    const cpf = validador.lerString("cpf");
    const cartao = validador.lerString("cartao");
    const ipAddress = getClientIp(request);
    const email = await this.authService.recuperaEmail(cpf, cartao, ipAddress);

    return { email };
  }

  @HttpCode(HttpStatus.OK)
  @Post("logout")
  async logout(@Res({ passthrough: true }) response: Response): RespostaRota<typeof endpoints.logout> {
    void this;
    response.clearCookie(AUTH_NOME_COOKIE, {
      httpOnly: true,
      sameSite: "lax",
      secure: true,
      path: "/",
    });

    return Promise.resolve({ message: "Logout realizado com sucesso!" });
  }

  @HttpCode(HttpStatus.OK)
  @Get("credenciais")
  async credenciais(@Req() request: Request): RespostaRota<typeof endpoints.lerCredenciais> {
    const credenciais = await this.authGuard.getCredenciais(request);
    return { credenciais };
  }
}
