import { BadRequestException, Injectable, NotFoundException, ServiceUnavailableException } from "@nestjs/common";
import { JwtService } from "@nestjs/jwt";
import bcrypt from "bcrypt";
import { Perfil, TipoLogin, UsuarioAutenticado } from "cpesc-shared";
import { tipoLoginSchema } from "cpesc-shared/out/endpoints/auth.js";
import { Request } from "express";
import { generate } from "otp-generator";
import z, { ZodType } from "zod";
import {
  apenasNumeros,
  aplicarMascaraCartao,
  aplicarMascaraCPF,
  validarCPF,
  validarEmail,
} from "../../helpers/functions.js";
import { CartaoService } from "../cartao/cartao.service.js";
import { EmailService } from "../email/email.service.js";
import { ApiPortador } from "../portador/portador.entity.js";
import { PortadorService } from "../portador/portador.service.js";
import { EnvService } from "../shared/env.service.js";
import type { Usuario } from "../usuario/usuario.entity.js";
import { UsuarioService } from "../usuario/usuario.service.js";
import { AUTH_DURACAO } from "./config.js";

/**
 * Payload do JWT armazenado no cliente (num cookie seguro).
 */
export interface JwtPayload {
  /**
   * Contém o ID do usuário autenticado. Utilizado o nome "sub" pois é uma "registered claim" do JWT:
   * https://auth0.com/docs/secure/tokens/json-web-tokens/json-web-token-claims#registered-claims
   * lista completa em https://www.iana.org/assignments/jwt/jwt.xhtml#claims
   */
  sub: number;
  perfil: Perfil;
  tipoLogin: TipoLogin;
}

export const jwtPayloadSchema: ZodType<JwtPayload> = z.object({
  sub: z.number(),
  perfil: z.enum(Perfil),
  tipoLogin: tipoLoginSchema,
});

export interface ResultadoLogin {
  token: string;
  credenciais: UsuarioAutenticado;
}

@Injectable()
export class AuthService {
  constructor(
    private envService: EnvService,
    private jwtService: JwtService,
    private usuarioService: UsuarioService,
    private portadorService: PortadorService,
    private emailService: EmailService,
    private cartaoService: CartaoService,
  ) {}

  async loginSau(request: Request, rota: string, ticket: string): Promise<ResultadoLogin> {
    const emailAutenticado = await this.validarTicketSau(rota, ticket);

    if (!emailAutenticado || !validarEmail(emailAutenticado)) {
      throw new BadRequestException(
        `O e-mail [${emailAutenticado}] não é permitido. \nPor favor, utilize um e-mail válido.`,
      );
    }

    const usuario = await this.usuarioService.getUsuarioPorEmail(emailAutenticado);

    if (usuario === null) {
      throw new BadRequestException(
        `Este e-email [${emailAutenticado}] não foi aceito. \nCaso você considere isso um erro, contate seu gestor para regularizar a situação`,
      );
    }

    const agora = new Date();
    usuario.dtUltimoAcesso = agora;
    usuario.otpExpiracao = agora;
    usuario.codigoOtp = "";
    await this.usuarioService.setUsuario(usuario);
    const token = await this.gerarTokenJwt(usuario, "sau");
    const credenciais: UsuarioAutenticado = {
      id: usuario.id,
      nome: usuario.nome,
      email: usuario.email,
      perfil: usuario.perfil,
      ipAddress: getClientIp(request),
      tipoLogin: "sau",
      rotaInicial: usuario.perfil === Perfil.Portador ? this.envService.get("ROTA_INICIAL_PORTADOR") : "/",
    };

    return { token, credenciais };
  }

  async loginOtp(request: Request, email: string, codigoOtp: string): Promise<ResultadoLogin> {
    if (!validarEmail(email)) {
      throw new BadRequestException(`O e-mail [${email}] não é permitido. \nPor favor, utilize um e-mail válido.`);
    }

    const portador = await this.portadorService.getPortadorPorEmail(email);

    if (portador !== null) {
      await validarOTP(portador.codigoOtp, codigoOtp, portador.otpExpiracao);
      const agora = new Date();
      portador.dtUltimoAcesso = agora;
      portador.otpExpiracao = agora;
      portador.codigoOtp = "";
      await this.portadorService.setPortador(portador);
      const token = await this.gerarTokenJwt(portador, "otp");
      const credenciais: UsuarioAutenticado = {
        id: portador.id,
        nome: portador.nome,
        email: portador.email,
        perfil: Perfil.Portador,
        ipAddress: getClientIp(request),
        tipoLogin: "otp",
        rotaInicial: this.envService.get("ROTA_INICIAL_PORTADOR"),
      };

      return { token, credenciais };
    }

    const usuario = await this.usuarioService.getUsuarioPorEmail(email);

    if (usuario !== null) {
      await validarOTP(usuario.codigoOtp, codigoOtp, usuario.otpExpiracao);
      const agora = new Date();
      usuario.dtUltimoAcesso = agora;
      usuario.otpExpiracao = agora;
      usuario.codigoOtp = "";
      await this.usuarioService.setUsuario(usuario);
      const token = await this.gerarTokenJwt(usuario, "otp");
      const credenciais: UsuarioAutenticado = {
        id: usuario.id,
        nome: usuario.nome,
        email: usuario.email,
        perfil: usuario.perfil,
        ipAddress: getClientIp(request),
        tipoLogin: "otp",
        rotaInicial: usuario.perfil === Perfil.Portador ? this.envService.get("ROTA_INICIAL_PORTADOR") : "/",
      };

      return { token, credenciais };
    }

    throw new BadRequestException(
      `Este e-email [${email}] não foi aceito. \nCaso você considere isso um erro, contate seu gestor para regularizar a situação`,
    );
  }

  private async validarTicketSau(rota: string, ticket: string): Promise<string> {
    const sauLoginUrl = this.envService.get("SAU_LOGIN_URL");
    const parametros = new URLSearchParams({ service: rota, ticket });

    const xml = await fetch(`${sauLoginUrl}?${parametros}`)
      .then(async r => r.text())
      .catch(e => {
        console.error(e);
        throw new ServiceUnavailableException("SAU Indisponível");
      });

    const erro = /<cas:authenticationFailure code=["']([^'"]+)["']>/.exec(xml)?.[1];

    if (erro !== undefined) {
      if (erro === "INVALID_TICKET") {
        throw new BadRequestException("Falha ao autenticar: ticket inválido");
      }

      throw new BadRequestException(`Falha ao autenticar: ${erro}`);
    }

    const email = /<cas:email>([^<]+)<\/cas:email>/.exec(xml)?.[1];

    if (email === undefined || email.length === 0) {
      throw new ServiceUnavailableException();
    }

    return email;
  }

  async gerarCodigoOtp(email: string, ipAddress: string | null = null): Promise<void> {
    if (!validarEmail(email)) {
      throw new BadRequestException(`O e-mail [${email}] não é permitido. \nPor favor, utilize um e-mail válido.`);
    }

    const portador = await this.portadorService.getPortadorPorEmail(email);

    if (portador !== null) {
      await this.salvarOtp(portador, async p => this.portadorService.setOtpPortador(p), ipAddress);
      return;
    }

    const usuario = await this.usuarioService.getUsuarioPorEmail(email);

    if (usuario !== null) {
      await this.salvarOtp(usuario, async u => this.usuarioService.setUsuario(u), ipAddress);
      return;
    }

    throw new BadRequestException(
      `Este e-email [${email}] não foi aceito. \nCaso você considere isso um erro, contate seu gestor para regularizar a situação`,
    );
  }

  private async salvarOtp<T extends Usuario | ApiPortador>(
    usuario: T,
    callbackSalvamento: (usuario: T) => Promise<void>,
    ipAddress: string | null,
  ): Promise<void> {
    if (usuario.otpExpiracao > new Date()) {
      return;
    }

    const { otp, otpExpiracao } = this.geraOtp();
    const salt = await bcrypt.genSalt();
    const otpCriptografado = await bcrypt.hash(otp, salt);
    usuario.codigoOtp = otpCriptografado;
    usuario.otpExpiracao = otpExpiracao;
    await callbackSalvamento(usuario);
    await this.enviarEmail(usuario.email, otp, otpExpiracao, ipAddress);
  }

  private geraOtp(): { otp: string; otpExpiracao: Date } {
    const otp = generate(6, {
      digits: true,
      lowerCaseAlphabets: false,
      upperCaseAlphabets: false,
      specialChars: false,
    });

    const segundosExpira = this.envService.get("EXPIRA_OTP");
    const otpExpiracao = new Date(Date.now() + segundosExpira * 1000); // Expira em 5 minutos

    return { otp, otpExpiracao };
  }

  private async enviarEmail(
    email: string,
    otp: string,
    otpExpiration: Date,
    ipAddress: string | null = null,
  ): Promise<void> {
    const expirate = new Intl.DateTimeFormat("pt-BR", {
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    }).format(otpExpiration);

    //const expirate = new Date(otpExpiration).toLocaleDateString("pt-br");
    await this.emailService.sendOtpEmail(email, otp, expirate, ipAddress);
  }

  private async gerarTokenJwt(usuario: Usuario | ApiPortador, tipoLogin: TipoLogin): Promise<string> {
    const expiracaoToken = new Date(Date.now() + AUTH_DURACAO);
    const expiresIn = Math.floor((expiracaoToken.getTime() - Date.now()) / 1000);

    const payload: JwtPayload = {
      sub: usuario.id,
      perfil: "perfil" in usuario ? usuario.perfil : Perfil.Portador,
      tipoLogin,
    };

    return this.jwtService.signAsync(payload, { expiresIn });
  }

  async recuperaEmail(cpf: string, cartao: string, ipAddress: string | null): Promise<string> {
    const cpfLimpo = apenasNumeros(cpf).toString().padStart(11, "0");

    if (!validarCPF(cpfLimpo)) {
      throw new BadRequestException("CPF inválido. Corrija e tente novamente.");
    }

    const cartaoLimpo = apenasNumeros(cartao).toString();

    if (cartaoLimpo.length < 16) {
      throw new BadRequestException("Cartão inválido. Corrija e tente novamente.");
    }

    const portador = await this.portadorService.getPortadorPorCpf(cpfLimpo);

    if (portador !== null) {
      const cartao = await this.cartaoService.getCartaoAtivoPorNumero(cartaoLimpo);

      if (cartao?.portadorUnidadeAdministrativa.portador.id === portador.id) {
        const data = new Intl.DateTimeFormat("pt-BR", {
          hour: "2-digit",
          minute: "2-digit",
          second: "2-digit",
          day: "2-digit",
          month: "2-digit",
          year: "numeric",
        }).format(new Date());
        const message = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2>Notificação do CPESC</h2>
          <p>Foi realizada uma busca pelo e-mail do CPF <span style="font-size: 24px; font-weight: bold;">${aplicarMascaraCPF(cpf, true)}</span>
           para o cartão <span style="font-size: 24px; font-weight: bold;">${aplicarMascaraCartao(cartao.numero)}</p>
          <p>Na data de ${data}.</p>
          ${ipAddress ? `<p>Acesso solicitado do IP: ${ipAddress}</p>` : ""}
          <hr>
          <p style="font-size: 12px; color: #666;">Este é um email automático, não responda.</p>
        </div>`;
        await this.emailService.sendNotificacao(portador.email, "CPESC", message);
        return portador.email;
      }
    }

    throw new NotFoundException("Não foi encontrado portador com os dados informados.");
  }
}

export function getClientIp(request: Request): string | null {
  // Get IP from various headers or from the socket
  const ip = request.headers["x-forwarded-for"] ?? request.headers["x-real-ip"] ?? request.socket.remoteAddress ?? null;

  // If the IP is an array (x-forwarded-for can be comma-separated list), get the first one
  if (Array.isArray(ip)) {
    const firstIp = ip[0] || null;
    return normalizeIp(firstIp);
  }

  // If the IP is a string, normalize it
  if (typeof ip === "string") {
    return normalizeIp(ip);
  }

  return null;
}

function normalizeIp(ip: string | null): string | null {
  if (!ip) return null;

  // Convert IPv6 localhost to IPv4 localhost
  if (ip === "::1") return "127.0.0.1";

  // Remove IPv6 prefix if present (::ffff:*********** -> ***********)
  if (ip.startsWith("::ffff:")) {
    return ip.substring(7);
  }

  // If it's a valid IPv4 address, return it
  const ipv4Regex = /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/;
  if (ipv4Regex.test(ip)) {
    return ip;
  }

  // For other IPv6 addresses, we can't easily convert to IPv4
  // So we'll just return the original IP
  return ip;
}

async function validarOTP(otpBase: string, otpDigitado: string, otpExpiracao: Date): Promise<void> {
  if (otpExpiracao < new Date()) {
    throw new BadRequestException("Código OTP expirado. \nPor favor, solicite um novo código.");
  }

  if (!(await bcrypt.compare(otpDigitado, otpBase))) {
    throw new BadRequestException(
      "Código OTP inválido. \nVerifique se você digitou corretamente o código recebido em seu email e tente novamente.",
    );
  }
}
