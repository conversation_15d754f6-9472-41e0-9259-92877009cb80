import { jest } from "@jest/globals";
import { ConfigService } from "@nestjs/config";
import { JwtService } from "@nestjs/jwt";
import { Test, type TestingModule } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import { ApiCartao } from "../cartao/cartao.entity.js";
import { CartaoService } from "../cartao/cartao.service.js";
import { EmailService } from "../email/email.service.js";
import { ApiPortador } from "../portador/portador.entity.js";
import { PortadorService } from "../portador/portador.service.js";
import { EnvService } from "../shared/env.service.js";
import { Usuario } from "../usuario/usuario.entity.js";
import { UsuarioService } from "../usuario/usuario.service.js";
import { AuthService } from "./auth.service.js";

describe("AuthService", () => {
  let service: AuthService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuthService,
        CartaoService,
        ConfigService,
        EmailService,
        EnvService,
        JwtService,
        PortadorService,
        UsuarioService,
        { provide: getRepositoryToken(ApiCartao), useFactory: jest.fn },
        { provide: getRepositoryToken(ApiPortador), useFactory: jest.fn },
        { provide: getRepositoryToken(Usuario), useFactory: jest.fn },
      ],
    }).compile();

    service = module.get<AuthService>(AuthService);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });
});
