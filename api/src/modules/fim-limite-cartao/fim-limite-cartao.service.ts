import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { SituacaoFimLimiteCartao } from "cpesc-shared";
import { Repository } from "typeorm";
import { HistoricoFimLimiteCartaoService } from "../historico-fim-limite-cartao/historico-fim-limite-cartao.service.js";
import { LimiteCartaoService } from "../limite-cartao/limite-cartao.service.js";
import { MovimentacaoService } from "../movimentacao/movimentacao.service.js";
import { ApiFimLimiteCartao } from "./fim-limite-cartao.entity.js";

@Injectable()
export class FimLimiteCartaoService {
  constructor(
    @InjectRepository(ApiFimLimiteCartao) private fimLimiteCartaoRepository: Repository<ApiFimLimiteCartao>,
    private movimentacaoService: MovimentacaoService,
    private limiteCartaoService: LimiteCartaoService,
    private historicoFimLimiteCartaoService: HistoricoFimLimiteCartaoService,
  ) {}

  async getFimLimiteCartaoAtiva(id: number): Promise<ApiFimLimiteCartao[]> {
    return this.fimLimiteCartaoRepository.find({
      where: {
        limite: { id },
        situacao: SituacaoFimLimiteCartao.Finalizado,
      },
      relations: { limite: true },
    });
  }

  async getFimLimiteCartao(): Promise<ApiFimLimiteCartao[]> {
    return this.fimLimiteCartaoRepository.find({});
  }

  async getFimLimiteCartaoPorId(id: number): Promise<ApiFimLimiteCartao | null> {
    return this.fimLimiteCartaoRepository.findOne({ where: { id } });
  }

  async setFimLimiteCartao(fimLimite: ApiFimLimiteCartao): Promise<void> {
    await this.fimLimiteCartaoRepository.save(fimLimite);
  }

  async criarFimLimiteCartao(
    limiteCartaoId: number,
    valorGasto: number,
    orgaoSgpe: string,
    processoSgpe: string,
    anoSgpe: string,
    email: string,
    ip: string,
    id: number,
  ): Promise<ApiFimLimiteCartao> {
    // Monta o objeto com os dados recebidos como parâmetros

    const dados: Partial<ApiFimLimiteCartao> = {
      limiteCartaoId,
      valorGasto,
      orgaoSgpe,
      processoSgpe,
      anoSgpe,
      atualizadoPor: email,
      criadoPor: email,
      situacao: SituacaoFimLimiteCartao.Finalizado,
      criadoPorIp: ip,
      atualizadoPorIp: ip,
      id,
    };
    const pc = this.fimLimiteCartaoRepository.create(dados);
    return this.fimLimiteCartaoRepository.save(pc);
  }

  async liberarParaEdicaoFimLimiteCartao(
    fimlimiteId: number,
    motivo: string,
    email: string,
    ip: string,
  ): Promise<boolean> {
    const fimlimite: ApiFimLimiteCartao | null = await this.fimLimiteCartaoRepository.findOneBy({ id: fimlimiteId });
    if (fimlimite != null) {
      const limiteCartaoId = fimlimite.limiteCartaoId;
      const removerMovimentacoesVinculados =
        await this.movimentacaoService.removerMovimentacoesVinculados(limiteCartaoId);
      if (!removerMovimentacoesVinculados) {
        return false;
      }
      fimlimite.situacao = SituacaoFimLimiteCartao.Edicao;
      fimlimite.atualizadoPor = email;
      fimlimite.atualizadoPorIp = ip;
      await this.fimLimiteCartaoRepository.save(fimlimite);
      await this.historicoFimLimiteCartaoService.criaHistoricoFimLimiteCartao(fimlimiteId, motivo, email, ip);
      await this.limiteCartaoService.finalizarLimiteCartao(limiteCartaoId);
      return true;
    }
    return false;
  }
}
