import { Body, Controller, Get, HttpException, HttpStatus, Param, Patch, Post, Req, UseGuards } from "@nestjs/common";
import type { endpoints, RespostaRota } from "cpesc-shared/out/endpoints/main.js";
import { AuthGuard, type RequisicaoAutenticada } from "../auth/auth.guard.js";
import { FimLimiteCartaoService } from "./fim-limite-cartao.service.js";

@Controller("fim-limite-cartao")
@UseGuards(AuthGuard)
export class FimLimiteCartaoController {
  constructor(private readonly fimLimiteCartaoService: FimLimiteCartaoService) {}

  @Post("/")
  async createFimLimiteCartao(
    @Body()
    body: {
      limiteCartaoId: number;
      valorGasto: number;
      orgaoSgpe: string;
      processoSgpe: string;
      anoSgpe: string;
      fimLimiteCartaoId: number;
    },
    @Req() req: RequisicaoAutenticada,
  ): RespostaRota<typeof endpoints.criarFimLimiteCartao> {
    const ip = req.usuario?.ipAddress;
    const email = req.usuario?.email;

    return this.fimLimiteCartaoService.criarFimLimiteCartao(
      body.limiteCartaoId,
      body.valorGasto,
      body.orgaoSgpe,
      body.processoSgpe,
      body.anoSgpe,
      email ?? "erro no sistema de login",
      ip ?? "erro no sistema de ip de login",
      body.fimLimiteCartaoId,
    );
  }

  @Get()
  async findAll() {
    return this.fimLimiteCartaoService.getFimLimiteCartao();
  }

  @Get("/:id")
  async findFimLimiteCartaoPorId(@Param("id") id: string) {
    return this.fimLimiteCartaoService.getFimLimiteCartaoPorId(+id);
  }

  @Get("/ativa/:id")
  async findFimLimiteCartaoFimLimiteCartaoAtiva(@Param("id") id: string) {
    return this.fimLimiteCartaoService.getFimLimiteCartaoAtiva(+id);
  }

  @Patch("/liberar-edicao")
  async liberarEdicaoFimLimiteCartao(
    @Body()
    body: {
      fimLimiteCartaoId: number;
      motivo: string;
    },
    @Req() req: RequisicaoAutenticada,
  ): RespostaRota<typeof endpoints.liberarEdicaoFimLimiteCartao> {
    const ip = req.usuario?.ipAddress;
    const email = req.usuario?.email;

    const sucesso = await this.fimLimiteCartaoService.liberarParaEdicaoFimLimiteCartao(
      body.fimLimiteCartaoId,
      body.motivo,
      email ?? "erro no sistema de login",
      ip ?? "erro no sistema de ip de login",
    );

    if (!sucesso) {
      throw new HttpException("Falha ao cancelar prestação de contas.", HttpStatus.EXPECTATION_FAILED);
    }

    return sucesso;
  }
}
