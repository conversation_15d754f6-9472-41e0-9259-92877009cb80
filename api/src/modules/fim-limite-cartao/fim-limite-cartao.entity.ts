import { FimLimiteCartao, SituacaoFimLimiteCartao } from "cpesc-shared";
import {
  Column,
  CreateDateColumn,
  Entity,
  JoinC<PERSON>umn,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import type { ApiLimiteCartao } from "../limite-cartao/limite-cartao.entity.js";

@Entity("CPESC_FIMLIMITECARTAO")
export class ApiFimLimiteCartao implements FimLimiteCartao {
  @PrimaryGeneratedColumn({ name: "ID" })
  id: number;

  @Column({ name: "LIMITECARTAO_ID" })
  limiteCartaoId: number;

  @Column({ name: "VALORGAST<PERSON>" })
  valorGasto: number;

  @Column({ name: "ORGAOSGPE" })
  orgaoSgpe: string;

  @Column({ name: "PROCESSOSGPE" })
  processoSgpe: string;

  @Column({ name: "ANOSGPE" })
  anoSgpe: string;

  @Column({ name: "ATUALIZADOPOR" })
  atualizadoPor: string;

  @Column({ name: "CRIADOPOR" })
  criadoPor: string;

  @CreateDateColumn({ name: "CRIADOEM" })
  criadoEm: Date;

  @UpdateDateColumn({ name: "ATUALIZADOEM" })
  atualizadoEm: Date;

  @Column({ name: "ATUALIZADOPORIP" })
  atualizadoPorIp: string;

  @Column({ name: "CRIADOPORIP" })
  criadoPorIp: string;

  @Column({ name: "SITUACAO" })
  situacao: SituacaoFimLimiteCartao;

  @OneToOne("ApiLimiteCartao", (l: ApiLimiteCartao) => l.id)
  @JoinColumn({ name: "LIMITECARTAO_ID" })
  limite: ApiLimiteCartao;
}
