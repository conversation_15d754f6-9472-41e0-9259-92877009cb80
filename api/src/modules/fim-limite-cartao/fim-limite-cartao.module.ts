import { Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { AuthModule } from "../auth/auth.module.js";
import { HistoricoFimLimiteCartaoModule } from "../historico-fim-limite-cartao/historico-fim-limite-cartao.module.js";
import { LimiteCartaoModule } from "../limite-cartao/limite-cartao.module.js";
import { MovimentacaoModule } from "../movimentacao/movimentacao.module.js";
import { PortadorModule } from "../portador/portador.module.js";
import { SharedModule } from "../shared/shared.module.js";
import { UsuarioModule } from "../usuario/usuario.module.js";
import { FimLimiteCartaoController } from "./fim-limite-cartao.controller.js";
import { ApiFimLimiteCartao } from "./fim-limite-cartao.entity.js";
import { FimLimiteCartaoService } from "./fim-limite-cartao.service.js";
@Module({
  imports: [
    TypeOrmModule.forFeature([ApiFimLimiteCartao]),
    AuthModule,
    UsuarioModule,
    PortadorModule,
    SharedModule,
    MovimentacaoModule,
    HistoricoFimLimiteCartaoModule,
    LimiteCartaoModule,
  ],
  controllers: [FimLimiteCartaoController],
  providers: [FimLimiteCartaoService],
  exports: [FimLimiteCartaoService],
})
export class FimLimiteCartaoModule {}
