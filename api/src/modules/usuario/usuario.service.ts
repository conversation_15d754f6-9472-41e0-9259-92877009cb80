import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { Usuario } from "./usuario.entity.js";

@Injectable()
export class UsuarioService {
  constructor(@InjectRepository(Usuario) private usuarioRepository: Repository<Usuario>) {}

  async getUsuarios(): Promise<Usuario[]> {
    return this.usuarioRepository.find();
  }

  async getUsuarioPorId(id: number): Promise<Usuario | null> {
    return this.usuarioRepository.findOneBy({ id });
  }

  async getUsuarioAtivoPorId(id: number): Promise<Usuario | null> {
    return this.usuarioRepository.findOneBy({
      id,
      ativo: "S",
    });
  }

  async getUsuarioAtivoPorEmail(email: string): Promise<Usuario | null> {
    return this.usuarioRepository.findOne({
      where: { email, ativo: "S" },
    });
  }

  async getUsuarioPorEmail(email: string): Promise<Usuario | null> {
    return this.usuarioRepository.findOne({
      where: { email },
    });
  }

  async setUsuario(usuario: Usuario): Promise<void> {
    await this.usuarioRepository.save(usuario);
  }
}
