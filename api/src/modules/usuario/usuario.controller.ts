import { Controller, Get, Param, UseGuards } from "@nestjs/common";
import { AuthGuard } from "../auth/auth.guard.js";
import { UsuarioService } from "./usuario.service.js";

@Controller("usuario")
@UseGuards(AuthGuard)
export class UsuarioController {
  constructor(private readonly usuarioService: UsuarioService) {}

  @Get()
  async findAll() {
    return this.usuarioService.getUsuarios();
  }

  @Get("/:id")
  async findUsuarioPorId(@Param("id") id: string) {
    return this.usuarioService.getUsuarioPorId(+id);
  }
}
