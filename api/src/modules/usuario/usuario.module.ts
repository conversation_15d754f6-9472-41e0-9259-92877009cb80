import { forwardRef, Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { AuthModule } from "../auth/auth.module.js";
import { PortadorModule } from "../portador/portador.module.js";
import { SharedModule } from "../shared/shared.module.js";
import { UsuarioController } from "./usuario.controller.js";
import { Usuario } from "./usuario.entity.js";
import { UsuarioService } from "./usuario.service.js";

@Module({
  imports: [
    TypeOrmModule.forFeature([Usuario]),
    forwardRef(() => AuthModule),
    forwardRef(() => PortadorModule),
    SharedModule,
  ],
  controllers: [UsuarioController],
  providers: [UsuarioService],
  exports: [UsuarioService],
})
export class UsuarioModule {}
