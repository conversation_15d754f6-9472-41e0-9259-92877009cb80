import { Perfil } from "cpesc-shared";
import { Column, CreateDate<PERSON><PERSON>umn, Entity, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";

@Entity("CPESC_USUARIO")
export class Usuario {
  @PrimaryGeneratedColumn({ name: "ID" })
  id: number;

  @Column({ name: "NOME" })
  nome: string;

  @Column({ name: "EMA<PERSON>" })
  email: string;

  @Column({ name: "RECE<PERSON>EMA<PERSON>" })
  enviarEmail: number;

  @Column({ name: "CRIADOPOR" })
  createdBy: string;

  @CreateDateColumn({ name: "CRIADOEM" })
  createdAt: Date;

  @Column({ name: "ATUALIZADOPOR" })
  updatedBy: string;

  @UpdateDateColumn({ name: "ATUALIZADOEM" })
  updatedAt: Date;

  @Column({ name: "ULTIMOACESSO" })
  dtUltimoAcesso: Date;

  @Column({ name: "PERFIL_ID" })
  perfil: Perfil;

  @Column({ name: "CODIGOOT<PERSON>" })
  codigoOtp: string;

  @Column({ name: "EX<PERSON><PERSON><PERSON>OOT<PERSON>" })
  otpExpiracao: Date;

  @Column({ name: "ATIVO" })
  ativo: string;
}
