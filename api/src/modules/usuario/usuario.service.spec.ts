import { jest } from "@jest/globals";
import { Test, type TestingModule } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import { Usuario } from "./usuario.entity.js";
import { UsuarioService } from "./usuario.service.js";

describe("UsuarioService", () => {
  let service: UsuarioService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [UsuarioService, { provide: getRepositoryToken(Usuario), useFactory: jest.fn }],
    }).compile();

    service = module.get<UsuarioService>(UsuarioService);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });
});
