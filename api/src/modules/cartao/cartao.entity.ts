import { <PERSON><PERSON><PERSON> } from "cpesc-shared";
import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany, PrimaryGeneratedColumn } from "typeorm";
//import type { ApiLimiteCartao } from "../limite-cartao/limite-cartao.entity.js";
import type { ApiMovimentacao } from "../movimentacao/movimentacao.entity.js";
import type { ApiPortadorCentroCusto } from "../portador-centro-custo/portador-centro-custo.entity.js";
import type { ApiPortadorUnidadeAdministrativa } from "../portador-unidadeadministrativa/portador-unidadeadministrativa.entity.js";
import { ApiLimiteCartao } from "../limite-cartao/limite-cartao.entity.js";

@Entity("CPESC_CARTAO")
export class ApiCartao implements Cartao {
  @PrimaryGeneratedColumn({ name: "ID" })
  id: number;

  @Column({ name: "NUMEROCARTAO" })
  numero: string;

  @Column({ name: "ATIVO" })
  ativo: string;

  @Column({ name: "NUCONTACARTAO" })
  nuContaCartao: number;

  @Column({ name: "CRIADOPOR" })
  criadoPor: string;

  @Column({ name: "CRIADOEM" })
  criadoEm: Date;

  @Column({ name: "ATUALIZADOPOR" })
  atualizadoPor: string;

  @Column({ name: "ATUALIZADOEM" })
  atualizadoEm: Date;

  @ManyToOne("ApiPortadorCentroCusto", (pcc: ApiPortadorCentroCusto) => pcc.cartoes)
  @JoinColumn({ name: "PORTADORCARTAOCENTROCUSTO_ID" })
  portadorCentroCusto: ApiPortadorCentroCusto;

  @ManyToOne("ApiPortadorUnidadeAdministrativa")
  @JoinColumn({ name: "PORTADORCARTAOUNIDADEADMINISTRATIVA_ID" })
  portadorUnidadeAdministrativa: ApiPortadorUnidadeAdministrativa;

  @OneToMany("ApiLimiteCartao", (lc: ApiLimiteCartao) => lc.cartao)
  @JoinColumn([{ name: "CARTAO_ID" }])
  limitesCartao: ApiLimiteCartao[];

  @OneToMany("ApiMovimentacao", (m: ApiMovimentacao) => m.cartaoMovimentacao)
  @JoinColumn([{ name: "NUCONTACARTAO", referencedColumnName: "nuContaCartao" }])
  movimentacoes: ApiMovimentacao[];
}
