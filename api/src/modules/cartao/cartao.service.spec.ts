import { jest } from "@jest/globals";
import { Test, type TestingModule } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import { ApiCartao } from "./cartao.entity.js";
import { CartaoService } from "./cartao.service.js";

describe("CartaoService", () => {
  let service: CartaoService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [CartaoService, { provide: getRepositoryToken(ApiCartao), useFactory: jest.fn }],
    }).compile();

    service = module.get<CartaoService>(CartaoService);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });
});
