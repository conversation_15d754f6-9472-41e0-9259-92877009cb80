import { Controller, Get, Param, UseGuards } from "@nestjs/common";
import { AuthGuard } from "../auth/auth.guard.js";
import { CartaoService } from "./cartao.service.js";

@Controller("cartao")
@UseGuards(AuthGuard)
export class CartaoController {
  constructor(private readonly cartaoService: CartaoService) {}

  @Get()
  async findAll() {
    return this.cartaoService.getCartoesAtivos();
  }

  @Get("/:id")
  async findOneByDynamic(@Param("id") id: number) {
    return this.cartaoService.getCartaoPorId(id);
  }
}
