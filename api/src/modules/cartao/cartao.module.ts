import { forwardRef, <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { AuthModule } from "../auth/auth.module.js";
import { PortadorModule } from "../portador/portador.module.js";
import { SharedModule } from "../shared/shared.module.js";
import { UsuarioModule } from "../usuario/usuario.module.js";
import { CartaoController } from "./cartao.controller.js";
import { ApiCartao } from "./cartao.entity.js";
import { CartaoService } from "./cartao.service.js";

@Module({
  imports: [
    TypeOrmModule.forFeature([ApiCartao]),
    forwardRef(() => AuthModule),
    UsuarioModule,
    PortadorModule,
    SharedModule,
  ],
  controllers: [CartaoController],
  providers: [CartaoService],
  exports: [CartaoService],
})
export class CartaoModule {}
