import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { ApiCartao } from "./cartao.entity.js";

@Injectable()
export class CartaoService {
  constructor(@InjectRepository(ApiCartao) private cartaoRepository: Repository<ApiCartao>) {}

  async getCartoesAtivos(): Promise<ApiCartao[]> {
    return this.cartaoRepository.find({
      where: { ativo: "S" },
    });
  }

  async getCartaoPorId(id: number): Promise<ApiCartao | null> {
    //return this.cartaoRepository.findOneBy({ id });
    return this.cartaoRepository.findOne({
      where: {
        id,
        ativo: "S",
      },
      relations: { portadorCentroCusto: { portador: true } },
    });
  }

  async getCartoesPorCpfPortador(cpf: string): Promise<ApiCartao[] | null> {
    const result = await this.cartaoRepository
      .createQueryBuilder("cartao")
      .innerJoin("cartao.portador", "portador")
      .where("portador.cpf = :cpf", { cpf })
      .select("cartao.cartao")
      .getMany();

    return result;
  }

  async getCartaoAtivoPorNumero(nuCartao: string): Promise<ApiCartao | null> {
    return this.cartaoRepository.findOne({
      where: {
        numero: nuCartao,
        ativo: "S",
      },
      relations: { portadorUnidadeAdministrativa: { portador: true } },
    });
  }
}
