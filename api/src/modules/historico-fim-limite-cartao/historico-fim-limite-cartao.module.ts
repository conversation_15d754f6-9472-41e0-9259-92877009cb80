import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { AuthModule } from "../auth/auth.module.js";
import { PortadorModule } from "../portador/portador.module.js";
import { SharedModule } from "../shared/shared.module.js";
import { UsuarioModule } from "../usuario/usuario.module.js";
import { ApiHistoricoFimLimiteCartao } from "./historico-fim-limite-cartao.entity.js";
import { HistoricoFimLimiteCartaoService } from "./historico-fim-limite-cartao.service.js";
@Module({
  imports: [
    TypeOrmModule.forFeature([ApiHistoricoFimLimiteCartao]),
    AuthModule,
    UsuarioModule,
    PortadorModule,
    SharedModule,
  ],
  providers: [HistoricoFimLimiteCartaoService],
  exports: [HistoricoFimLimiteCartaoService],
})
export class HistoricoFimLimiteCartaoModule {}
