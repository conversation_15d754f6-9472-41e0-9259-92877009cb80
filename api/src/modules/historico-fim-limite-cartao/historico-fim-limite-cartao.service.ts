import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { ApiHistoricoFimLimiteCartao } from "./historico-fim-limite-cartao.entity.js";

@Injectable()
export class HistoricoFimLimiteCartaoService {
  constructor(
    @InjectRepository(ApiHistoricoFimLimiteCartao)
    private hisFimLimiteCartaoRepository: Repository<ApiHistoricoFimLimiteCartao>,
  ) {}

  async criaHistoricoFimLimiteCartao(
    fimLimiteCartaoId: number,
    motivo: string,
    email: string,
    ip: string,
  ): Promise<ApiHistoricoFimLimiteCartao> {
    // Monta o objeto com os dados recebidos como parâmetros

    const dados: Partial<ApiHistoricoFimLimiteCartao> = {
      fimLimiteCartaoId,
      motivo,
      criadoPor: email,
      criadoPorIp: ip,
    };
    const h = this.hisFimLimiteCartaoRepository.create(dados);
    return this.hisFimLimiteCartaoRepository.save(h);
  }
}
