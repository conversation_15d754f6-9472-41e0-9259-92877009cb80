import { jest } from "@jest/globals";
import { Test, type TestingModule } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import { DataSource } from "typeorm";
import { createDataSourceMock } from "../../test-utils/mock-datasource.js";
import { ApiDocumentoFiscalItem } from "../documento-fiscal-item/documento-fiscal-item.entity.js";
import { DocumentoFiscalItemService } from "../documento-fiscal-item/documento-fiscal-item.service.js";
import { ApiDocumentoFiscal } from "../documento-fiscal/documento-fiscal.entity.js";
import { DocumentoFiscalService } from "../documento-fiscal/documento-fiscal.service.js";
import { ApiFimLimiteCartao } from "../fim-limite-cartao/fim-limite-cartao.entity.js";
import { FimLimiteCartaoService } from "../fim-limite-cartao/fim-limite-cartao.service.js";
import { ApiItemFiscal } from "../item-fiscal/item-fiscal.entity.js";
import { ItemFiscalService } from "../item-fiscal/item-fiscal.service.js";
import { ApiLimiteCartao } from "../limite-cartao/limite-cartao.entity.js";
import { LimiteCartaoService } from "../limite-cartao/limite-cartao.service.js";
import { ApiMovimentacao } from "../movimentacao/movimentacao.entity.js";
import { MovimentacaoService } from "../movimentacao/movimentacao.service.js";
import { ApiHistoricoFimLimiteCartao } from "./historico-fim-limite-cartao.entity.js";
import { HistoricoFimLimiteCartaoService } from "./historico-fim-limite-cartao.service.js";

describe("HistoricoFimLimiteCartaoService", () => {
  let service: HistoricoFimLimiteCartaoService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DocumentoFiscalService,
        DocumentoFiscalItemService,
        ItemFiscalService,
        MovimentacaoService,
        LimiteCartaoService,
        FimLimiteCartaoService,
        HistoricoFimLimiteCartaoService,
        { provide: getRepositoryToken(ApiHistoricoFimLimiteCartao), useFactory: jest.fn },
        { provide: getRepositoryToken(ApiFimLimiteCartao), useFactory: jest.fn },
        { provide: getRepositoryToken(ApiDocumentoFiscal), useFactory: jest.fn },
        { provide: getRepositoryToken(ApiDocumentoFiscalItem), useFactory: jest.fn },
        { provide: getRepositoryToken(ApiItemFiscal), useFactory: jest.fn },
        { provide: getRepositoryToken(ApiMovimentacao), useFactory: jest.fn },
        { provide: getRepositoryToken(ApiLimiteCartao), useFactory: jest.fn },
        { provide: DataSource, useFactory: createDataSourceMock },
      ],
    }).compile();

    service = module.get<HistoricoFimLimiteCartaoService>(HistoricoFimLimiteCartaoService);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });
});
