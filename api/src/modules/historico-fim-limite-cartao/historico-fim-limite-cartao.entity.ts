import { <PERSON>um<PERSON>, CreateDate<PERSON><PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, PrimaryGeneratedColumn } from "typeorm";
import { ApiFimLimiteCartao } from "../fim-limite-cartao/fim-limite-cartao.entity.js";

@Entity("CPESC_HISTORICOFIMLIMITECARTAO")
export class ApiHistoricoFimLimiteCartao {
  @PrimaryGeneratedColumn({ name: "ID" })
  id: number;

  @Column({ name: "FIMLIMITECARTAO_ID" })
  fimLimiteCartaoId: number;

  @Column({ name: "MOTIVO" })
  motivo: string;

  @Column({ name: "CRIADOPOR" })
  criadoPor: string;

  @CreateDateColumn({ name: "CRIADOEM" })
  criadoEm: Date;

  @Column({ name: "CRIADOPORIP" })
  criadoPorIp: string;

  @ManyToOne("ApiFimLimiteCartao", (l: ApiFimLimiteCartao) => l.id)
  @JoinC<PERSON>umn({ name: "FIMLIMITECARTAO_ID" })
  fimLimite: ApiFimLimiteCartao;
}
