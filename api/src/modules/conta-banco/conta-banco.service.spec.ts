import { jest } from "@jest/globals";
import { Test, type TestingModule } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import { ApiContaBanco } from "./conta-banco.entity.js";
import { ContaBancoService } from "./conta-banco.service.js";

describe("ContaBancoService", () => {
  let service: ContaBancoService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ContaBancoService, { provide: getRepositoryToken(ApiContaBanco), useFactory: jest.fn }],
    }).compile();

    service = module.get<ContaBancoService>(ContaBancoService);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });
});
