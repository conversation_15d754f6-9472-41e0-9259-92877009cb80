import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { SharedModule } from "../shared/shared.module.js";
import { ContaBancoController } from "./conta-banco.controller.js";
import { ApiContaBanco } from "./conta-banco.entity.js";
import { ContaBancoService } from "./conta-banco.service.js";

@Module({
  imports: [TypeOrmModule.forFeature([ApiContaBanco]), SharedModule],
  controllers: [ContaBancoController],
  providers: [ContaBancoService],
  exports: [ContaBancoService],
})
export class ContaBancoModule {}
