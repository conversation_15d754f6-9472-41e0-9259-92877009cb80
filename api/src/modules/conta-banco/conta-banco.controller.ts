import { Controller, Get, Param } from "@nestjs/common";
import { ContaBancoService } from "./conta-banco.service.js";

@Controller("conta-banco")
//@UseGuards(AuthGuard)
export class ContaBancoController {
  constructor(private readonly contaBancoService: ContaBancoService) {}

  @Get()
  async findAll() {
    return this.contaBancoService.getContaBancos();
  }

  @Get("/:id")
  async findContaBancoPorId(@Param("id") id: string) {
    return this.contaBancoService.getContaBancoPorId(+id);
  }
}
