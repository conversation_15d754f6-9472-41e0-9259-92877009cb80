import { ContaBanco } from "cpesc-shared";
import {
  <PERSON><PERSON><PERSON>,
  CreateDate<PERSON>olumn,
  <PERSON><PERSON>ty,
  <PERSON>in<PERSON><PERSON>um<PERSON>,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import type { ApiUnidadeGestora } from "../unidade-gestora/unidade-gestora.entity.js";

@Entity("CPESC_CONTABANCO")
export class ApiContaBanco implements ContaBanco {
  @PrimaryGeneratedColumn({ name: "ID" })
  id: number;

  @Column({ name: "CODIGOMCI" })
  codigoMci: string;

  @Column({ name: "PROCESSO" })
  processo: number;

  @Column({ name: "AGENCIA" })
  agencia: string;

  @Column({ name: "AGENCIADV" })
  digitoAgencia: string;

  @Column({ name: "CONTA" })
  conta: string;

  @Column({ name: "CONTADV" })
  digitoConta: string;

  @Column({ name: "ATUALIZADOPOR" })
  atualizadoPor: string;

  @Column({ name: "CRIADOPOR" })
  criadoPor: string;

  @CreateDateColumn({ name: "CRIADOEM" })
  criadoEm: Date;

  @UpdateDateColumn({ name: "ATUALIZADOEM" })
  atualizadoEm: Date;

  @OneToOne("ApiUnidadeGestora", (ug: ApiUnidadeGestora) => ug.id)
  @JoinColumn({ name: "UNIDADEGESTORA_ID" })
  unidadeGestora: ApiUnidadeGestora;
}
