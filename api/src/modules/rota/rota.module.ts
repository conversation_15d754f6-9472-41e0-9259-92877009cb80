import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { SharedModule } from "../shared/shared.module.js";
import { ApiRota } from "./rota.entity.js";
import { RotaService } from "./rota.service.js";

@Module({
  imports: [TypeOrmModule.forFeature([ApiRota]), SharedModule],
  controllers: [],
  providers: [RotaService],
  exports: [RotaService],
})
export class RotaModule {}
