import { Injectable, NotFoundException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { EnvService } from "../shared/env.service.js";
import { ApiRota } from "./rota.entity.js";

@Injectable()
export class RotaService {
  constructor(
    @InjectRepository(ApiRota) private rotaRepository: Repository<ApiRota>,
    private envService: EnvService,
  ) {}

  async getRotaByUrl(url: string): Promise<ApiRota> {
    url = url.replace(this.envService.get("FRONTEND_URL"), "");
    const rota = await this.rotaRepository.findOneBy({ url });

    if (rota !== null) {
      return rota;
    }

    const rotasComParametos = await this.rotaRepository.findBy({ possuiParametros: 1 });

    for (const rotaCandidata of rotasComParametos) {
      const regexBase = rotaCandidata.url.replace(/(:[A-Za-z0-9]+)/g, "[A-Za-z0-9]+");
      const regex = new RegExp(`^${regexBase}$`);

      if (regex.test(url)) {
        return rotaCandidata;
      }
    }

    throw new NotFoundException("Rota não encontrada: " + url);
  }
}
