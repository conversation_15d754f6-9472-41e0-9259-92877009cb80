import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { AuthModule } from "../auth/auth.module.js";
import { DocumentoFiscalModule } from "../documento-fiscal/documento-fiscal.module.js";
import { PortadorModule } from "../portador/portador.module.js";
import { SharedModule } from "../shared/shared.module.js";
import { UsuarioModule } from "../usuario/usuario.module.js";
import { MovimentacaoController } from "./movimentacao.controller.js";
import { ApiMovimentacao } from "./movimentacao.entity.js";
import { MovimentacaoService } from "./movimentacao.service.js";
@Module({
  imports: [
    TypeOrmModule.forFeature([ApiMovimentacao]),
    AuthModule,
    UsuarioModule,
    PortadorModule,
    SharedModule,
    DocumentoFiscalModule,
  ],
  controllers: [MovimentacaoController],
  providers: [MovimentacaoService],
  exports: [MovimentacaoService],
})
export class MovimentacaoModule {}
