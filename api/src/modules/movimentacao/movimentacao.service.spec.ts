import { jest } from "@jest/globals";
import { Test, type TestingModule } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import { DataSource } from "typeorm";
import { createDataSourceMock } from "../../test-utils/mock-datasource.js";
import { ApiDocumentoFiscalItem } from "../documento-fiscal-item/documento-fiscal-item.entity.js";
import { DocumentoFiscalItemService } from "../documento-fiscal-item/documento-fiscal-item.service.js";
import { ApiDocumentoFiscal } from "../documento-fiscal/documento-fiscal.entity.js";
import { DocumentoFiscalService } from "../documento-fiscal/documento-fiscal.service.js";
import { ApiItemFiscal } from "../item-fiscal/item-fiscal.entity.js";
import { ItemFiscalService } from "../item-fiscal/item-fiscal.service.js";
import { ApiMovimentacao } from "./movimentacao.entity.js";
import { MovimentacaoService } from "./movimentacao.service.js";

describe("MovimentacaoService", () => {
  let service: MovimentacaoService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DocumentoFiscalService,
        DocumentoFiscalItemService,
        ItemFiscalService,
        MovimentacaoService,
        { provide: getRepositoryToken(ApiDocumentoFiscal), useFactory: jest.fn },
        { provide: getRepositoryToken(ApiDocumentoFiscalItem), useFactory: jest.fn },
        { provide: getRepositoryToken(ApiItemFiscal), useFactory: jest.fn },
        { provide: getRepositoryToken(ApiMovimentacao), useFactory: jest.fn },
        { provide: DataSource, useFactory: createDataSourceMock },
      ],
    }).compile();

    service = module.get<MovimentacaoService>(MovimentacaoService);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });
});
