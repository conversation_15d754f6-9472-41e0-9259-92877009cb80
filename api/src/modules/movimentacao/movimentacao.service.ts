import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { IsNull, LessThanOrEqual, Raw, Repository } from "typeorm";
import { DocumentoFiscalService } from "../documento-fiscal/documento-fiscal.service.js";
import { ApiMovimentacao } from "./movimentacao.entity.js";

@Injectable()
export class MovimentacaoService {
  constructor(
    @InjectRepository(ApiMovimentacao) private movimentacaoRepository: Repository<ApiMovimentacao>,
    private documentoFiscalService: DocumentoFiscalService,
  ) {}

  async getAllMovimentacao(): Promise<ApiMovimentacao[]> {
    return this.movimentacaoRepository.find({
      where: {
        id: Raw(() => "ROWNUM <= 10"),
      },
      relations: {
        portador: true,
        documentoFiscal: true,
      },
    });
  }

  async getMovimentacaoPorId(id: number): Promise<ApiMovimentacao | null> {
    const movimentacao = await this.movimentacaoRepository.findOne({
      where: { id },
      relations: {
        portador: true,
        documentoFiscal: { documentoFiscalItens: true },
      },
    });

    if (!movimentacao) {
      return null;
    }

    movimentacao.valorTransacaoReal =
      movimentacao.codigoTransacaoBB === 253600
        ? movimentacao.valorTransacaoReal * -1
        : movimentacao.valorTransacaoReal;
    return movimentacao;
  }

  async getMovimentacoesPorPeriodo(
    limiteId: number,
    fimPeriodo: Date,
    nuContaCartao: number,
  ): Promise<ApiMovimentacao[]> {
    if (!nuContaCartao) {
      return [];
    }

    const movimentacoes = await this.movimentacaoRepository.find({
      where: [
        {
          nuContaCartao,
          dataTransacao: LessThanOrEqual(fimPeriodo),
          limiteCartaoId: IsNull(),
        },
        {
          nuContaCartao,
          dataTransacao: LessThanOrEqual(fimPeriodo),
          limiteCartaoId: limiteId,
        },
      ],
      relations: {
        documentoFiscal: { documentoFiscalItens: true },
      },
      order: {
        dataTransacao: "ASC",
        valorTransacaoReal: "DESC",
        codigoTransacaoBB: "ASC",
      },
    });

    return movimentacoes.map(gasto => ({
      ...gasto,
      nomeEstabelecimento: gasto.nomeEstabelecimento
        ? gasto.nomeEstabelecimento.length > 3
          ? gasto.nomeEstabelecimento
          : gasto.descricaoTransacao
        : gasto.descricaoTransacao,
      CNPJEstabelecimento: gasto.CNPJEstabelecimento === "00000000000000" ? "" : gasto.CNPJEstabelecimento,
      valorTransacaoReal: gasto.codigoTransacaoBB === 253600 ? gasto.valorTransacaoReal * -1 : gasto.valorTransacaoReal,
    }));
  }

  async removerVinculoDocumentoFiscal(id: number): Promise<boolean> {
    try {
      const movimentacao = await this.movimentacaoRepository.findOne({
        where: { id },
        relations: { documentoFiscal: { documentoFiscalItens: true } },
      });

      if (!movimentacao) {
        return false;
      }

      const notaId = movimentacao.documentoFiscal?.id;

      if (notaId) {
        await this.documentoFiscalService.removeDocumentoFiscalEDocumentoFiscalItem(notaId);
      }

      movimentacao.limiteCartaoId = null;
      movimentacao.documentoFiscal = null;

      await this.movimentacaoRepository.save(movimentacao);

      return true;
    } catch (error) {
      console.error("Erro ao remover vínculo com a nota fiscal:", error);
      return false;
    }
  }

  async removerMovimentacoesVinculados(id: number): Promise<boolean> {
    try {
      const movimentacao = await this.movimentacaoRepository.find({
        where: { limiteCartaoId: id },
      });

      for (const m of movimentacao) {
        m.limiteCartaoId = null;
      }
      await this.movimentacaoRepository.save(movimentacao);

      return true;
    } catch (error) {
      console.error("Erro ao remover movimentações vinculadas:", error);
      return false;
    }
  }

  async associarLimite(movimentacaoIds: number[], limiteCartaoId: number): Promise<void> {
    for (const id of movimentacaoIds) {
      const movimentacao = await this.movimentacaoRepository.findOne({ where: { id } });
      if (movimentacao) {
        movimentacao.limiteCartaoId = limiteCartaoId;
        await this.movimentacaoRepository.save(movimentacao);
      }
    }
  }
}
