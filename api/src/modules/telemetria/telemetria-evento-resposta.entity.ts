import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON><PERSON>, PrimaryGeneratedColumn } from "typeorm";
import { ApiTelemetriaEvento } from "./telemetria-evento.entity.js";

@Entity("CPESC_TELEMETRIA_EVENTO_RESPOSTA")
export class ApiTelemetriaEventoResposta {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: "evento_id" })
  eventoId: number;

  @Column({ name: "status_code" })
  statusCode: number;

  @Column({ name: "tempo_execucao" })
  tempoExecucao: number;

  @ManyToOne(() => ApiTelemetriaEvento, evento => evento.respostas)
  @JoinColumn({ name: "evento_id" })
  evento: ApiTelemetriaEvento;
}
