import { Body, Controller, Get, Param, Post, Query, Req, UseGuards } from "@nestjs/common";
import { assertNotNull } from "cpesc-shared/out/assertions.js";
import { endpoints, RespostaRota } from "cpesc-shared/out/endpoints/main.js";
import { validarPayload } from "../../helpers/validar-payload.js";
import { AuthGuard, RequisicaoAutenticada } from "../auth/auth.guard.js";
import { TelemetriaService } from "./telemetria.service.js";

@Controller("telemetria")
@UseGuards(AuthGuard)
export class TelemetriaController {
  constructor(private readonly telemetriaService: TelemetriaService) {}

  @Post("/")
  async registrarTelemetria(
    @Req() req: RequisicaoAutenticada,
    @Body() payload: unknown,
  ): RespostaRota<typeof endpoints.registrarTelemetria> {
    const eventoTelemetria = validarPayload(payload, endpoints.registrarTelemetria);
    assertNotNull(req.usuario, "Usuário não autenticado");

    return this.telemetriaService.registrarTelemetria(req.usuario.id, eventoTelemetria);
  }

  @Post("/:id")
  async registrarTelemetriaResposta(
    @Param("id") id: number,
    @Body() payload: unknown,
  ): RespostaRota<typeof endpoints.registrarTelemetriaResposta> {
    const eventoResposta = validarPayload(payload, endpoints.registrarTelemetriaResposta);
    await this.telemetriaService.armazenarEventoResposta(id, eventoResposta);
  }

  @Get("/dashboard/usuarios-mais-ativos")
  async getUsuariosMaisAtivos(
    @Query("limite") limite = 10,
    @Query("dataInicial") dataInicial?: string,
    @Query("dataFinal") dataFinal?: string,
  ) {
    return this.telemetriaService.getUsuariosMaisAtivos(Number(limite), dataInicial, dataFinal);
  }

  @Get("/dashboard/rotas-mais-acessadas")
  async getRotasMaisAcessadas(
    @Query("limite") limite = 10,
    @Query("dataInicial") dataInicial?: string,
    @Query("dataFinal") dataFinal?: string,
  ) {
    return this.telemetriaService.getRotasMaisAcessadas(Number(limite), dataInicial, dataFinal);
  }

  @Get("/dashboard/rotas-mais-demoradas")
  async getRotasMaisDemoradas(
    @Query("limite") limite = 10,
    @Query("dataInicial") dataInicial?: string,
    @Query("dataFinal") dataFinal?: string,
  ) {
    return this.telemetriaService.getRotasMaisDemoradas(Number(limite), dataInicial, dataFinal);
  }

  @Get("/dashboard/estatisticas-gerais")
  async getEstatisticasGerais(@Query("dataInicial") dataInicial?: string, @Query("dataFinal") dataFinal?: string) {
    return this.telemetriaService.getEstatisticasGerais(dataInicial, dataFinal);
  }
}
