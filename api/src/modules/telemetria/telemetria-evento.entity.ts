import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany, PrimaryGeneratedColumn } from "typeorm";
import { Usuario } from "../usuario/usuario.entity.js";
import { ApiTelemetriaEventoResposta } from "./telemetria-evento-resposta.entity.js";

@Entity("CPESC_TELEMETRIA_EVENTO")
export class ApiTelemetriaEvento {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: "tipo_evento" })
  tipoEvento: number;

  @Column({ name: "rota_id" })
  rotaId: number;

  @Column({ name: "rota_frontend" })
  rotaFrontend: string;

  @Column({ name: "rota_api", type: String, nullable: true })
  rotaApi: string | null;

  @Column({ name: "verbo_http", type: String, nullable: true })
  verboHttp: string | null;

  @Column({ name: "usuario_id" })
  usuarioId: number;

  @Column({ name: "created_at", type: "timestamp", default: () => "CURRENT_TIMESTAMP" })
  createdAt: Date;

  @ManyToOne(() => Usuario)
  @JoinColumn({ name: "usuario_id" })
  usuario: Usuario;

  @OneToMany(() => ApiTelemetriaEventoResposta, resposta => resposta.evento)
  respostas: ApiTelemetriaEventoResposta[];
}
