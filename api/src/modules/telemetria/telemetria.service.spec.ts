import { jest } from "@jest/globals";
import { ConfigService } from "@nestjs/config";
import { Test, type TestingModule } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import { ApiRota } from "../rota/rota.entity.js";
import { RotaService } from "../rota/rota.service.js";
import { EnvService } from "../shared/env.service.js";
import { ApiTelemetriaEventoResposta } from "./telemetria-evento-resposta.entity.js";
import { ApiTelemetriaEvento } from "./telemetria-evento.entity.js";
import { TelemetriaService } from "./telemetria.service.js";

describe("TelemetriaService", () => {
  let service: TelemetriaService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ConfigService,
        EnvService,
        RotaService,
        TelemetriaService,
        { provide: getRepositoryToken(ApiRota), useFactory: jest.fn },
        { provide: getRepositoryToken(ApiTelemetriaEvento), useFactory: jest.fn },
        { provide: getRepositoryToken(ApiTelemetriaEventoResposta), useFactory: jest.fn },
      ],
    }).compile();

    service = module.get<TelemetriaService>(TelemetriaService);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });
});
