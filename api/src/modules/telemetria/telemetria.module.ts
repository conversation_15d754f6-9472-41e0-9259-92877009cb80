import { forwardRef, Module } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { AuthModule } from "../auth/auth.module.js";
import { PortadorModule } from "../portador/portador.module.js";
import { RotaModule } from "../rota/rota.module.js";
import { SharedModule } from "../shared/shared.module.js";
import { UsuarioModule } from "../usuario/usuario.module.js";
import { ApiTelemetriaEventoResposta } from "./telemetria-evento-resposta.entity.js";
import { ApiTelemetriaEvento } from "./telemetria-evento.entity.js";
import { TelemetriaController } from "./telemetria.controller.js";
import { TelemetriaService } from "./telemetria.service.js";

@Module({
  imports: [
    TypeOrmModule.forFeature([ApiTelemetriaEvento, ApiTelemetriaEventoResposta]),
    forwardRef(() => AuthModule),
    UsuarioModule,
    PortadorModule,
    RotaModule,
    SharedModule,
  ],
  controllers: [TelemetriaController],
  providers: [TelemetriaService],
  exports: [TelemetriaService],
})
export class TelemetriaModule {}
