import { Injectable, NotFoundException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { switchExaustivo } from "cpesc-shared/out/assertions.js";
import {
  EventoTelemetria,
  EventoTelemetriaResposta,
  TipoEventoTelemetria,
} from "cpesc-shared/out/endpoints/telemetria.js";
import { Between, FindOptionsWhere, IsNull, LessThanOrEqual, MoreThanOrEqual, Not, Repository } from "typeorm";
import { RotaService } from "../rota/rota.service.js";
import { ApiTelemetriaEventoResposta } from "./telemetria-evento-resposta.entity.js";
import { ApiTelemetriaEvento } from "./telemetria-evento.entity.js";

@Injectable()
export class TelemetriaService {
  constructor(
    @InjectRepository(ApiTelemetriaEvento) private telemetriaEventoRepository: Repository<ApiTelemetriaEvento>,
    @InjectRepository(ApiTelemetriaEventoResposta)
    private telemetriaEventoRespostaRepository: Repository<ApiTelemetriaEventoResposta>,
    private rotaService: RotaService,
  ) {}

  async registrarTelemetria(usuarioId: number, evento: EventoTelemetria): Promise<number> {
    let eventoNovo: ApiTelemetriaEvento;
    const rota = await this.rotaService.getRotaByUrl(evento.url);

    switch (evento.tipo) {
      case TipoEventoTelemetria.AcessoRota:
        eventoNovo = this.telemetriaEventoRepository.create({
          tipoEvento: evento.tipo,
          rotaId: rota.id,
          rotaFrontend: evento.url,
          usuarioId,
        });
        break;
      case TipoEventoTelemetria.Consulta:
        eventoNovo = this.telemetriaEventoRepository.create({
          tipoEvento: evento.tipo,
          rotaId: rota.id,
          rotaFrontend: evento.url,
          rotaApi: evento.rotaApi,
          verboHttp: evento.verboHttp,
          usuarioId,
        });
        break;
      default:
        return switchExaustivo(evento);
    }

    const registro = await this.telemetriaEventoRepository.save(eventoNovo);
    return registro.id;
  }

  async armazenarEventoResposta(eventoId: number, eventoResposta: EventoTelemetriaResposta): Promise<void> {
    const evento = await this.telemetriaEventoRepository.findOneBy({ id: eventoId });

    if (evento === null) {
      throw new NotFoundException("Evento de telemetria não encontrado");
    }

    await this.telemetriaEventoRespostaRepository.save({
      eventoId,
      statusCode: eventoResposta.statusCode,
      tempoExecucao: eventoResposta.tempoExecucaoMs,
    });
  }

  private buildDateWhere(dataInicial?: string, dataFinal?: string): FindOptionsWhere<ApiTelemetriaEvento> {
    const where: FindOptionsWhere<ApiTelemetriaEvento> = {};
    void this.telemetriaEventoRepository;

    if (dataInicial && dataFinal) {
      const inicio = new Date(dataInicial);
      inicio.setUTCHours(0, 0, 0, 0);

      const fim = new Date(dataFinal);
      fim.setUTCHours(23, 59, 59, 999);

      where.createdAt = Between(inicio, fim);
    } else if (dataInicial) {
      const inicio = new Date(dataInicial);
      inicio.setUTCHours(0, 0, 0, 0);
      where.createdAt = MoreThanOrEqual(inicio);
    } else if (dataFinal) {
      const fim = new Date(dataFinal);
      fim.setUTCHours(23, 59, 59, 999);
      where.createdAt = LessThanOrEqual(fim);
    }
    return where;
  }

  async getUsuariosMaisAtivos(limite: number, dataInicial?: string, dataFinal?: string) {
    const usuarios = await this.telemetriaEventoRepository.find({
      relations: {
        usuario: true,
      },
      where: this.buildDateWhere(dataInicial, dataFinal),
      select: {
        usuarioId: true,
        usuario: {
          nome: true,
        },
      },
      order: {
        usuarioId: "ASC",
      },
    });

    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
    const usuariosValidos = usuarios.filter(evento => evento.usuario !== null);

    const contadores = usuariosValidos.reduce<Record<number, { count: number; nome: string }>>((acc, evento) => {
      if (!(evento.usuarioId in acc)) {
        acc[evento.usuarioId] = { count: 0, nome: evento.usuario.nome };
      }
      acc[evento.usuarioId].count++;
      return acc;
    }, {});

    return Object.entries(contadores)
      .map(([usuarioId, data]) => ({
        usuarioId: Number(usuarioId),
        nome: data.nome,
        totalAcessos: data.count,
      }))
      .sort((a, b) => b.totalAcessos - a.totalAcessos)
      .slice(0, limite);
  }

  async getRotasMaisAcessadas(limite: number, dataInicial?: string, dataFinal?: string) {
    const eventos = await this.telemetriaEventoRepository.find({
      select: {
        rotaFrontend: true,
      },
      where: this.buildDateWhere(dataInicial, dataFinal),
      order: {
        rotaFrontend: "ASC",
      },
    });

    const contadores = eventos.reduce<Record<string, number>>((acc, evento) => {
      acc[evento.rotaFrontend] = (acc[evento.rotaFrontend] || 0) + 1;
      return acc;
    }, {});

    return Object.entries(contadores)
      .map(([rota, totalAcessos]) => ({
        rota,
        totalAcessos,
      }))
      .sort((a, b) => b.totalAcessos - a.totalAcessos)
      .slice(0, limite);
  }

  async getRotasMaisDemoradas(limite: number, dataInicial?: string, dataFinal?: string) {
    const eventosComResposta = await this.telemetriaEventoRepository.find({
      relations: {
        respostas: true,
      },
      where: {
        ...this.buildDateWhere(dataInicial, dataFinal),
        respostas: {
          tempoExecucao: Not(IsNull()),
        },
      },
      select: {
        rotaFrontend: true,
        respostas: {
          tempoExecucao: true,
        },
      },
    });

    const dadosRotas = eventosComResposta.reduce<Record<string, { tempos: number[]; totalConsultas: number }>>(
      (acc, evento) => {
        const rota = evento.rotaFrontend;
        acc[rota] ??= { tempos: [], totalConsultas: 0 };

        evento.respostas.forEach(resposta => {
          if (resposta.tempoExecucao) {
            acc[rota].tempos.push(resposta.tempoExecucao);
            acc[rota].totalConsultas++;
          }
        });

        return acc;
      },
      {},
    );

    return Object.entries(dadosRotas)
      .map(([rota, dados]) => ({
        rota,
        tempoMedio: dados.tempos.reduce((sum, tempo) => sum + tempo, 0) / dados.tempos.length,
        totalConsultas: dados.totalConsultas,
      }))
      .filter(item => item.totalConsultas > 0)
      .sort((a, b) => b.tempoMedio - a.tempoMedio)
      .slice(0, limite);
  }

  async getEstatisticasGerais(dataInicial?: string, dataFinal?: string) {
    const where = this.buildDateWhere(dataInicial, dataFinal);
    const totalEventos = await this.telemetriaEventoRepository.count({ where });

    const usuariosUnicos = await this.telemetriaEventoRepository.find({
      select: {
        usuarioId: true,
      },
      where,
    });

    const totalUsuarios = new Set(usuariosUnicos.map(u => u.usuarioId)).size;

    return {
      totalEventos,
      totalUsuarios,
    };
  }
}
