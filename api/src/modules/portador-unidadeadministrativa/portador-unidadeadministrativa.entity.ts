import { PortadorUnidadeAdministrativa } from "cpesc-shared";
import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import type { ApiCartao } from "../cartao/cartao.entity.js";
import type { ApiPortador } from "../portador/portador.entity.js";
import type { ApiUnidadeAdministrativa } from "../unidade-administrativa/unidade-administrativa.entity.js";

@Entity("CPESC_PORTADORCARTAO_UNIDADEADMINISTRATIVA")
export class ApiPortadorUnidadeAdministrativa implements PortadorUnidadeAdministrativa {
  @PrimaryGeneratedColumn({ name: "ID" })
  id: number;

  @Column({ name: "MATRICULA" })
  matricula: string;

  @Column({ name: "ATIVO" })
  ativo: string;

  @Column({ name: "CRIADOPOR" })
  criadoPor: string;

  @CreateDateColumn({ name: "CRIADOEM" })
  criadoEm: Date;

  @Column({ name: "ATUALIZADOPOR" })
  atualizadoPor: string;

  @UpdateDateColumn({ name: "ATUALIZADOEM" })
  atualizadoEm: Date;

  @ManyToOne("ApiPortador")
  @JoinColumn({ name: "PORTADORCARTAO_ID" })
  portador: ApiPortador;

  @ManyToOne("ApiUnidadeAdministrativa")
  @JoinColumn({ name: "UNIDADEADMINISTRATIVA_ID" })
  unidadeAdministrativa: ApiUnidadeAdministrativa;

  @OneToMany("ApiCartao", (c: ApiCartao) => c.portadorUnidadeAdministrativa)
  cartoes: ApiCartao[];
}
