import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToMany,
  ManyToOne,
  OneToMany,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import type { ApiCartao } from "../cartao/cartao.entity.js";
import type { ApiCentroCusto } from "../centro-custo/centro-custo.entity.js";
import type { ApiPortadorUnidadeAdministrativa } from "../portador-unidadeadministrativa/portador-unidadeadministrativa.entity.js";
import type { ApiPortador } from "../portador/portador.entity.js";

@Entity("CPESC_PORTADORCARTAO_CENTROCUSTO")
export class ApiPortadorCentroCusto {
  @PrimaryGeneratedColumn({ name: "ID" })
  id: number;

  @Column({ name: "ATIVO" })
  ativo: string;

  @Column({ name: "CRIADOPOR" })
  criadoPor: string;

  @CreateDateColumn({ name: "CRIADOEM" })
  criadoEm: Date;

  @Column({ name: "ATUALIZADOPOR" })
  atualizadoPor: string;

  @UpdateDateColumn({ name: "ATUALIZADOEM" })
  atualizadoEm: Date;

  @ManyToOne("ApiPortador")
  @JoinColumn({ name: "PORTADORCARTAO_ID" })
  portador: ApiPortador;

  @ManyToMany("ApiCentroCusto")
  @JoinColumn({ name: "CENTROCUSTO_ID" })
  centroCustos: ApiCentroCusto[];

  @ManyToMany("ApiPortadorUnidadeAdministrativa")
  @JoinColumn({ name: "PORTADORCARTAO_UNIDADEADMINISTRATIVA_ID" })
  portadorUnidadeAdministrativas: ApiPortadorUnidadeAdministrativa[];

  @OneToMany("ApiCartao", (cartao: ApiCartao) => cartao.portadorCentroCusto)
  cartoes: ApiCartao[];
}
