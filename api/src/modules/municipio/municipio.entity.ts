import { <PERSON><PERSON><PERSON><PERSON> } from "cpesc-shared";
import { Column, <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn } from "typeorm";

@Entity("CPESC_MUNICIPIO")
export class ApiMunicipio implements Municipio {
  @PrimaryGeneratedColumn({ name: "ID" })
  id: number;

  @Column({ name: "CODIGO" })
  codigoMunicipio: number;

  @Column({ name: "NOME" })
  nomeMunicipio: string;

  @Column({ name: "CODIGOIBGE" })
  codigoIbge: number;
}
