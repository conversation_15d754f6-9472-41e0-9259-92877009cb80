import { Controller, Get, Param } from "@nestjs/common";
import { MunicipioService } from "./municipio.service.js";

@Controller("municipio")
//@UseGuards(AuthGuard)
export class MunicipioController {
  constructor(private readonly municipioService: MunicipioService) {}

  @Get()
  async findAll() {
    return this.municipioService.getMunicipios();
  }

  @Get("/:id")
  async findMunicipioPorId(@Param("id") id: string) {
    return this.municipioService.getMunicipioPorId(+id);
  }
}
