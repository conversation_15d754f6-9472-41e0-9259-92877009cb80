import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { SharedModule } from "../shared/shared.module.js";
import { MunicipioController } from "./municipio.controller.js";
import { ApiMunicipio } from "./municipio.entity.js";
import { MunicipioService } from "./municipio.service.js";

@Module({
  imports: [TypeOrmModule.forFeature([ApiMunicipio]), SharedModule],
  controllers: [MunicipioController],
  providers: [MunicipioService],
  exports: [MunicipioService],
})
export class MunicipioModule {}
