import { jest } from "@jest/globals";
import { Test, type TestingModule } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import { ApiMunicipio } from "./municipio.entity.js";
import { MunicipioService } from "./municipio.service.js";

describe("MunicipioService", () => {
  let service: MunicipioService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [MunicipioService, { provide: getRepositoryToken(ApiMunicipio), useFactory: jest.fn }],
    }).compile();

    service = module.get<MunicipioService>(MunicipioService);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });
});
