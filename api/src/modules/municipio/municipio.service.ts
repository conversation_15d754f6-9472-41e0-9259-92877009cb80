import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { ApiMunicipio } from "./municipio.entity.js";

@Injectable()
export class MunicipioService {
  constructor(@InjectRepository(ApiMunicipio) private municipioRepository: Repository<ApiMunicipio>) {}

  async getMunicipios(): Promise<ApiMunicipio[]> {
    return this.municipioRepository.find();
  }

  async getMunicipioPorId(id: number): Promise<ApiMunicipio | null> {
    return this.municipioRepository.findOneBy({ id });
  }

  async setMunicipio(municipio: ApiMunicipio): Promise<void> {
    await this.municipioRepository.save(municipio);
  }
}
