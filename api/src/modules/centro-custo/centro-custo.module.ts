import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { SharedModule } from "../shared/shared.module.js";
import { CentroCustoController } from "./centro-custo.controller.js";
import { ApiCentroCusto } from "./centro-custo.entity.js";
import { CentroCustoService } from "./centro-custo.service.js";

@Module({
  imports: [TypeOrmModule.forFeature([ApiCentroCusto]), SharedModule],
  controllers: [CentroCustoController],
  providers: [CentroCustoService],
  exports: [CentroCustoService],
})
export class CentroCustoModule {}
