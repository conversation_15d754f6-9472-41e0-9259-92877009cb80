import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { ApiCentroCusto } from "./centro-custo.entity.js";

@Injectable()
export class CentroCustoService {
  constructor(@InjectRepository(ApiCentroCusto) private centroCustoRepository: Repository<ApiCentroCusto>) {}

  async getCentroCustos(): Promise<ApiCentroCusto[]> {
    return this.centroCustoRepository.find();
  }

  async getCentroCustoPorId(id: number): Promise<ApiCentroCusto | null> {
    return this.centroCustoRepository.findOneBy({ id });
  }

  async setCentroCusto(centroCusto: ApiCentroCusto): Promise<void> {
    await this.centroCustoRepository.save(centroCusto);
  }
}
