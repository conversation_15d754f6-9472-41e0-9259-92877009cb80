import { jest } from "@jest/globals";
import { Test, type TestingModule } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import { ApiCentroCusto } from "./centro-custo.entity.js";
import { CentroCustoService } from "./centro-custo.service.js";

describe("CentroCustoService", () => {
  let service: CentroCustoService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [CentroCustoService, { provide: getRepositoryToken(ApiCentroCusto), useFactory: jest.fn }],
    }).compile();

    service = module.get<CentroCustoService>(CentroCustoService);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });
});
