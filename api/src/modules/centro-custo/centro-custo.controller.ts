import { Controller, Get, Param } from "@nestjs/common";
import { CentroCustoService } from "./centro-custo.service.js";

@Controller("centro-custo")
//@UseGuards(AuthGuard)
export class CentroCustoController {
  constructor(private readonly centroCustoService: CentroCustoService) {}

  @Get()
  async findAll() {
    return this.centroCustoService.getCentroCustos();
  }

  @Get("/:id")
  async findCentroCustoPorId(@Param("id") id: string) {
    return this.centroCustoService.getCentroCustoPorId(+id);
  }
}
