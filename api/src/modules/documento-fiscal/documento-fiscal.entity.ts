import { DocumentoFiscal } from "cpesc-shared";
import {
  AfterLoad,
  Column,
  CreateDateColumn,
  <PERSON>tity,
  JoinColumn,
  OneToMany,
  OneToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from "typeorm";
import type { ApiDocumentoFiscalItem } from "../documento-fiscal-item/documento-fiscal-item.entity.js";
import type { ApiMovimentacao } from "../movimentacao/movimentacao.entity.js";

@Entity("CPESC_DOCUMENTOFISCAL")
export class ApiDocumentoFiscal implements DocumentoFiscal {
  @PrimaryGeneratedColumn({ name: "ID" })
  id: number;

  @Column({ name: "MOVIMENTACAO_ID" })
  movimentacaoId: number;

  @Column({ name: "TIPODOCUMENTOFISCAL_ID" })
  tipodocumentofiscalId: number;

  @Column({ name: "NUMERO", nullable: true, type: "number" })
  numero: number | null;

  @Column({ name: "SERIE", nullable: true, type: "number" })
  serie: number | null;

  numeroSerie: string;

  @Column({ name: "CNPJ" })
  cnpj: string;

  @Column({ name: "CHAVE", nullable: true, type: String })
  chave: string | null;

  @Column({ name: "VALOR", type: "decimal", nullable: true })
  valor: number | null;

  @Column({ name: "DESCONTO", nullable: true, type: "decimal" })
  desconto: number | null;

  @Column({ name: "ACRESCIMO", nullable: true, type: "decimal" })
  acrescimo: number | null;

  @Column({ name: "DATAEMISSAO", nullable: true, type: Date })
  dataEmissao: Date | null;

  @Column({ name: "CODIGOSIGEF", nullable: true, type: Number })
  codigoSigef: number | null;

  @Column({ name: "CRIADOPOR" })
  criadoPor: string;

  @CreateDateColumn({ name: "CRIADOEM" })
  criadoEm: Date;

  @Column({ name: "ATUALIZADOPOR" })
  atualizadoPor: string;

  @UpdateDateColumn({ name: "ATUALIZADOEM" })
  atualizadoEm: Date | null;

  @OneToMany("ApiDocumentoFiscalItem", (nfi: ApiDocumentoFiscalItem) => nfi.documentofiscal)
  documentoFiscalItens: ApiDocumentoFiscalItem[] | null;

  @OneToOne("ApiMovimentacao", (movimentacao: ApiMovimentacao) => movimentacao.documentoFiscal)
  @JoinColumn({ name: "MOVIMENTACAO_ID" })
  movimentacao: ApiMovimentacao;

  @AfterLoad()
  generateFields() {
    this.numeroSerie = this.serie ? `${this.numero}-${this.serie}` : `${this.numero}`;
  }
}
