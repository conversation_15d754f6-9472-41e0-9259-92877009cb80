import { jest } from "@jest/globals";
import { BadRequestException } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { JwtService } from "@nestjs/jwt";
import { Test, type TestingModule } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import type { endpoints } from "cpesc-shared/out/endpoints/main.js";
import { DataSource } from "typeorm";
import type z from "zod";
import { TokenService } from "../../helpers/token.service.js";
import { createDataSourceMock } from "../../test-utils/mock-datasource.js";
import { createRepositoryMock } from "../../test-utils/mock-repository.js";
import { getMockRequest } from "../../test-utils/mock-request.js";
import { ApiCartao } from "../cartao/cartao.entity.js";
import { ApiDocumentoFiscalItem } from "../documento-fiscal-item/documento-fiscal-item.entity.js";
import { DocumentoFiscalItemService } from "../documento-fiscal-item/documento-fiscal-item.service.js";
import { ApiItemFiscal } from "../item-fiscal/item-fiscal.entity.js";
import { ItemFiscalService } from "../item-fiscal/item-fiscal.service.js";
import { ApiLimiteCartao } from "../limite-cartao/limite-cartao.entity.js";
import { ApiMovimentacao } from "../movimentacao/movimentacao.entity.js";
import { ApiPortador } from "../portador/portador.entity.js";
import { PortadorService } from "../portador/portador.service.js";
import { EnvService } from "../shared/env.service.js";
import { ApiUnidadeGestora } from "../unidade-gestora/unidade-gestora.entity.js";
import { Usuario } from "../usuario/usuario.entity.js";
import { UsuarioService } from "../usuario/usuario.service.js";
import { DocumentoFiscalController } from "./documento-fiscal.controller.js";
import { ApiDocumentoFiscal } from "./documento-fiscal.entity.js";
import { DocumentoFiscalService } from "./documento-fiscal.service.js";

describe("DocumentoFiscalController", () => {
  let controller: DocumentoFiscalController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [DocumentoFiscalController],
      providers: [
        ConfigService,
        DocumentoFiscalService,
        DocumentoFiscalItemService,
        EnvService,
        ItemFiscalService,
        JwtService,
        PortadorService,
        TokenService,
        UsuarioService,
        { provide: getRepositoryToken(ApiCartao), useFactory: createRepositoryMock },
        { provide: getRepositoryToken(ApiDocumentoFiscal), useFactory: createRepositoryMock },
        { provide: getRepositoryToken(ApiDocumentoFiscalItem), useFactory: createRepositoryMock },
        { provide: getRepositoryToken(ApiItemFiscal), useFactory: createRepositoryMock },
        { provide: getRepositoryToken(ApiLimiteCartao), useFactory: createRepositoryMock },
        { provide: getRepositoryToken(ApiMovimentacao), useFactory: createRepositoryMock },
        { provide: getRepositoryToken(ApiPortador), useFactory: createRepositoryMock },
        { provide: getRepositoryToken(ApiUnidadeGestora), useFactory: createRepositoryMock },
        { provide: getRepositoryToken(Usuario), useFactory: createRepositoryMock },
        { provide: DataSource, useFactory: createDataSourceMock },
      ],
    }).compile();

    controller = module.get(DocumentoFiscalController);
  });

  describe("cria nota fiscal", () => {
    it("retorna sucesso com payload válido", async () => {
      jest.spyOn(DocumentoFiscalService.prototype, "getDocumentoFiscalNoSIGEF").mockResolvedValue([
        {
          numero: 1,
          serie: 1,
          numeroSerie: "1-1",
          cnpj: "12345678000195",
          chave: "abc",
          valor: 100,
          dataEmissao: new Date("2025-01-01T12:00:00Z"),
          codigoSigef: 123,
        },
      ]);

      const json = await controller.create(
        {
          chave: "abc",
          cnpj: "12345678000195",
          codigoSigef: 123,
          dataEmissao: new Date("01/01/2025"),
          movimentacaoId: 1,
          numero: 1,
          serie: 1,
          tipodocumentofiscalId: 1,
          valor: 100,
          desconto: null,
        } satisfies z.infer<typeof endpoints.criarDocumentoFiscal.tipoPayload>,
        getMockRequest(),
      );
      expect(json.sucesso).toBeTruthy();
    }, 120000);

    it("retorna erro com payload inválido", async () => {
      await expect(async () => controller.create({}, getMockRequest())).rejects.toBeInstanceOf(BadRequestException);
    });
  });
});
