import { Modu<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { AuthModule } from "../auth/auth.module.js";
import { DocumentoFiscalItemModule } from "../documento-fiscal-item/documento-fiscal-item.module.js";
import { PortadorModule } from "../portador/portador.module.js";
import { SharedModule } from "../shared/shared.module.js";
import { UsuarioModule } from "../usuario/usuario.module.js";
import { DocumentoFiscalController } from "./documento-fiscal.controller.js";
import { ApiDocumentoFiscal } from "./documento-fiscal.entity.js";
import { DocumentoFiscalService } from "./documento-fiscal.service.js";

@Module({
  imports: [
    TypeOrmModule.forFeature([ApiDocumentoFiscal]),
    AuthModule,
    PortadorModule,
    UsuarioModule,
    SharedModule,
    DocumentoFiscalItemModule,
  ],
  controllers: [DocumentoFiscalController],
  providers: [DocumentoFiscalService],
  exports: [DocumentoFiscalService],
})
export class DocumentoFiscalModule {}
