import {
  BadRequestException,
  Body,
  Controller,
  Get,
  HttpCode,
  HttpException,
  HttpStatus,
  InternalServerErrorException,
  Param,
  Patch,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from "@nestjs/common";
import { DocumentoFiscal } from "cpesc-shared";
import { assertNotNull } from "cpesc-shared/out/assertions.js";
import type {
  PayloadCriarDocFiscalImportacao,
  PayloadCriarDocFiscalManual,
  PayloadCriarReversao,
} from "cpesc-shared/out/endpoints/documento-fiscal.js";
import { endpoints, type RespostaRota } from "cpesc-shared/out/endpoints/main.js";
import type { FindOptionsWhere } from "typeorm";
import { validarPayload } from "../../helpers/validar-payload.js";
import { AuthGuard, type RequisicaoAutenticada } from "../auth/auth.guard.js";
import { ApiDocumentoFiscal } from "./documento-fiscal.entity.js";
import { DocumentoFiscalService } from "./documento-fiscal.service.js";

@Controller("documento-fiscal")
@UseGuards(AuthGuard)
export class DocumentoFiscalController {
  constructor(private readonly documentoFiscalService: DocumentoFiscalService) {}

  @Post("/")
  async create(
    @Body() payload: unknown,
    @Req() req: RequisicaoAutenticada,
  ): RespostaRota<typeof endpoints.criarDocumentoFiscal> {
    try {
      const nota = validarPayload(payload, endpoints.criarDocumentoFiscal);
      const dataEmissao = new Date(nota.dataEmissao);

      if (isNaN(dataEmissao.getTime())) {
        throw new BadRequestException({ sucesso: false, mensagem: "Data Inválida" });
      }

      assertNotNull(req.usuario, "Usuário não autenticado");
      const emailUsuario = req.usuario.email;
      let documentoFiscalGerado: DocumentoFiscal | null;

      switch (nota.tipodocumentofiscalId) {
        case 1: {
          const notaImportacao = nota as PayloadCriarDocFiscalImportacao;

          if (!notaImportacao.codigoSigef) {
            throw new BadRequestException({ sucesso: false, mensagem: "Código SIGEF Inválido" });
          }
          if (!notaImportacao.chave) {
            throw new BadRequestException({ sucesso: false, mensagem: "Documento Fiscal importada requer chave" });
          }

          documentoFiscalGerado = await this.documentoFiscalService.vincularDocumentoFiscalImportacao(
            notaImportacao,
            emailUsuario,
          );
          break;
        }
        case 2:
        case 3:
        case 4: {
          const notaManual = nota as PayloadCriarDocFiscalManual;

          if (!notaManual.documentoFiscalItens || notaManual.documentoFiscalItens.length === 0) {
            throw new BadRequestException({ sucesso: false, mensagem: "Documento Fiscal sem itens válidos" });
          }

          documentoFiscalGerado = await this.documentoFiscalService.criarDocumentoFiscalManualComItens(
            notaManual,
            dataEmissao,
            emailUsuario,
          );
          break;
        }
        default:
          throw new BadRequestException({ sucesso: false, mensagem: "Tipo de Documento Fiscal Inválido" });
      }

      if (!documentoFiscalGerado) {
        throw new InternalServerErrorException({ sucesso: false, mensagem: "Erro ao criar Documento Fiscal" });
      }

      return { sucesso: true, mensagem: "NF criada", documentoFiscalGerado };
    } catch (err) {
      if (err instanceof HttpException) {
        throw err;
      }

      const error = err as Error;
      throw new InternalServerErrorException({
        sucesso: false,
        mensagem: error.message,
      });
    }
  }

  @Post("reversao")
  async createReversao(
    @Body() reversao: PayloadCriarReversao,
    @Req() req: RequisicaoAutenticada,
  ): RespostaRota<typeof endpoints.criarReversao> {
    try {
      assertNotNull(req.usuario, "Usuário não autenticado");
      const emailUsuario = req.usuario.email;
      const dataAtual = new Date();
      const reversaoGerada = await this.documentoFiscalService.salvarReversao(reversao, emailUsuario, dataAtual);
      return {
        sucesso: true,
        mensagem: `Reversão criada: ${reversaoGerada.id}`,
        documentoFiscalGerado: reversaoGerada,
      };
    } catch (err) {
      if (err instanceof HttpException) {
        throw err;
      }

      const error = err as Error;
      throw new InternalServerErrorException({
        sucesso: false,
        mensagem: error.message,
      });
    }
  }

  @Get()
  async findAll(
    @Query() query: FindOptionsWhere<ApiDocumentoFiscal>,
  ): RespostaRota<typeof endpoints.buscarDocumentosFiscais> {
    return this.documentoFiscalService.findAll(query);
  }

  @Get(":id")
  async findBy(@Param("id") id: string) {
    const idNumber = parseInt(id);
    if (isNaN(idNumber)) {
      throw new BadRequestException("ID inválido");
    }
    return this.documentoFiscalService.getPorId(idNumber);
  }

  @Put(":id")
  async update(
    @Req() req: RequisicaoAutenticada,
    @Param("id") id: string,
    @Body() documentoFiscal: Partial<ApiDocumentoFiscal>,
  ): RespostaRota<typeof endpoints.editarDocumentoFiscal> {
    const idNumber = parseInt(id);

    if (isNaN(idNumber)) {
      throw new BadRequestException("ID inválido");
    }

    assertNotNull(req.usuario, "Usuário não autenticado");
    await this.documentoFiscalService.atualizarDocumentoFiscal(idNumber, req.usuario.email, documentoFiscal);

    return { sucesso: true, mensagem: "NF atualizada" };
  }

  @HttpCode(HttpStatus.OK)
  @Get("buscar-externo/:parametro")
  async findDocumentoFiscalNoSIGEF(
    @Param("parametro") param: string,
  ): RespostaRota<typeof endpoints.buscarDocumentosFiscaisExternos> {
    return this.documentoFiscalService.getDocumentoFiscalNoSIGEF(param);
  }

  /* @Patch(":id")
  async remove(@Param("id") id: string) {
    return this.documentoFiscalService.removeDocumentoFiscalEDocumentoFiscalItem(+id);
  } */

  @Patch("reversao/:id")
  async removeReversao(@Param("id") id: string) {
    const resultado = await this.documentoFiscalService.removeReversao(+id);
    return { sucesso: resultado, mensagem: `Reversão ${id} removido com sucesso.` };
  }
}
