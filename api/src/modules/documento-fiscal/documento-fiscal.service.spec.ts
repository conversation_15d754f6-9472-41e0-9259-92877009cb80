import { jest } from "@jest/globals";
import { Test, type TestingModule } from "@nestjs/testing";
import { getRepositoryToken } from "@nestjs/typeorm";
import { DataSource } from "typeorm";
import { createDataSourceMock } from "../../test-utils/mock-datasource.js";
import { ApiDocumentoFiscalItem } from "../documento-fiscal-item/documento-fiscal-item.entity.js";
import { DocumentoFiscalItemService } from "../documento-fiscal-item/documento-fiscal-item.service.js";
import { ApiItemFiscal } from "../item-fiscal/item-fiscal.entity.js";
import { ItemFiscalService } from "../item-fiscal/item-fiscal.service.js";
import { ApiDocumentoFiscal } from "./documento-fiscal.entity.js";
import { DocumentoFiscalService } from "./documento-fiscal.service.js";

describe("DocumentoFiscalService", () => {
  let service: DocumentoFiscalService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ItemFiscalService,
        DocumentoFiscalItemService,
        DocumentoFiscalService,
        { provide: getRepositoryToken(ApiItemFiscal), useFactory: jest.fn },
        { provide: getRepositoryToken(ApiDocumentoFiscal), useFactory: jest.fn },
        { provide: getRepositoryToken(ApiDocumentoFiscalItem), useFactory: jest.fn },
        { provide: DataSource, useFactory: createDataSourceMock },
      ],
    }).compile();

    service = module.get<DocumentoFiscalService>(DocumentoFiscalService);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });
});
