{
  "extends": "../tsconfig-base",
  "references": [{ "path": "../shared" }],
  "include": ["src/**/*", "src/modules/reversao-movimentacao/reversao-movimentacao.controller.ts"],
  "compilerOptions": {
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "allowJs": true,
    "allowSyntheticDefaultImports": true,
    "outDir": "./dist",
    // "baseUrl": "./",
    // "incremental": true,
    "strictNullChecks": true,
    "strictBindCallApply": true,
    "strictPropertyInitialization": false, // Exigido pelas entidades do TypeORM
  }
}
