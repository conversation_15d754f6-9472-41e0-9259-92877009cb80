// @ts-check

import eslint from "@eslint/js";
import stylistic from "@stylistic/eslint-plugin";
import react from "eslint-plugin-react";
import reactHooks from "eslint-plugin-react-hooks";
import tseslint from "typescript-eslint";

/**
 * @type {Record<string, import("eslint").Linter.RuleEntry>}
 */
const rules = {
  "@typescript-eslint/adjacent-overload-signatures": "error",
  "@typescript-eslint/array-type": ["error", { default: "array" }],
  "@typescript-eslint/await-thenable": "error",
  "@typescript-eslint/ban-ts-comment": "error",
  "@typescript-eslint/ban-tslint-comment": "error",
  "@typescript-eslint/class-literal-property-style": "error",
  "class-methods-use-this": "off",
  "@typescript-eslint/class-methods-use-this": [
    "error",
    {
      enforceForClassFields: true,
      ignoreClassesThatImplementAnInterface: true,
      ignoreOverrideMethods: true,
    },
  ],
  "@typescript-eslint/consistent-generic-constructors": "error",
  "@typescript-eslint/consistent-indexed-object-style": "error",
  "@typescript-eslint/consistent-type-assertions": [
    "error",
    {
      assertionStyle: "as",
      objectLiteralTypeAssertions: "never",
    },
  ],
  "@typescript-eslint/consistent-type-definitions": "error",
  "@typescript-eslint/consistent-type-exports": ["error", { fixMixedExportsWithInlineTypeSpecifier: true }],
  "@typescript-eslint/consistent-type-imports": [
    "error",
    {
      disallowTypeAnnotations: true,
      fixStyle: "inline-type-imports",
      prefer: "type-imports",
    },
  ],
  "default-param-last": "off",
  "@typescript-eslint/default-param-last": "error",
  "dot-notation": "off",
  "@typescript-eslint/dot-notation": [
    "error",
    {
      allowKeywords: true,
      allowPrivateClassPropertyAccess: false,
      allowProtectedClassPropertyAccess: false,
      allowIndexSignaturePropertyAccess: false,
    },
  ],
  "@typescript-eslint/explicit-member-accessibility": ["error", { accessibility: "no-public" }],
  "init-declarations": "off",
  "@typescript-eslint/init-declarations": "off",
  "@typescript-eslint/member-ordering": [
    "error",
    {
      default: [
        // Signatures
        "signature",
        "call-signature",

        // Static fields
        "#private-static-field",
        "private-static-field",
        "protected-static-field",
        "public-static-field",

        // Instance fields
        "#private-instance-field",
        "private-instance-field",
        "protected-instance-field",
        "public-instance-field",

        // Constructors
        "public-constructor",
        "protected-constructor",
        "private-constructor",

        // Static methods
        "static-method",

        // Instance methods
        "instance-method",
      ],
    },
  ],
  "@typescript-eslint/method-signature-style": "off",
  "@typescript-eslint/naming-convention": [
    "error",
    {
      selector: "default",
      format: ["camelCase"],
      leadingUnderscore: "forbid",
      trailingUnderscore: "forbid",
    },
    {
      selector: "function",
      format: ["camelCase", "PascalCase"], // PascalCase para componentes React
      leadingUnderscore: "forbid",
      trailingUnderscore: "forbid",
    },
    {
      selector: "enumMember",
      format: ["PascalCase"],
      leadingUnderscore: "forbid",
      trailingUnderscore: "forbid",
    },
    {
      selector: "import",
      format: ["camelCase", "PascalCase"],
      leadingUnderscore: "forbid",
      trailingUnderscore: "forbid",
    },
    {
      selector: "objectLiteralProperty",
      format: null,
      leadingUnderscore: "forbid",
      trailingUnderscore: "forbid",
    },
    {
      selector: "parameter",
      format: ["camelCase"],
      leadingUnderscore: "allow",
      trailingUnderscore: "forbid",
    },
    {
      selector: "typeLike",
      format: ["PascalCase"],
      leadingUnderscore: "forbid",
      trailingUnderscore: "forbid",
    },
    {
      selector: "typeProperty",
      format: null,
      leadingUnderscore: "forbid",
      trailingUnderscore: "forbid",
    },
    {
      selector: "typeProperty",
      format: ["camelCase"],
      leadingUnderscore: "allowSingleOrDouble",
      trailingUnderscore: "forbid",
    },
    {
      selector: "variable",
      format: ["camelCase", "UPPER_CASE"],
      leadingUnderscore: "allow",
      trailingUnderscore: "forbid",
    },
  ],
  "no-array-constructor": "off",
  "@typescript-eslint/no-array-constructor": "error",
  "@typescript-eslint/no-base-to-string": "error",
  "@typescript-eslint/no-confusing-non-null-assertion": "error",
  "@typescript-eslint/no-confusing-void-expression": "off",
  "no-constant-condition": "off",
  "no-dupe-class-members": "off",
  "@typescript-eslint/no-dupe-class-members": "off",
  "@typescript-eslint/no-duplicate-enum-values": "error",
  "@typescript-eslint/no-duplicate-type-constituents": "error",
  "@typescript-eslint/no-dynamic-delete": "error",
  "@typescript-eslint/no-empty-interface": "error",
  "@typescript-eslint/no-empty-object-type": "error",
  "@typescript-eslint/no-explicit-any": "error",
  "@typescript-eslint/no-extra-non-null-assertion": "error",
  "@stylistic/no-extra-semi": "error",
  // "@typescript-eslint/no-extraneous-class": "error",
  "@typescript-eslint/no-floating-promises": "error",
  "@typescript-eslint/no-for-in-array": "error",
  "no-implied-eval": "off",
  "@typescript-eslint/no-implied-eval": "error",
  "@typescript-eslint/no-import-type-side-effects": "error",
  "@typescript-eslint/no-inferrable-types": ["error", { ignoreProperties: true, ignoreParameters: true }],
  "no-invalid-this": "off",
  "@typescript-eslint/no-invalid-this": "off",
  "@typescript-eslint/no-invalid-void-type": ["error", { allowInGenericTypeArguments: ["Promise"] }],
  "no-loop-func": "off",
  "@typescript-eslint/no-loop-func": "off",
  "no-loss-of-precision": "off",
  "@typescript-eslint/no-loss-of-precision": "error",
  "@typescript-eslint/no-meaningless-void-operator": ["error", { checkNever: true }],
  "@typescript-eslint/no-misused-new": "error",
  "@typescript-eslint/no-misused-promises": "error",
  "@typescript-eslint/no-mixed-enums": "error",
  "@typescript-eslint/no-namespace": "error",
  "@typescript-eslint/no-non-null-asserted-nullish-coalescing": "error",
  "@typescript-eslint/no-non-null-asserted-optional-chain": "error",
  "@typescript-eslint/no-non-null-assertion": "error",
  "no-redeclare": "off",
  "@typescript-eslint/no-redeclare": "off",
  "@typescript-eslint/no-redundant-type-constituents": "error",
  "@typescript-eslint/no-require-imports": "off",
  "no-restricted-imports": "off",
  "@typescript-eslint/no-restricted-imports": "off",
  "no-shadow": "off",
  "@typescript-eslint/no-this-alias": "off",
  "@typescript-eslint/no-unnecessary-boolean-literal-compare": "error",
  "@typescript-eslint/no-unnecessary-condition": ["error", { allowConstantLoopConditions: true }],
  "@typescript-eslint/no-unnecessary-qualifier": "error",
  "@typescript-eslint/no-unnecessary-type-arguments": "error",
  "@typescript-eslint/no-unnecessary-type-assertion": "error",
  "@typescript-eslint/no-unnecessary-type-constraint": "error",
  "@typescript-eslint/no-unsafe-argument": "error",
  "@typescript-eslint/no-unsafe-assignment": "error",
  "@typescript-eslint/no-unsafe-call": "error",
  "@typescript-eslint/no-unsafe-declaration-merging": "error",
  "@typescript-eslint/no-unsafe-enum-comparison": "error",
  "@typescript-eslint/no-unsafe-function-type": "error",
  "@typescript-eslint/no-unsafe-member-access": "error",
  "@typescript-eslint/no-unsafe-return": "error",
  "@typescript-eslint/no-unsafe-unary-minus": "error",
  "no-unused-expressions": "off",
  "@typescript-eslint/no-unused-expressions": "error",
  "no-unused-vars": "off",
  "@typescript-eslint/no-unused-vars": [
    "error",
    {
      args: "after-used",
      argsIgnorePattern: "^_",
      caughtErrors: "all",
      caughtErrorsIgnorePattern: "^_",
      destructuredArrayIgnorePattern: "^_",
      ignoreRestSiblings: true,
      vars: "all",
      varsIgnorePattern: "^_",
    },
  ],
  "no-use-before-define": "off",
  "@typescript-eslint/no-use-before-define": [
    "error",
    {
      allowNamedExports: false,
      classes: true,
      enums: true,
      functions: false,
      typedefs: true,
      ignoreTypeReferences: true,
      variables: false,
    },
  ],
  "no-useless-constructor": "off",
  "@typescript-eslint/no-useless-constructor": "error",
  "@typescript-eslint/no-useless-empty-export": "error",
  "@typescript-eslint/no-var-requires": "error",
  "@typescript-eslint/no-wrapper-object-types": "error",
  "@typescript-eslint/non-nullable-type-assertion-style": "error",
  "object-shorthand": "error",
  "@typescript-eslint/only-throw-error": "error",
  "@typescript-eslint/parameter-properties": ["error", { prefer: "parameter-property" }],
  "@typescript-eslint/prefer-as-const": "error",
  "prefer-destructuring": "off",
  "@typescript-eslint/prefer-destructuring": "off",
  "@typescript-eslint/prefer-enum-initializers": "off",
  "@typescript-eslint/prefer-for-of": "error",
  "@typescript-eslint/prefer-function-type": "off",
  "@typescript-eslint/prefer-includes": "error",
  "@typescript-eslint/prefer-literal-enum-member": "error",
  "@typescript-eslint/prefer-namespace-keyword": "error",
  "@typescript-eslint/prefer-nullish-coalescing": "error",
  "@typescript-eslint/prefer-optional-chain": "error",
  "@typescript-eslint/prefer-readonly": "off",
  "@typescript-eslint/prefer-readonly-parameter-types": "off",
  "@typescript-eslint/prefer-reduce-type-parameter": "error",
  "@typescript-eslint/prefer-regexp-exec": "error",
  "@typescript-eslint/prefer-return-this-type": "error",
  "@typescript-eslint/prefer-string-starts-ends-with": "error",
  "@typescript-eslint/prefer-ts-expect-error": "error",
  "@typescript-eslint/promise-function-async": "error",
  "@stylistic/quotes": [
    "error",
    "double",
    {
      avoidEscape: true,
    },
  ],
  "@typescript-eslint/require-array-sort-compare": "error",
  "require-await": "off",
  "@typescript-eslint/require-await": "error",
  "@typescript-eslint/restrict-plus-operands": "error",
  "@typescript-eslint/restrict-template-expressions": "off",
  "no-return-await": "off",
  "@typescript-eslint/return-await": "error",
  "@typescript-eslint/sort-type-constituents": "off",
  // "@typescript-eslint/strict-boolean-expressions": [
  //     "error",
  //     {
  //         allowAny: false,
  //         allowNullableBoolean: true,
  //         allowNullableEnum: false,
  //         allowNullableNumber: false,
  //         allowNullableObject: false,
  //         allowNullableString: false,
  //         allowNumber: false,
  //         allowRuleToRunWithoutStrictNullChecksIKnowWhatIAmDoing: false,
  //         allowString: false,
  //     },
  // ],
  "@typescript-eslint/switch-exhaustiveness-check": "error",
  "@typescript-eslint/triple-slash-reference": [
    "error",
    {
      lib: "never",
      path: "never",
      types: "never",
    },
  ],
  "@typescript-eslint/typedef": "off",
  "@typescript-eslint/unbound-method": "error",
  "@typescript-eslint/unified-signatures": "error",
};

export default tseslint.config(
  {
    files: ["frontend/**/*.{ts,tsx}"],
    ignores: ["**/*.d.ts", "frontend/vite.config.ts"],
    plugins: { "@stylistic": stylistic, react },
    languageOptions: {
      parserOptions: {
        project: "frontend/tsconfig.json",
      },
    },
    linterOptions: {
      reportUnusedDisableDirectives: "warn",
    },
    extends: [eslint.configs.recommended, ...tseslint.configs.recommended, reactHooks.configs["recommended-latest"]],
    rules: {
      ...rules,
      // Permitido pois a regra rejeita componentes não-async que retornem ReactNode
      "@typescript-eslint/promise-function-async": "off",
    },
  },
  {
    files: ["api/**/*.{ts,tsx}"],
    ignores: ["**/*.d.ts", "**/*.js", "api/test/app.e2e-spec.ts"],
    plugins: { "@stylistic": stylistic },
    languageOptions: {
      parserOptions: {
        project: "api/tsconfig.json",
      },
    },
    linterOptions: {
      reportUnusedDisableDirectives: "warn",
    },
    extends: [eslint.configs.recommended, ...tseslint.configs.recommended],
    rules,
  },
);
