export const SEPARADOR_NCM: string = " - "; // Separador de ncm e descrição... tambem usado para extrair NCM de uma descricao

export const MIN_BUSCA: number = 3; // Quantidade mínima de caracteres para iniciar a busca de ncm na base e liberar para salvar

export type PayloadLogin = PayloadLoginSau | PayloadLoginOtp;

export interface PayloadLoginSau {
  tipo: "sau";
  rota: string;
  ticket: string;
}

export interface PayloadLoginOtp {
  tipo: "otp";
  email: string;
  codigo: string;
}

export type TipoLogin = PayloadLogin["tipo"];

export interface RetornoLogin {
  credenciais: UsuarioAutenticado;
}

export interface RetornoLogout {
  message: string;
}

export interface RetornoCredenciais {
  credenciais: UsuarioAutenticado | null;
}

export interface UsuarioAutenticado {
  id: number;
  perfil: Perfil;
  nome: string;
  email: string;
  ipAddress: string | null;
  tipoLogin: TipoLogin;
  rotaInicial: string;
}
export enum Perfil {
  Portador,
  GestorSed,
  Gestor,
  Consulta,
  AdministradorCpesc,
  AdministradorCiasc,
}

export enum HttpStatus {
  CONTINUE = 100,
  OK = 200,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  NOT_ACCEPTABLE = 406,
  INTERNAL_SERVER_ERROR = 500,
}
export interface CreditoCartao {
  id: number;
  dataCredito: Date;
  dataVencimento: Date;
  preparacaopagamento: string;
  notaLancamento: string;
  valorCredito: number;
  fimLimiteCartao: FimLimiteCartao | null;
  cartao: Cartao;
  subelemento: Subelemento;
  movimentacoes?: Movimentacao[];
  dataInicioMovimentacao: Date;
  dataLimiteMovimentacao: Date;
  dataZeramento: Date | null;
  temMaisLimites?: boolean;
}

export interface Credito extends CreditoCartao {
  notaEmpenho: string;
  ordemBancaria: string;
  fonteRecurso: string;
}

export interface UnidadeGestora {
  id: number;
  nome: string;
  codigo: number;
  descricao: string;
  cnpj: string;
  nomeOrdenador: string;
  siglaSgpe: string;
  contaBanco: ContaBanco;
}

export interface ContaBanco {
  conta: string;
  digitoConta: string;
}

export interface Portador {
  id: number;
  nome: string;
  nomeAbreviado: string;
  cpfOfuscado: string;
  email: string;
}

export interface Subelemento {
  id: number;
  subelemento: string;
  nome: string;
}

export interface FimLimiteCartao {
  id: number;
  situacao: SituacaoFimLimiteCartao;
  anoSgpe: string;
  orgaoSgpe: string;
  processoSgpe: string;
  valorGasto: number;
  limiteCartaoId: number;
}

export interface HistoricoFimLimiteCartao {
  id: number;
  fimLimiteCartaoId: number;
  motivo: string;
}

export enum SituacaoFimLimiteCartao {
  Pendente = "P",
  Finalizado = "F",
  Edicao = "E",
}

export interface Cartao {
  id: number;
  numero: string;
  nuContaCartao: number;
  ativo: string;
  portadorUnidadeAdministrativa: PortadorUnidadeAdministrativa;
  movimentacoes: Movimentacao[];
}

export interface PortadorUnidadeAdministrativa {
  id: number;
  matricula: string;
  ativo: string;
  portador: Portador;
  unidadeAdministrativa: UnidadeAdministrativa;
}

export interface Movimentacao {
  id: number;
  numeroCartao: number;
  nomeEstabelecimento: string;
  CNPJEstabelecimento: string;
  cidadeEstabelecimento: string;
  ufEstabelecimento: string;
  valorTransacaoReal: number;
  dataTransacao: Date;
  horaTransacao: string;
  codigoTransacaoBB: number;
  descricaoTransacao: string;
  documentoFiscal: DocumentoFiscal | null;
}

export interface UnidadeAdministrativa {
  id: number;
  nome: string;
  codigo: number;
  municipio: Municipio;
  unidadeGestora: UnidadeGestora;
}

export interface DocumentoFiscal {
  id: number;
  movimentacaoId: number;
  tipodocumentofiscalId: TipoDocumentoFiscal;
  numero: number | null;
  numeroSerie: string;
  serie: number | null;
  cnpj: string | null;
  chave: string | null;
  dataEmissao: Date | null;
  valor: number | null;
  desconto: number | null;
  acrescimo: number | null;
  documentoFiscalItens: DocumentoFiscalItem[] | null;
  codigoSigef: number | null;
  criadoPor?: string;
  atualizadoPor?: string;
  criadoEm?: Date | null;
  atualizadoEm?: Date | null;
}

export enum TipoDocumentoFiscal {
  notaFiscalEletronicaImportacao = 1,
  notaFiscalEletronicaManual = 2,
  notaServicoManual = 3,
  cupomFiscalManual = 4,
  reversao = 5,
}
export enum TipoCompra {
  padrao = 1,
  emergencial = 2,
  licitacaodeserta = 3,
}

export interface DocumentoFiscalSigef {
  numero: number;
  serie: number;
  numeroSerie: string;
  cnpj: string;
  chave: string | null;
  valor: number;
  dataEmissao: Date;
  codigoSigef: number | null;
}

export interface DocumentoFiscalItem {
  id: number;
  documentoFiscalId: number;
  ncm: string;
  descricao: string;
  quantidade: number;
  unidade: string;
  valorunitario: number | null;
  valor: number;
  tipocompraId: TipoCompra;
  processotipocompra: string;
}

export interface ItemFiscal {
  id: number | null;
  ncm: string | null;
  descricao: string;
  descricaoLonga: string;
  tipo: string;
  pesquisa: string;
}

export interface Municipio {
  id: number;
  nomeMunicipio: string;
}

const tamanhoCNPJSemDV: number = 12;
const regexCNPJSemDV: RegExp = /^([A-Z\d]){12}$/;
const regexCNPJ: RegExp = /^([A-Z\d]){12}(\d){2}$/;
const regexCaracteresMascara: RegExp = /[./-]/g;
const regexCaracteresNaoPermitidos: RegExp = /[^A-Z\d./-]/i;
const valorBase: number = "0".charCodeAt(0);
const pesosDV: number[] = [6, 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];
const cnpjZerado: string = "00000000000000";

//Funçao que recebe uma string em portugues do Brasil e remove acentos e caracteres especiais entre outros
export function limparTexto(texto: string): string {
  return texto
    .normalize("NFD")
    .replace(/[\u0300-\u036f]/g, "")
    .replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g, "");
}

export function validarCnpj(cnpj: string): boolean {
  if (!regexCaracteresNaoPermitidos.test(cnpj)) {
    let cnpjSemMascara = removeMascaraCNPJ(cnpj);
    if (regexCNPJ.test(cnpjSemMascara) && cnpjSemMascara !== cnpjZerado) {
      const dvInformado = cnpjSemMascara.substring(tamanhoCNPJSemDV);
      const dvCalculado = calculaDV(cnpjSemMascara.substring(0, tamanhoCNPJSemDV));
      return dvInformado === dvCalculado;
    }
  }
  return false;
}

function calculaDV(cnpj: string): string {
  if (!regexCaracteresNaoPermitidos.test(cnpj)) {
    let cnpjSemMascara = removeMascaraCNPJ(cnpj);
    if (regexCNPJSemDV.test(cnpjSemMascara) && cnpjSemMascara !== cnpjZerado.substring(0, tamanhoCNPJSemDV)) {
      let somatorioDV1 = 0;
      let somatorioDV2 = 0;
      for (let i = 0; i < tamanhoCNPJSemDV; i++) {
        const asciiDigito = cnpjSemMascara.charCodeAt(i) - valorBase;
        somatorioDV1 += asciiDigito * pesosDV[i + 1];
        somatorioDV2 += asciiDigito * pesosDV[i];
      }
      const dv1 = somatorioDV1 % 11 < 2 ? 0 : 11 - (somatorioDV1 % 11);
      somatorioDV2 += dv1 * pesosDV[tamanhoCNPJSemDV];
      const dv2 = somatorioDV2 % 11 < 2 ? 0 : 11 - (somatorioDV2 % 11);
      return `${dv1}${dv2}`;
    }
  }
  throw new Error("Não é possível calcular o DV pois o CNPJ fornecido é inválido");
}

function removeMascaraCNPJ(cnpj: string): string {
  return cnpj.replace(regexCaracteresMascara, "");
}

/** Funcao que compara duas datas e retorna true se a primeira for maior que a segunda  **/
export function testaDataMaior(data1: Date, data2: Date): boolean {
  return (
    data1.getFullYear() > data2.getFullYear() &&
    data1.getMonth() > data2.getMonth() &&
    data1.getDate() > data2.getDate()
  );
}

export interface GastosPorNCM {
  id: string;
  ncm: string;
  descricao: string;
  quantidade: number;
  valor: number;
  tipo: string;
  dataTransacao: Date;
  unidadegestoraId: number;
  nmunidadegestora: string;
}

export interface TiposCompra {
  name: string;
  key: TipoCompra;
  label?: string;
}

export const tiposCompraPermitida: TiposCompra[] = [
  { name: "Padrão", key: TipoCompra.padrao, label: "P" },
  { name: "Emergencial", key: TipoCompra.emergencial, label: "E" },
  { name: "Licitação Deserta/Frustrada", key: TipoCompra.licitacaodeserta, label: "D" },
];
