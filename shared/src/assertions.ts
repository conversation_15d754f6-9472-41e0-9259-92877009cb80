export function switchExaustivo(valor: never): never {
  throw Error("Switch não-exaustivo (valor: " + JSON.stringify(valor) + ")");
}

export function assertNotUndefined<T>(value: T | undefined, message?: string): asserts value is T {
  assert(value !== undefined, message ?? 'Valor "undefined" inesperado');
}

export function assertNotNull<T>(value: T | null, message?: string): asserts value is T {
  assert(value !== null, message ?? "Valor nulo inesperado");
}

export function assert(condition: boolean, message?: string): asserts condition {
  if (!condition) {
    const err = new Error("Asser<PERSON> falhou. " + (message ?? "Condição falsa inesperada"));

    throw err;
  }
}
