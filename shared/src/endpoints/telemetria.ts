import { z, ZodType } from "zod";
import { MetodoHttp, Rota } from "./main.js";

export enum TipoEventoTelemetria {
  AcessoRota,
  Consulta,
}

export type EventoTelemetria<T extends TipoEventoTelemetria = TipoEventoTelemetria> =
  T extends TipoEventoTelemetria.AcessoRota
    ? { tipo: T; url: string }
    : { tipo: T; url: string; rotaApi: string; verboHttp: MetodoHttp };

export interface EventoTelemetriaResposta {
  statusCode: number;
  tempoExecucaoMs: number;
}

export const telemetriaEventoAcessoRotaSchema: ZodType<EventoTelemetria<TipoEventoTelemetria.AcessoRota>> = z.object({
  tipo: z.literal(TipoEventoTelemetria.AcessoRota),
  url: z.string(),
});

export const telemetriaEventoConsultaSchema: ZodType<EventoTelemetria<TipoEventoTelemetria.Consulta>> = z.object({
  tipo: z.literal(TipoEventoTelemetria.Consulta),
  url: z.string(),
  rotaApi: z.string(),
  verboHttp: z.enum(["get", "post", "put", "delete", "patch", "options", "head"]),
});

export const eventoTelemetriaRespostaSchema: ZodType<EventoTelemetriaResposta> = z.object({
  statusCode: z.number().int(),
  tempoExecucaoMs: z.number(),
});

export const endpointsTelemetria = {
  registrarTelemetria: {
    uri: "/telemetria",
    metodo: "post",
    tipoPayload: z.union([telemetriaEventoAcessoRotaSchema, telemetriaEventoConsultaSchema]),
    tipoResposta: z.number(),
  },
  registrarTelemetriaResposta: {
    uri: "/telemetria/:id",
    metodo: "post",
    tipoPayload: eventoTelemetriaRespostaSchema,
    tipoResposta: z.undefined(),
  },
  usuariosMaisAtivos: {
    uri: "/telemetria/dashboard/usuarios-mais-ativos",
    metodo: "get",
    tipoPayload: z.undefined(),
    tipoResposta: z.array(
      z.object({
        usuarioId: z.number(),
        nome: z.string(),
        totalAcessos: z.number(),
      }),
    ),
  },
  rotasMaisAcessadas: {
    uri: "/telemetria/dashboard/rotas-mais-acessadas",
    metodo: "get",
    tipoPayload: z.undefined(),
    tipoResposta: z.array(
      z.object({
        rota: z.string(),
        totalAcessos: z.number(),
      }),
    ),
  },
  rotasMaisDemoradas: {
    uri: "/telemetria/dashboard/rotas-mais-demoradas",
    metodo: "get",
    tipoPayload: z.undefined(),
    tipoResposta: z.array(
      z.object({
        rota: z.string(),
        tempoMedio: z.number(),
        totalConsultas: z.number(),
      }),
    ),
  },
  estatisticasGerais: {
    uri: "/telemetria/dashboard/estatisticas-gerais",
    metodo: "get",
    tipoPayload: z.undefined(),
    tipoResposta: z.object({
      totalEventos: z.number(),
      totalUsuarios: z.number(),
    }),
  },
} as const satisfies Record<string, Rota>;
