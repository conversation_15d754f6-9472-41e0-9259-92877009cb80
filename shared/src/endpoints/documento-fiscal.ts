import { z, ZodType } from "zod";
import { DocumentoFiscal, DocumentoFiscalSigef, TipoDocumentoFiscal } from "../index.js";
import { documentoFiscalItemSchema, payloadCriarDocFiscalItemSchema } from "./documento-fiscal-item.js";
import type { Rota } from "./main.js";

export const payloadCriarDocFiscalImportacaoSchema = z.object({
  chave: z.string().nullable(),
  cnpj: z.string(),
  codigoSigef: z.number(),
  dataEmissao: z.date(),
  movimentacaoId: z.number(),
  numero: z.number(),
  serie: z.number(),
  tipodocumentofiscalId: z.number(),
  valor: z.number().nullable(),
});

export const payloadCriarDocFiscalManualSchema = z.object({
  movimentacaoId: z.number(),
  tipodocumentofiscalId: z.number(),
  numero: z.number(),
  serie: z.number().nullable(),
  cnpj: z.string(),
  valor: z.number(),
  desconto: z.number().nullable(),
  acrescimo: z.number().nullable(),
  dataEmissao: z.date(),
  codigoSigef: z.number().nullable(),
  documentoFiscalItens: z.array(payloadCriarDocFiscalItemSchema).nullable(),
});

export const documentoFiscalSchema: ZodType<DocumentoFiscal> = z.object({
  id: z.number(),
  movimentacaoId: z.number(),
  tipodocumentofiscalId: z.union([
    z.literal(TipoDocumentoFiscal.notaFiscalEletronicaImportacao),
    z.literal(TipoDocumentoFiscal.notaFiscalEletronicaManual),
    z.literal(TipoDocumentoFiscal.notaServicoManual),
    z.literal(TipoDocumentoFiscal.cupomFiscalManual),
    z.literal(TipoDocumentoFiscal.reversao),
  ]),
  numero: z.number().nullable(),
  numeroSerie: z.string(),
  serie: z.number().nullable(),
  cnpj: z.string().nullable(),
  chave: z.string().nullable(),
  dataEmissao: z.date().nullable(),
  valor: z.number().nullable(),
  desconto: z.number().nullable(),
  acrescimo: z.number().nullable(),
  documentoFiscalItens: z.array(documentoFiscalItemSchema).nullable(),
  codigoSigef: z.number().nullable(),
  criadoPor: z.string().optional(),
  atualizadoPor: z.string().optional(),
});

export const documentoFiscalSigefSchema: ZodType<DocumentoFiscalSigef> = z.object({
  numero: z.number(),
  serie: z.number(),
  numeroSerie: z.string(),
  cnpj: z.string(),
  chave: z.string().nullable(),
  valor: z.number(),
  dataEmissao: z.date(),
  codigoSigef: z.number().nullable(),
});

export const payloadCriarReversaoSchema = z.object({
  movimentacaoId: z.number(),
  numero: z.number().nullable(),
  dataEmissao: z.date().nullable(),
});

export const payloadCriarDocFiscalSchema = z.union([
  payloadCriarDocFiscalImportacaoSchema,
  payloadCriarDocFiscalManualSchema,
]);
export type PayloadCriarDocFiscal = z.infer<typeof payloadCriarDocFiscalSchema>;
export type PayloadCriarDocFiscalImportacao = z.infer<typeof payloadCriarDocFiscalImportacaoSchema>;
export type PayloadCriarDocFiscalManual = z.infer<typeof payloadCriarDocFiscalManualSchema>;
export type PayloadCriarReversao = z.infer<typeof payloadCriarReversaoSchema>;

export const endpointsDocumentoFiscal = {
  criarDocumentoFiscal: {
    uri: "/documento-fiscal",
    metodo: "post",
    tipoPayload: payloadCriarDocFiscalSchema,
    tipoResposta: z.object({
      sucesso: z.boolean(),
      mensagem: z.string(),
      documentoFiscalGerado: documentoFiscalSchema.nullable(),
    }),
  },
  buscarDocumentosFiscais: {
    uri: "/documento-fiscal",
    metodo: "get",
    tipoPayload: z.undefined(),
    tipoResposta: z.array(documentoFiscalSchema),
  },
  buscarDocumentosFiscaisExternos: {
    uri: "/documento-fiscal/buscar-externo/:parametro",
    metodo: "get",
    tipoPayload: z.undefined(),
    tipoResposta: z.array(documentoFiscalSigefSchema),
  },
  editarDocumentoFiscal: {
    uri: "/documento-fiscal/:id",
    metodo: "put",
    tipoPayload: documentoFiscalSchema,
    tipoResposta: z.object({
      sucesso: z.boolean(),
      mensagem: z.string(),
    }),
  },
  criarReversao: {
    uri: "/documento-fiscal/reversao",
    metodo: "post",
    tipoPayload: payloadCriarReversaoSchema,
    tipoResposta: z.object({
      sucesso: z.boolean(),
      mensagem: z.string(),
      documentoFiscalGerado: documentoFiscalSchema.nullable(),
    }),
  },
  excluirReversao: {
    uri: "/documento-fiscal/reversao/:id",
    metodo: "patch",
    tipoPayload: z.undefined(),
    tipoResposta: z.object({
      sucesso: z.boolean(),
      mensagem: z.string(),
    }),
  },
} as const satisfies Record<string, Rota>;
