import { z, ZodType } from "zod";
import { ItemFiscal } from "../index.js";
import type { Rota } from "./main.js";

export const itemFiscalSchema: ZodType<ItemFiscal> = z.object({
  id: z.number().nullable(),
  ncm: z.string().nullable(),
  descricao: z.string(),
  descricaoLonga: z.string(),
  tipo: z.string(),
  pesquisa: z.string(),
});

export const endpointsItemFiscal = {
  buscarItensFiscais: {
    uri: "/item-fiscal",
    metodo: "get",
    tipoPayload: z.undefined(),
    tipoResposta: z.array(itemFiscalSchema),
  },
  buscarItensFiscaisPorNcmETipoGasto: {
    uri: "/item-fiscal/:ncm/:tipoGasto",
    metodo: "get",
    tipoPayload: z.undefined(),
    tipoResposta: z.object({
      items: z.array(itemFiscalSchema),
      total: z.number(),
    }),
  },
} as const satisfies Record<string, Rota>;
