import { z, ZodType } from "zod";
import { DocumentoFiscalItem, TipoCompra } from "../index.js";
import { Rota } from "./main.js";

export const payloadCriarItemFiscalSchema = z.object({
  id: z.number(),
  ncm: z.string(),
  descricao: z.string(),
  tipo: z.string(),
  pesquisa: z.string(),
});

export const payloadCriarDocFiscalItemSchema = z.object({
  documentoFiscalId: z.number(),
  ncm: z.string(),
  descricao: z.string(),
  unidade: z.string(),
  quantidade: z.number(),
  valor: z.number(),
  valorunitario: z.number().nullable(),
  tipocompraId: z.union([
    z.literal(TipoCompra.padrao),
    z.literal(TipoCompra.emergencial),
    z.literal(TipoCompra.licitacaodeserta),
  ]),
  processotipocompra: z.string(),
});

export const documentoFiscalItemSchema: ZodType<DocumentoFiscalItem> = z.object({
  id: z.number(),
  documentoFiscalId: z.number(),
  tipocompraId: z.union([
    z.literal(TipoCompra.padrao),
    z.literal(TipoCompra.emergencial),
    z.literal(TipoCompra.licitacaodeserta),
  ]),
  processotipocompra: z.string(),
  ncm: z.string(),
  descricao: z.string(),
  quantidade: z.number(),
  unidade: z.string(),
  valorunitario: z.number().nullable(),
  valor: z.number(),
});

export type PayloadCriarDocFiscalItem = z.infer<typeof payloadCriarDocFiscalItemSchema>;

export const endpointsDocumentoFiscalItem = {} as const satisfies Record<string, Rota>;
