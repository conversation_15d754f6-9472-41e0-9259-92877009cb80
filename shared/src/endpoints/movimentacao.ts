import { z, ZodType } from "zod";
import { Movimentacao } from "../index.js";
import { documentoFiscalSchema } from "./documento-fiscal.js";
import { Rota } from "./main.js";

export const movimentacaoSchema: ZodType<Movimentacao> = z.object({
  id: z.number(),
  numeroCartao: z.number(),
  nomeEstabelecimento: z.string(),
  CNPJEstabelecimento: z.string(),
  cidadeEstabelecimento: z.string(),
  ufEstabelecimento: z.string(),
  valorTransacaoReal: z.number(),
  dataTransacao: z.date(),
  horaTransacao: z.string(),
  indicativoDebitoCredito: z.string(),
  codigoTransacaoBB: z.number(),
  descricaoTransacao: z.string(),
  documentoFiscal: z.nullable(documentoFiscalSchema),
});

export const endpointsMovimentacao = {
  excluirVinculo: {
    uri: "/movimentacao/:id",
    metodo: "patch",
    tipoPayload: z.undefined(),
    tipoResposta: z.object({
      sucesso: z.boolean(),
      mensagem: z.string(),
    }),
  },
  associarLimiteMovimentacao: {
    uri: "/movimentacao/associar-limite",
    metodo: "patch",
    tipoPayload: z.object({
      movimentacaoIds: z.array(z.number()),
      limiteCartaoId: z.number(),
    }),
    tipoResposta: z.object({
      sucesso: z.boolean(),
    }),
  },
} as const satisfies Record<string, Rota>;
