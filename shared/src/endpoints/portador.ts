import { z, ZodType } from "zod";
import { Portador } from "../index.js";
import { Rota } from "./main.js";

export const portadorSchema: ZodType<Portador> = z.object({
  id: z.number(),
  nome: z.string(),
  nomeAbreviado: z.string(),
  cpfOfuscado: z.string(),
  email: z.string(),
});

export const endpointsPortador = {
  buscarPortadoresPorUG: {
    uri: "/portador/unidade-gestora/:id",
    metodo: "get",
    tipoPayload: z.undefined(),
    tipoResposta: z.array(portadorSchema),
  },
} as const satisfies Record<string, Rota>;
