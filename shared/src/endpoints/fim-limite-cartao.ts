import { z, ZodType } from "zod";
import { FimLimiteCartao, SituacaoFimLimiteCartao } from "../index.js";
import { Rota } from "./main.js";

export const fimLimiteCartaoSchema: ZodType<FimLimiteCartao> = z.object({
  id: z.number(),
  situacao: z.enum(SituacaoFimLimiteCartao),
  anoSgpe: z.string(),
  orgaoSgpe: z.string(),
  processoSgpe: z.string(),
  valorGasto: z.number(),
  limiteCartaoId: z.number(),
});

export const endpointsFimLimiteCartao = {
  criarFimLimiteCartao: {
    uri: "/fim-limite-cartao",
    metodo: "post",
    tipoPayload: z.object({
      limiteCartaoId: z.number(),
      valorGasto: z.number(),
      orgaoSgpe: z.string(),
      processoSgpe: z.string(),
      anoSgpe: z.string(),
      fimLimiteCartaoId: z.number().optional(),
    }),
    tipoResposta: fimLimiteCartaoSchema,
  },
  liberarEdicaoFimLimiteCartao: {
    uri: "/fim-limite-cartao/liberar-edicao",
    metodo: "patch",
    tipoPayload: z.object({
      fimLimiteCartaoId: z.number(),
      motivo: z.string(),
    }),
    tipoResposta: z.boolean(),
  },
} as const satisfies Record<string, Rota>;
