import { z, ZodType } from "zod";
import { Cartao, Credito, CreditoCartao, PortadorUnidadeAdministrativa, Subelemento } from "../index.js";
import { fimLimiteCartaoSchema } from "./fim-limite-cartao.js";
import type { Rota } from "./main.js";
import { movimentacaoSchema } from "./movimentacao.js";
import { portadorSchema } from "./portador.js";
import { unidadeAdministrativaSchema } from "./unidade-administrativa.js";

export const portadorUnidadeAdministrativaSchema: ZodType<PortadorUnidadeAdministrativa> = z.object({
  id: z.number(),
  matricula: z.string(),
  ativo: z.string(),
  portador: portadorSchema,
  unidadeAdministrativa: unidadeAdministrativaSchema,
});

export const cartaoSchema: ZodType<Cartao> = z.object({
  id: z.number(),
  numero: z.string(),
  nuContaCartao: z.number(),
  ativo: z.string(),
  portadorUnidadeAdministrativa: portadorUnidadeAdministrativaSchema,
  movimentacoes: z.array(movimentacaoSchema),
});

export const subelementoSchema: ZodType<Subelemento> = z.object({
  id: z.number(),
  subelemento: z.string(),
  nome: z.string(),
});

export const creditoCartaoSchema = z.object({
  id: z.number(),
  dataCredito: z.date(),
  dataVencimento: z.date(),
  preparacaopagamento: z.string(),
  notaLancamento: z.string(),
  valorCredito: z.number(),
  fimLimiteCartao: fimLimiteCartaoSchema,
  cartao: cartaoSchema,
  subelemento: subelementoSchema,
  movimentacoes: z.array(movimentacaoSchema).optional(),
  dataInicioMovimentacao: z.date(),
  dataLimiteMovimentacao: z.date(),
  dataZeramento: z.date().nullable(),
  temMaisLimites: z.boolean().optional(),
}) satisfies ZodType<CreditoCartao>;

export const creditoSchema: ZodType<Credito> = z.object({
  ...creditoCartaoSchema.shape,
  notaEmpenho: z.string(),
  ordemBancaria: z.string(),
  fonteRecurso: z.string(),
});

export const endpointsLimiteCartao = {
  buscarGastosCartao: {
    uri: "/limite-cartao/gastos/:idCredito",
    metodo: "get",
    tipoPayload: z.undefined(),
    tipoResposta: creditoSchema,
  },
  buscarSelecaoCreditoPorPortador: {
    uri: "/limite-cartao/portador/:id",
    metodo: "get",
    tipoPayload: z.undefined(),
    tipoResposta: z.array(creditoSchema),
  },
  buscarSelecaoCreditoPorPortadorUA: {
    uri: "/limite-cartao/portador/:idPortador/ua/:idUa",
    metodo: "get",
    tipoPayload: z.undefined(),
    tipoResposta: z.array(creditoSchema),
  },
  finalizarLimiteCartao: {
    uri: "/limite-cartao/:idCredito/finalizar",
    metodo: "patch",
    tipoPayload: z.undefined(),
    tipoResposta: z.object({
      sucesso: z.boolean(),
    }),
  },
} as const satisfies Record<string, Rota>;
