import { z, ZodType } from "zod";
import { Municipio, UnidadeAdministrativa } from "../index.js";
import { Rota } from "./main.js";
import { unidadeGestoraSchema } from "./unidade-gestora.js";

export const municipioSchema: ZodType<Municipio> = z.object({
  id: z.number(),
  nomeMunicipio: z.string(),
});

export const unidadeAdministrativaSchema: ZodType<UnidadeAdministrativa> = z.object({
  id: z.number(),
  nome: z.string(),
  codigo: z.number(),
  municipio: municipioSchema,
  unidadeGestora: unidadeGestoraSchema,
});

export const endpointsUnidadeAdministrativa = {
  buscarUAsPorPortador: {
    uri: "/unidade-administrativa/portador/:id",
    metodo: "get",
    tipoPayload: z.undefined(),
    tipoResposta: z.array(unidadeAdministrativaSchema),
  },

  buscarUAsPorPortadorUG: {
    uri: "/unidade-administrativa/portador/:idPortador/ug/:idUg",
    metodo: "get",
    tipoPayload: z.undefined(),
    tipoResposta: z.array(unidadeAdministrativaSchema),
  },
} as const satisfies Record<string, Rota>;
