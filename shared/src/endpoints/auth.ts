import { z, ZodType } from "zod";
import {
  PayloadLoginOtp,
  PayloadLoginSau,
  Perfil,
  RetornoCredenciais,
  RetornoLogin,
  RetornoLogout,
  TipoLogin,
  UsuarioAutenticado,
} from "../index.js";
import { Rota } from "./main.js";

export const payloadLoginSauSchema: ZodType<PayloadLoginSau> = z.object({
  tipo: z.literal("sau"),
  rota: z.string(),
  ticket: z.string(),
});

export const payloadLoginOtpSchema: ZodType<PayloadLoginOtp> = z.object({
  tipo: z.literal("otp"),
  email: z.string(),
  codigo: z.string(),
});

export const payloadLoginSchema = z.union([payloadLoginSauSchema, payloadLoginOtpSchema]);

export const tipoLoginSchema: ZodType<TipoLogin> = z.union([z.literal("sau"), z.literal("otp")]);

const usuarioAutenticadoSchema: ZodType<UsuarioAutenticado> = z.object({
  id: z.number(),
  perfil: z.enum(Perfil),
  nome: z.string(),
  email: z.string(),
  ipAddress: z.string().nullable(),
  tipoLogin: tipoLoginSchema,
  rotaInicial: z.string(),
});

export const retornoCredenciaisSchema: ZodType<RetornoCredenciais> = z.object({
  credenciais: usuarioAutenticadoSchema.nullable(),
});

export const retornoLoginSchema: ZodType<RetornoLogin> = z.object({
  credenciais: usuarioAutenticadoSchema,
});

export const retornoLogoutSchema: ZodType<RetornoLogout> = z.object({
  message: z.string(),
});

export const endpointsAuth = {
  lerCredenciais: {
    uri: "/auth/credenciais",
    metodo: "get",
    tipoPayload: z.undefined(),
    tipoResposta: retornoCredenciaisSchema,
  },
  login: {
    uri: "/auth/login",
    metodo: "post",
    tipoPayload: payloadLoginSchema,
    tipoResposta: retornoLoginSchema,
  },
  logout: {
    uri: "/auth/logout",
    metodo: "post",
    tipoPayload: z.undefined(),
    tipoResposta: retornoLogoutSchema,
  },
  otp: {
    uri: "/auth/otp",
    metodo: "post",
    tipoPayload: z.object({ email: z.string() }),
    tipoResposta: z.object({
      sucesso: z.boolean(),
    }),
  },
  recuperarEmail: {
    uri: "/auth/recupera-email",
    metodo: "post",
    tipoPayload: z.object({
      cpf: z.string(),
      cartao: z.string(),
    }),
    tipoResposta: z.object({
      email: z.string(),
    }),
  },
} as const satisfies Record<string, Rota>;
