import { z, ZodType } from "zod";
import { endpointsAuth } from "./auth.js";
import { endpointsDocumentoFiscalItem } from "./documento-fiscal-item.js";
import { endpointsDocumentoFiscal } from "./documento-fiscal.js";
import { endpointsFimLimiteCartao } from "./fim-limite-cartao.js";
import { endpointsItemFiscal } from "./item-fiscal.js";
import { endpointsLimiteCartao } from "./limite-cartao.js";
import { endpointsMovimentacao } from "./movimentacao.js";
import { endpointsPortador } from "./portador.js";
import { endpointsTelemetria } from "./telemetria.js";
import { endpointsUnidadeAdministrativa } from "./unidade-administrativa.js";
import { endpointsUnidadeGestora } from "./unidade-gestora.js";

export type MetodoHttp = "get" | "post" | "put" | "delete" | "patch" | "options" | "head";

export interface Rota<
  TUri extends string = string,
  TMetodo extends MetodoHttp = MetodoHttp,
  TPayload extends ZodType = ZodType,
  TResposta extends ZodType = ZodType,
> {
  uri: TUri;
  metodo: TMetodo;
  tipoPayload: TPayload;
  tipoResposta: TResposta;
}

export type RespostaRota<T extends Rota> = Promise<z.infer<T["tipoResposta"]>>;

export const endpoints = {
  ...endpointsAuth,
  ...endpointsItemFiscal,
  ...endpointsLimiteCartao,
  ...endpointsMovimentacao,
  ...endpointsDocumentoFiscal,
  ...endpointsDocumentoFiscalItem,
  ...endpointsPortador,
  ...endpointsTelemetria,
  ...endpointsUnidadeAdministrativa,
  ...endpointsUnidadeGestora,
  ...endpointsFimLimiteCartao,
} as const satisfies Record<string, Rota>;
