import { exec } from "@ghabriel/z-script";
import { addNamespacedCommand, print, processNamespacedCommands, syncDependencies } from "./helpers";
import { FRONTEND_FOLDER } from "./settings";

export function syncFrontendDependencies() {
  syncDependencies(
    `${FRONTEND_FOLDER}/package.json`,
    `${FRONTEND_FOLDER}/.latest_build`,
    `${FRONTEND_FOLDER}/node_modules`,
    "npm run install:frontend",
  );
}

export function addFrontendCommands() {
  addNamespacedCommand("frontend", "main", {
    description: `Inicia o Frontend para desenvolvimento.`,
    execute: async () => {
      syncFrontendDependencies();

      const command = `npm --prefix ${FRONTEND_FOLDER} start`;
      print.notice(`➜  Inicializando Frontend...`);
      print.command(command);
      exec(command);
    },
  });

  processNamespacedCommands("frontend");
}
