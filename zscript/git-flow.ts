import { AsyncShell, exec, Format, Git, runCommand, Shell } from "@ghabriel/z-script";
import * as fs from "fs";
import { addCommand, getChangelogTagList, print, referToParameter } from "./helpers";
import { CHANGELOG_PATH, CHANGELOG_TAG_PREFIX, GIT_URL, STYLE_DANGER, STYLE_NOTICE, STYLE_RESET } from "./settings";

interface BranchType {
  commandName: string;
  displayName: string;
  baseBranch: string;
  targetBranch: string;
  branchPrefix: string;
  requiresTag: boolean;
  updateKind: SemanticVersioningUpdateKind;
}

enum SemanticVersioningUpdateKind {
  UpdateMajor,
  UpdateMinor,
  UpdatePatch,
}

const SPECIAL_BRANCHES: BranchType[] = [
  {
    commandName: "feature",
    displayName: "Feature",
    baseBranch: "develop",
    targetBranch: "develop",
    branchPrefix: "feature/",
    requiresTag: false,
    updateKind: SemanticVersioningUpdateKind.UpdateMinor,
  },
  {
    commandName: "release",
    displayName: "Release",
    baseBranch: "develop",
    targetBranch: "master",
    branchPrefix: "release/",
    requiresTag: true,
    updateKind: SemanticVersioningUpdateKind.UpdateMinor,
  },
  {
    commandName: "hotfix",
    displayName: "Hotfix",
    baseBranch: "master",
    targetBranch: "master",
    branchPrefix: "hotfix/",
    requiresTag: true,
    updateKind: SemanticVersioningUpdateKind.UpdatePatch,
  },
];

function getCurrentBranch(): string {
  return Shell.getStdout('git branch | grep \\* | cut -d " " -f2 | sed "s/\x1b[[0-9;]*m//g"').trim();
}

function getLatestTags(amount: number): string[] {
  return Shell.getStdout(
    `git for-each-ref --sort=creatordate --format "%(refname)" refs/tags | tail -n ${amount} | tac`,
  )
    .replace(/refs\/tags\//g, "")
    .trim()
    .split("\n");
}

function getSuggestedTag(branchType: BranchType): string {
  const latestTag = getLatestTags(1)[0];
  const numbers = latestTag.match(/[0-9]+/g)?.map(n => parseInt(n)) ?? [];

  switch (branchType.updateKind) {
    case SemanticVersioningUpdateKind.UpdateMajor:
      numbers[numbers.length - 3]++;
      numbers[numbers.length - 2] = 0;
      numbers[numbers.length - 1] = 0;
      break;
    case SemanticVersioningUpdateKind.UpdateMinor:
      numbers[numbers.length - 2]++;
      numbers[numbers.length - 1] = 0;
      break;
    case SemanticVersioningUpdateKind.UpdatePatch:
      numbers[numbers.length - 1]++;
      break;
  }

  let i = 0;
  return latestTag.replace(/[0-9]+/g, () => numbers[i++].toString());
}

async function hasUncommittedChanges(): Promise<boolean> {
  const command = "git status --porcelain --untracked-files=no";
  const { stdout } = await AsyncShell.execute(command);

  return stdout.length > 0;
}

function checkChangelogChanges(changelogPath: string, tag: string): void {
  const expectedContent = `### ${tag}`;
  const changelogContent = fs.readFileSync(changelogPath).toString();

  if (!changelogContent.includes(expectedContent)) {
    print.error(
      `O arquivo ${changelogPath} não foi atualizado. ` +
        `Adicione uma entrada para a versão atual com a seguinte linha, e depois descreva as ` +
        `alterações realizadas:\n${expectedContent}`,
    );
    process.exit(1);
  }
}

async function processBranchFinish(branchType: BranchType, currentBranch: string): Promise<void> {
  if (await hasUncommittedChanges()) {
    print.error('Você possui alterações locais. Faça um commit ou stash antes do "zsc finish".');
    process.exit(1);
  }

  if (branchType.requiresTag) {
    print.notice("➜  Atualizando tags locais");
    exec(`git pull --tags`);

    const changelogTagList = getChangelogTagList();

    if (changelogTagList.length === 0 || testTagExists(changelogTagList[0])) {
      const latestTags = getLatestTags(3);
      const suggestedTag = getSuggestedTag(branchType);

      print.error(`O arquivo ${CHANGELOG_PATH} não foi atualizado, o que é obrigatório para este tipo de branch.`);

      console.log(
        "As três últimas tags deste projeto são:\n" +
          latestTags
            .map(tag => "    " + tag)
            .reverse()
            .join("\n"),
      );

      print.notice(`➜  Sugestão de tag: ${suggestedTag}`);

      console.log("Adicione uma entrada para a versão atual com a seguinte linha, alterando a tag caso necessário:");

      console.log(CHANGELOG_TAG_PREFIX + suggestedTag);
      process.exit(1);
    }
  }

  print.notice("➜  Realizando o push dos commits locais");
  Git.push();

  const { targetBranch, displayName } = branchType;
  print.notice(`➜  Abrindo URL de Merge Request de ${displayName} (${currentBranch} → ${targetBranch})`);
  exec(
    `xdg-open "${GIT_URL}/merge_requests/new?merge_request[source_branch]=${currentBranch}&merge_request[target_branch]=${targetBranch}"`,
  );
}

function testTagExists(tag: string): boolean {
  return Shell.getStdout(`git ls-remote --tags origin ${tag}`).trim().length > 0;
}

export function addGitFlowCommands() {
  for (const data of SPECIAL_BRANCHES) {
    addCommand(data.commandName, {
      description: `Atualiza automaticamente o branch "${data.baseBranch}" e cria um novo branch do tipo ${
        data.displayName
      } a partir dele, com o ${referToParameter("nome")} especificado.`,
      parameters: [{ name: "", optional: false, valueName: "nome" }],
      execute: async args => {
        if (args.length === 0) {
          print.error("Nome do branch ausente");
          return process.exit(1);
        }

        const newBranchName = data.branchPrefix + args[0];

        if (args[0] === "finish") {
          const answer = await Shell.readInput(
            `Você está prestes a criar um branch chamado "${newBranchName}".\n` +
              'Para finalizar o branch atual, rode somente "zsc finish".\n' +
              `Continuar com a criação do branch ${newBranchName}? [Y/n] `,
          );

          if (answer.toLowerCase() === "n") {
            process.exit(0);
          }
        }

        print.notice(`Checkout para branch ${data.baseBranch}`);
        Git.checkout(data.baseBranch);
        print.notice("Atualizando branch");
        Git.pull();
        print.notice(`Checkout para nova branch ${newBranchName}`);
        Git.createBranch(newBranchName);
      },
    });
  }

  addCommand("finish", {
    description:
      'Realiza um "push" dos commits locais, verifica se o CHANGELOG foi atualizado ' +
      "(caso o branch atual seja uma hotfix ou release) e então abre a página de Merge " +
      "Requests do projeto, onde um novo pode ser criado.",
    parameters: [],
    execute: async args => {
      const currentBranch = getCurrentBranch();

      for (const data of SPECIAL_BRANCHES) {
        if (currentBranch.startsWith(data.branchPrefix)) {
          await processBranchFinish(data, currentBranch);
          return;
        }
      }

      print.error("O tipo do branch atual não pode ser determinado.");
      process.exit(1);
    },
  });

  addCommand("clear-local-branches", {
    description: "Elimina branches locais cujo remote tenha sido excluído.",
    execute: async () => {
      print.notice("Atualizando referências a branches remotos");
      Git.fetchRemoteBranches();

      const expiredBranches = Git.getExpiredBranches();

      if (expiredBranches.length === 0) {
        return print.success("Não há branches locais cujo remote associado tenha sido excluído.");
      }

      print.notice("Os seguintes branches locais não possuem um remote associado:");
      console.log(expiredBranches.join("\n"));

      const answer = await Shell.readInput(`${STYLE_NOTICE}Deseja apagá-los? [Y/n] ${STYLE_RESET}`);
      if (answer.toLowerCase() === "n") {
        process.exit(0);
      }

      for (const branch of expiredBranches) {
        print.command(`git branch -d ${branch}`);

        try {
          Git.deleteLocalBranch(branch);
        } catch (e) {
          const answer = await Shell.readInput(
            `${STYLE_DANGER}Ocorreu um erro ao apagar o branch "${branch}". ` +
              `Deseja forçar sua exclusão? [y/N] ${STYLE_RESET}`,
          );

          if (answer.toLowerCase() === "y") {
            print.command(`git branch -D ${branch}`);
            Git.deleteLocalBranch(branch, true);
          } else {
            print.notice(`Branch "${branch}" ignorado. Prosseguindo...`);
          }
        }
      }
    },
  });

  addCommand("clear-branches", {
    description:
      "Sincroniza os branches locais com os remotos e elimina branches desnecessários (branches locais com um remote já excluído e branches remotos já unidos à develop).",
    execute: async () => {
      runCommand("clear-local-branches");
      print.notice("Criando um branch local para cada branch remoto");

      const remoteBranches = Git.getRemoteBranches()
        .filter(b => b.startsWith("origin/"))
        .filter(b => !b.startsWith("origin/HEAD") && !b.startsWith("origin/master"));
      for (const remoteBranch of remoteBranches) {
        print.command(remoteBranch.replace("origin/", ""));
        try {
          Git.createTrackingBranchFor(remoteBranch);
        } catch {}
      }

      print.notice("Atualizando branches locais");
      for (const remoteBranch of remoteBranches.filter(b => !b.startsWith("origin/develop"))) {
        const unprefixedBranch = remoteBranch.replace("origin/", "");
        print.command(unprefixedBranch);
        Git.checkout(unprefixedBranch);
        Git.pull();
      }

      print.notice("Checkout para branch develop");
      Git.checkout("develop");
      print.notice("Atualizando branch");
      Git.pull();

      const mergedBranches = Git.getMergedBranches().filter(b => b !== "develop" && b !== "master");
      if (mergedBranches.length === 0) {
        print.success("Não há branches remotos já unidos à develop.");
        return;
      }

      print.notice("Os seguintes branches remotos já foram unidos à develop");
      console.log(mergedBranches.join("\n"));

      Format.print("Deseja ", STYLE_NOTICE);
      Format.print("apagá-los permanentemente? [Y/n] ", STYLE_DANGER);

      const answer = await Shell.readInput(
        `${STYLE_NOTICE}Deseja ${STYLE_RESET}` +
          `${STYLE_DANGER}apagá-los permanentemente? ${STYLE_RESET}` +
          `${STYLE_NOTICE}[Y/n] ${STYLE_RESET}`,
      );
      if (answer.toLowerCase() === "n") {
        process.exit(0);
      }

      for (const branch of mergedBranches) {
        print.command(`git push --delete origin ${branch}`);
        Git.deleteRemoteBranch(branch);
      }

      runCommand("clear-local-branches");
    },
  });
}
