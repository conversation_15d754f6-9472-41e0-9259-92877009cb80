import { AsyncShell, exec, run } from "@ghabriel/z-script";
import { addApiCommands, syncApiDependencies } from "./api";
import { addCICDCommands } from "./cicd";
import { addFrontendCommands, syncFrontendDependencies } from "./frontend";
import { addGitFlowCommands } from "./git-flow";
import {
  CommandOptions,
  addCommand,
  getNamespacedCommands,
  getRegisteredCommands,
  print,
  syncDependencies,
} from "./helpers";
import { addMigrationCommands } from "./migrations";
import { FRONTEND_FOLDER, STYLE_COMMAND, STYLE_PARAMETER, STYLE_RESET, STYLE_VERSION } from "./settings";

interface DependencyVersions {
  react: string;
  node: string;
  npm: string;
  typescript: string;
}

async function getDependencyVersions(): Promise<DependencyVersions> {
  const dependencyList = await AsyncShell.execute(`npm --prefix frontend list`);
  const react = getPackageVersion("react", dependencyList.stdout);
  const node = await parseVersionCommand("node --version");
  const npm = await parseVersionCommand("npm --version");
  const typescript = getPackageVersion("typescript", dependencyList.stdout);

  return { react, node, npm, typescript };
}

export function getPackageVersion(packageName: string, dependencyList: string): string {
  const regex = new RegExp(` ${packageName}@(.*)\n`);

  return dependencyList.match(regex)?.[1] ?? "N/A";
}

export async function parseVersionCommand(command: string): Promise<string> {
  const output = await AsyncShell.execute("node --version");

  return output.stdout.trim().replace(/[^0-9.]/g, "");
}

addCommand("main", {
  description: "Inicia o projeto para desenvolvimento (API e Frontend).",
  execute: () => {
    syncDependencies(
      "package.json",
      ".latest_build",
      "node_modules",
      `npm install && touch ${FRONTEND_FOLDER}/.latest_build`,
    );

    syncFrontendDependencies();
    syncApiDependencies();
    exec("npm start");
  },
});

addCommand("help", {
  description: "Lista os comandos disponíveis e suas descrições.",
  execute: () => {
    function getCommandSignatureParameters(options: CommandOptions): string[] {
      const result: string[] = [];

      for (const parameter of options.parameters ?? []) {
        const parts: string[] = [];

        if (parameter.name.length > 0) {
          parts.push(parameter.name);
        }

        if (parameter.valueName !== undefined) {
          parts.push(STYLE_PARAMETER + parameter.valueName + STYLE_RESET);
        }

        const baseText = parts.join(" ");
        result.push(parameter.optional ? `[${baseText}]` : baseText);
      }

      return result;
    }

    interface Section {
      commandSignature: string;
      parameterSignature: string;
      description: string;
    }

    const commands = getRegisteredCommands();
    const sections: Section[] = [];

    commands.forEach((options, commandName) => {
      const commandSignature = commandName === "main" ? "zsc" : `zsc ${commandName}`;
      const signatureParams = getCommandSignatureParameters(options);
      const parameterSignature = signatureParams.map(p => " " + p).join("");

      sections.push({
        commandSignature,
        parameterSignature,
        description: options.description,
      });
    });

    const namespacedCommands = getNamespacedCommands();

    namespacedCommands.forEach((commands, namespace) => {
      commands.forEach((options, commandName) => {
        const commandSignature = commandName === "main" ? `zsc ${namespace}` : `zsc ${namespace} ${commandName}`;
        const signatureParams = getCommandSignatureParameters(options);
        const parameterSignature = signatureParams.map(p => " " + p).join("");

        sections.push({
          commandSignature,
          parameterSignature,
          description: options.description,
        });
      });
    });

    sections.sort((a, b) => a.commandSignature.localeCompare(b.commandSignature));
    print.notice("➜  Comandos disponíveis:");

    for (const section of sections) {
      console.log(STYLE_COMMAND + section.commandSignature + STYLE_RESET + section.parameterSignature);
      console.log("    " + section.description + "\n");
    }
  },
});

addCommand("version", {
  description: "Exibe as versões das principais dependências do projeto.",
  execute: async () => {
    const versions = await getDependencyVersions();

    console.log(`React: ${STYLE_VERSION}${versions.react}${STYLE_RESET}`);
    console.log(`Node: ${STYLE_VERSION}${versions.node}${STYLE_RESET}`);
    console.log(`NPM: ${STYLE_VERSION}${versions.npm}${STYLE_RESET}`);
    console.log(`TypeScript: ${STYLE_VERSION}${versions.typescript}${STYLE_RESET}`);
  },
});

function main() {
  addFrontendCommands();
  addApiCommands();
  addCICDCommands();
  addGitFlowCommands();
  addMigrationCommands();

  run();
}

main();
