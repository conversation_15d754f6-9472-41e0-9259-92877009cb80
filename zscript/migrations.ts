import { exec } from "@ghabriel/z-script";
import { addNamespacedCommand, print, processNamespacedCommands, referToParameter } from "./helpers";
import { API_FOLDER, API_MIGRATIONS_FOLDER, API_TYPEORM_DATASOURCE_PATH } from "./settings";

function formatarUltimaMigration(): void {
  print.notice(`➜  Formatando migration...`);
  const before = new Date();
  exec(
    `find ${API_FOLDER}/${API_MIGRATIONS_FOLDER} | sort | tail -n 1 | xargs npx --silent --prefix api prettier --write`,
  );
  exec(`find ${API_FOLDER}/${API_MIGRATIONS_FOLDER} | sort | tail -n 1 | xargs npx --silent --prefix api eslint --fix`);
  const after = new Date();
  const elapsedTime = after.getTime() - before.getTime();
  print.success(`✔  Migration gerada com sucesso (${elapsedTime} ms).\n`);
}

export function addMigrationCommands() {
  addNamespacedCommand("migrate", "main", {
    description: "Executa as migrations pendentes da API.",
    execute: () => {
      print.notice(`➜  Compilando API...`);
      exec(`npm --silent --prefix api run build`);
      print.notice(`➜  Executando migrations...`);
      exec(`cd ${API_FOLDER} && npx --silent typeorm migration:run -d ${API_TYPEORM_DATASOURCE_PATH}`);
    },
  });

  addNamespacedCommand("migrate", "status", {
    description: "Exibe o status das migrations da API.",
    execute: () => {
      print.notice(`➜  Compilando API...`);
      exec(`npm --silent --prefix api run build`);
      print.notice(`➜  Calculando migrations...`);
      exec(`cd ${API_FOLDER} && npx --silent typeorm migration:show -d ${API_TYPEORM_DATASOURCE_PATH}`);
    },
  });

  addNamespacedCommand("migrate", "rollback", {
    description: "Desfaz o último batch de migrations da API.",
    execute: () => {
      print.notice(`➜  Compilando API...`);
      exec(`npm --silent --prefix api run build`);
      print.notice(`➜  Executando rollback...`);
      exec(`cd ${API_FOLDER} && npx --silent typeorm migration:revert -d ${API_TYPEORM_DATASOURCE_PATH}`);
    },
  });

  addNamespacedCommand("migrate", "new", {
    description: `Cria uma migration com o ${referToParameter("nome")} especificado.`,
    parameters: [{ name: "", optional: false, valueName: "nome" }],
    execute: args => {
      if (args.length === 0) {
        print.error("Nome de migration não fornecido");
        return process.exit(1);
      }

      print.notice(`➜  Compilando API...`);
      exec(`npm --silent --prefix api run build`);
      print.notice(`➜  Criando migration...`);
      exec(`cd ${API_FOLDER} && npx --silent typeorm migration:create ${API_MIGRATIONS_FOLDER}/${args[0]}`);
      formatarUltimaMigration();
    },
  });

  addNamespacedCommand("migrate", "generate", {
    description: `Cria uma migration automática do TypeORM com o ${referToParameter("nome")} especificado.`,
    parameters: [{ name: "", optional: false, valueName: "nome" }],
    execute: args => {
      if (args.length === 0) {
        print.error("Nome de migration não fornecido");
        return process.exit(1);
      }

      print.notice(`➜  Compilando API...`);
      exec(`npm --silent --prefix api run build`);
      print.notice(`➜  Gerando migration automática...`);
      exec(
        `cd ${API_FOLDER} && ` +
          `npx --silent typeorm migration:generate -d ${API_TYPEORM_DATASOURCE_PATH} ${API_MIGRATIONS_FOLDER}/${args[0]}`,
      );

      formatarUltimaMigration();
    },
  });

  processNamespacedCommands("migrate");
}
