import { addNamespacedCommand, getChangelogTagList, print, processNamespacedCommands } from "./helpers";
import { CHANGELOG_PATH } from "./settings";

export function addCICDCommands() {
  addNamespacedCommand("ci", "create-tag", {
    description: "Cria uma tag no repositório remoto correspondente à versão atual, segundo o CHANGELOG.md.",
    parameters: [
      { name: "", optional: false, valueName: "tokenDeAcesso" },
      { name: "", optional: false, valueName: "hostDoServidor" },
      { name: "", optional: false, valueName: "caminhoDoProjeto" },
    ],
    execute: async args => {
      if (args.length !== 3) {
        console.error("Uso: zsc ci create-tag <token de acesso> <host do servidor> <caminho do projeto>");
        process.exit(1);
      }

      const [accessToken, serverHost, projectPath] = args;
      const tagList = getChangelogTagList();

      if (tagList.length === 0) {
        print.error(`Nenhuma tag encontrada no arquivo ${CHANGELOG_PATH}`);
        process.exit(1);
      }

      const tag = tagList[0];
      print.notice(`➜  Criando tag ${tag}`);
      print.command("git remote remove origin");
      print.command(`git remote add origin http://oauth2:${accessToken}@${serverHost}/${projectPath}`);
      print.command(`git tag ${tag}`);
      print.command(`git push origin ${tag}`);
    },
  });

  processNamespacedCommands("ci");
}
