import { Color, Format } from "@ghabriel/z-script";

export const API_FOLDER = "api";
export const API_MIGRATIONS_FOLDER = "src/migrations";
export const API_TYPEORM_DATASOURCE_PATH = "dist/src/TypeOrmDataSource.js";
export const FRONTEND_FOLDER = "frontend";
export const CHANGELOG_PATH = "CHANGELOG.md";
export const CHANGELOG_TAG_PREFIX = "### ";
export const CHANGELOG_TAG_PREFIX_REGEX = "(?:^|\n)" + CHANGELOG_TAG_PREFIX;
export const CHANGELOG_TAG_LINE_REGEX = CHANGELOG_TAG_PREFIX_REGEX + "([A-Za-z0-9.]+)";
export const GIT_URL = "http://git.intranet.ciasc.gov.br/sef/cpesc-2";

export const STYLE_PARAMETER = Format.foreground(Color.Green) + Format.underline();
export const STYLE_ERROR = Format.foreground(Color.Red) + Format.bold();
export const STYLE_WARNING = Format.foreground(Color.LightYellow);
export const STYLE_NOTICE = Format.foreground(Color.Yellow) + Format.bold();
export const STYLE_SUCCESS = Format.foreground(Color.Green) + Format.bold();
export const STYLE_DANGER = Format.foreground(Color.Red) + Format.bold();
export const STYLE_COMMAND = Format.foreground(Color.Yellow);
export const STYLE_VERSION = Format.foreground(Color.Yellow);
export const STYLE_RESET = Format.reset();
