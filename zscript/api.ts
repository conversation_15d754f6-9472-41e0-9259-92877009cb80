import { exec } from "@ghabriel/z-script";
import { addNamespacedCommand, print, processNamespacedCommands, syncDependencies } from "./helpers";
import { API_FOLDER } from "./settings";

export function syncApiDependencies() {
  syncDependencies(
    `${API_FOLDER}/package.json`,
    `${API_FOLDER}/.latest_build`,
    `${API_FOLDER}/node_modules`,
    "npm run install:api",
  );
}

export function addApiCommands() {
  addNamespacedCommand("api", "main", {
    description: `Inicia a API para desenvolvimento.`,
    execute: async () => {
      syncApiDependencies();
    
      const apiCommand = `npm --prefix ${API_FOLDER} start`;
      print.notice(`➜  Inicializando API...`);
      print.command(apiCommand);
      exec(apiCommand);
    },
  });

  processNamespacedCommands("api");
}
