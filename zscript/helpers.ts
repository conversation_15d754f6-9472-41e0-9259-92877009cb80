import { Format, Shell, addCommand as addZscCommand } from "@ghabriel/z-script";
import { ExecutionFunction } from "@ghabriel/z-script/lib/core/Command";
import * as fs from "fs";
import {
  CHANGELOG_PATH,
  CHANGELOG_TAG_LINE_REGEX,
  CHANGELOG_TAG_PREFIX_REGEX,
  STYLE_COMMAND,
  STYLE_ERROR,
  STYLE_NOTICE,
  STYLE_PARAMETER,
  STYLE_RESET,
  STYLE_SUCCESS,
  STYLE_WARNING,
} from "./settings";

export const print = {
  success(message: string): void {
    Format.print(message + "\n", STYLE_SUCCESS);
  },

  error(message: string): void {
    Format.print(message + "\n", STYLE_ERROR);
  },

  warning(message: string): void {
    Format.print(message + "\n", STYLE_WARNING);
  },

  notice(message: string): void {
    Format.print(message + "\n", STYLE_NOTICE);
  },

  command(message: string): void {
    Format.print(message + "\n", STY<PERSON>_COMMAND);
  },
};

export interface CommandOptions {
  description: string;
  parameters?: CommandParameter[];
  execute: ExecutionFunction;
}

export interface CommandParameter {
  name: string;
  optional: boolean;
  valueName?: string;
}

const registeredCommands = new Map<string, CommandOptions>();
const namespacedCommands = new Map<string, Map<string, CommandOptions>>();

export function addCommand(name: string, options: CommandOptions): void {
  addZscCommand(name, options.execute);
  registeredCommands.set(name, options);
}

export function addNamespacedCommand(namespace: string, name: string, options: CommandOptions): void {
  const commands = namespacedCommands.get(namespace) ?? new Map<string, CommandOptions>();
  commands.set(name, options);
  namespacedCommands.set(namespace, commands);
}

export function processNamespacedCommands(namespace: string): void {
  const commands = namespacedCommands.get(namespace) ?? new Map<string, CommandOptions>();

  addZscCommand(namespace, (_, context) => {
    commands.forEach((options, commandName) => {
      context.addCommand(commandName, options.execute);
    });

    context.run();
  });
}

export function getRegisteredCommands(): ReadonlyMap<string, CommandOptions> {
  return registeredCommands;
}

export function getNamespacedCommands(): ReadonlyMap<string, Map<string, CommandOptions>> {
  return namespacedCommands;
}

export function referToCommand(commandName: string): string {
  return STYLE_COMMAND + commandName + STYLE_RESET;
}

export function referToParameter(parameterName: string): string {
  return STYLE_PARAMETER + parameterName + STYLE_RESET;
}

export function syncDependencies(
  dependenciesFile: string,
  latestBuildFile: string,
  generatedDirectoryPath: string,
  updateCommand: string,
): void {
  print.notice(`➜  Verificando se ${generatedDirectoryPath} está atualizado...`);

  if (isMoreRecentThan(dependenciesFile, latestBuildFile) || !Shell.fileExists(generatedDirectoryPath)) {
    print.warning(`⚠  ${generatedDirectoryPath} está desatualizado. Atualizando...`);
    const before = new Date();
    print.command(updateCommand);
    Shell.execute(updateCommand);
    const updateLatestBuildFileCommand = `touch ${latestBuildFile}`;
    print.command(updateLatestBuildFileCommand);
    Shell.execute(updateLatestBuildFileCommand);
    const after = new Date();
    const elapsedTime = after.getTime() - before.getTime();
    print.success(`✔  ${generatedDirectoryPath} atualizado com sucesso (${elapsedTime} ms).\n`);
  } else {
    print.success(`✔  ${generatedDirectoryPath} está atualizado.\n`);
  }
}

function isMoreRecentThan(firstFile: string, secondFile: string): boolean {
  if (!Shell.fileExists(firstFile) || !Shell.fileExists(secondFile)) {
    return true;
  }

  const firstFileTimestamp = Shell.getRecursiveModificationTime(firstFile);
  const secondFileTimestamp = Shell.getRecursiveModificationTime(secondFile);

  return firstFileTimestamp > secondFileTimestamp;
}

export function getChangelogTagList(): string[] {
  return (
    fs
      .readFileSync(CHANGELOG_PATH)
      .toString()
      .match(new RegExp(CHANGELOG_TAG_LINE_REGEX, "g"))
      ?.map(tag => tag.replace(new RegExp(CHANGELOG_TAG_PREFIX_REGEX), "")) ?? []
  );
}
